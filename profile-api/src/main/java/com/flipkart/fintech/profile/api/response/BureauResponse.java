package com.flipkart.fintech.profile.api.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.UUID;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BureauResponse {

    private String id;

    @JsonProperty("rawData")
    private String rawData;
    @JsonProperty("errorMsg")
    private String errorMsg;
    @JsonProperty("stgOneHitId")
    private String stgOneHitId;
    @JsonProperty("stgTwoHitId")
    private String stgTwoHitId;
    @JsonProperty("userId")
    private Integer experianUserId;

    public BureauResponse() {
        this.id = generateHashedId("ECR");
    }

    private String generateHashedId(String prefix) {
        String uuid = UUID.randomUUID().toString().toUpperCase().replace("-", "");
        int maxLength = 15 - prefix.length();
        if (uuid.length() > maxLength) {
            uuid = uuid.substring(0, maxLength);
        }
        return prefix.toUpperCase() + uuid;
    }
}
