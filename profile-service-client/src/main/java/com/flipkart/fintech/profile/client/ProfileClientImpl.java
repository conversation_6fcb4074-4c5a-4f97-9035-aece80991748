package com.flipkart.fintech.profile.client;

import com.flipkart.fintech.profile.common.DynamicConfigHelper;
import com.flipkart.fintech.profile.model.Profile;
import com.flipkart.fintech.profile.request.BureauDataRequest;
import com.flipkart.fintech.profile.request.BureauDataRequestSm;
import com.flipkart.fintech.profile.request.CrossMerchantConsentRequest;
import com.flipkart.fintech.profile.response.*;
import com.flipkart.kloud.config.DynamicBucket;
import com.google.inject.Inject;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;
import org.glassfish.jersey.client.ClientProperties;

import javax.ws.rs.client.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

@CustomLog
public class ProfileClientImpl implements ProfileClient {

    private final WebTarget webTarget;
    private final Client client;
    private final String clientId;
    private final static String IS_PROFILE_V2_ENABLED = "is_profile_v2_client_enabled";

    @Inject
    public ProfileClientImpl(ProfileClientConfiguration profileClientConfiguration) {
        if (StringUtils.isBlank(profileClientConfiguration.getUrl())) {
            throw new IllegalArgumentException("Endpoint can't be blank");
        }
        client = ClientBuilder.newClient();
        client.property(ClientProperties.CONNECT_TIMEOUT, profileClientConfiguration.getConnectTimeout() != 0 ? profileClientConfiguration.getConnectTimeout() : 30000);
        client.property(ClientProperties.CONNECT_TIMEOUT, profileClientConfiguration.getReadTimeout() != 0 ? profileClientConfiguration.getReadTimeout() : 30000);
        this.webTarget = client.target(profileClientConfiguration.getUrl());
        this.clientId = profileClientConfiguration.getClient();

    }


    private String getProfileIdByUserId(String merchantUserId, String smUserId) {
        Response response = null;
        ProfileCRUDResponse profileServiceResponse = null;
        try {
            Invocation.Builder invocationBuilder = null;
            if (shouldUseV2API()) {
                invocationBuilder = webTarget.path(Constants.UrlPaths.GET_PROFILE_BY_USER_ID_PATH_V2)
                        .queryParam("merchantUserId", merchantUserId)
                        .queryParam("smUserId", smUserId)
                        .request(MediaType.APPLICATION_JSON);
            } else {
                String urlPath = String.format(Constants.UrlPaths.GET_PROFILE_BY_USER_ID_PATH, merchantUserId);
                invocationBuilder = webTarget.path(urlPath).
                        request(MediaType.APPLICATION_JSON);
            }

            response = invocationBuilder.get();
            if (response.getStatus() == 200) {
                profileServiceResponse = response.readEntity(ProfileCRUDResponse.class);
            } else {
                log.error("Profile does not exist, response code is {} ", response.getStatus());
                throw new RuntimeException();
            }
        } catch (Exception e) {
            log.error("Profile does not exist, response code is {} ", response.getStatus());
            throw new RuntimeException();
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return profileServiceResponse.getProfileId().toString();
    }

    @Override
    public ProfileDetailedResponse getProfile(String merchantUserId, String smUserId, boolean isUnmaskedData) {
        Response response = null;
        ProfileDetailedResponse profile = null;
        try {
            Invocation.Builder invocationBuilder = null;
            if (shouldUseV2API()) {
                invocationBuilder = webTarget.path(Constants.UrlPaths.GET_PROFILE_BY_USER_ID_PATH_V2)
                        .queryParam("merchantUserId", merchantUserId)
                        .queryParam("smUserId", smUserId)
                        .queryParam("isUnmaskedData", isUnmaskedData)
                        .request(MediaType.APPLICATION_JSON);
            } else {
                String urlPath = String.format(Constants.UrlPaths.GET_PROFILE_BY_USER_ID_PATH, merchantUserId);
                invocationBuilder = webTarget.path(urlPath)
                        .request(MediaType.APPLICATION_JSON);
            }
            response = invocationBuilder.get();
            if (response.getStatus() == 200) {
                profile = response.readEntity(ProfileDetailedResponse.class);
            }
        } catch (Exception e) {
            log.error("Profile does not exist, response code is {} ", response.getStatus());
            throw e;
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return profile;
    }

    @Override
    public BureauDataResponse getCreditScore(BureauDataRequest bureauDataRequest) {
        Response response = null;
        BureauDataResponse bureauDataResponse = null;
        try {
            Invocation.Builder invocationBuilder = null;
            if (shouldUseV2API()) {
                String urlPath = Constants.UrlPaths.GET_CREDIT_SCORE_PATH_V2;
                invocationBuilder = webTarget.path(urlPath)
                        .request(MediaType.APPLICATION_JSON);
            } else {
                String urlPath = Constants.UrlPaths.GET_CREDIT_SCORE_PATH;
                invocationBuilder = webTarget.path(urlPath)
                        .request(MediaType.APPLICATION_JSON);
            }
            response = invocationBuilder.post(Entity.json(bureauDataRequest));
            if (response.getStatus() == 200) {
                bureauDataResponse = response.readEntity(BureauDataResponse.class);
            } else {
                log.error("Bureau entity does not exist, response code is {} ", response.getStatus());
                throw new RuntimeException();
            }
        } catch (Exception e) {
            log.error("Error while getting credit score, response code is {} ", response.getStatus());
            throw new RuntimeException();
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return bureauDataResponse;
    }

    @Override
    public BureauDataResponse getRefreshCreditScore(String merchantUserId, String smUserId) {
        Response response = null;
        BureauDataResponse bureauDataResponse = null;
        try {
            Invocation.Builder invocationBuilder = null;
            if (shouldUseV2API()) {
                invocationBuilder = webTarget.path(Constants.UrlPaths.GET_REFRESH_CREDIT_SCORE_PATH_V2)
                        .queryParam("merchantUserId", merchantUserId)
                        .queryParam("smUserId", smUserId)
                        .request(MediaType.APPLICATION_JSON);
            } else {
                String urlPath = String.format(Constants.UrlPaths.GET_REFRESH_CREDIT_SCORE_PATH, merchantUserId);
                invocationBuilder = webTarget.path(urlPath)
                        .request(MediaType.APPLICATION_JSON);
            }
            response = invocationBuilder.get();
            if (response.getStatus() == 200) {
                bureauDataResponse = response.readEntity(BureauDataResponse.class);
            } else {
                log.error("Bureau entity does not exist, response code is {} ", response.getStatus());
                throw new RuntimeException();
            }
        } catch (Exception e) {
            log.error("Could not get refresh credit score, response code is {} ", response.getStatus());
            throw new RuntimeException();
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return bureauDataResponse;
    }

    @Override
    public BureauDataResponse getExistingCreditScore(String merchantUserId, String smUserId) {
        Response response = null;
        BureauDataResponse bureauDataResponse = null;
        try {

            Invocation.Builder invocationBuilder = null;
            if (shouldUseV2API()) {
                invocationBuilder = webTarget.path(Constants.UrlPaths.GET_EXISTING_CREDIT_SCORE_PATH_V2)
                        .queryParam("merchantUserId", merchantUserId)
                        .queryParam("smUserId", smUserId)
                        .request(MediaType.APPLICATION_JSON);
            } else {
                String urlPath = String.format(Constants.UrlPaths.GET_EXISTING_CREDIT_SCORE_PATH, merchantUserId);
                invocationBuilder = webTarget.path(urlPath)
                        .request(MediaType.APPLICATION_JSON);
            }
            response = invocationBuilder.get();
            if (response.getStatus() == 200) {
                bureauDataResponse = response.readEntity(BureauDataResponse.class);
            } else {
                log.error("Bureau entity does not exist, response code is {} ", response.getStatus());
                throw new RuntimeException();
            }
        } catch (Exception e) {
            log.error("Existing credit score does not exist, response code is {} ", response.getStatus());
            throw new RuntimeException();
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return bureauDataResponse;
    }

    @Override
    public BureauDataResponse getCreditScoreSm(BureauDataRequestSm bureauDataRequestSm) {
        Response response = null;
        BureauDataResponse bureauDataResponse = null;
        try {
            Invocation.Builder invocationBuilder = null;
            String urlPath = Constants.UrlPaths.GET_CREDIT_SCORE_SM_PATH;
            invocationBuilder = webTarget.path(urlPath)
                    .request(MediaType.APPLICATION_JSON);
            response = invocationBuilder.post(Entity.json(bureauDataRequestSm));
            if (response.getStatus() == 200) {
                bureauDataResponse = response.readEntity(BureauDataResponse.class);
            } else {
                log.error("Bureau entity does not exist, response code is {} ", response.getStatus());
                throw new RuntimeException();
            }
        } catch (Exception e) {
            log.error("Error while getting credit score, response code is {} ", response.getStatus());
            throw new RuntimeException();
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return bureauDataResponse;
    }

    @Override
    public BureauDataResponse getExistingCreditScoreSm(String smUserId) {
        Response response = null;
        BureauDataResponse bureauDataResponse = null;
        try {

            Invocation.Builder invocationBuilder = null;


            String urlPath = String.format(Constants.UrlPaths.GET_EXISTING_CREDIT_SCORE_SM_PATH, smUserId);
            invocationBuilder = webTarget.path(urlPath)
                    .request(MediaType.APPLICATION_JSON);

            response = invocationBuilder.get();
            if (response.getStatus() == 200) {
                bureauDataResponse = response.readEntity(BureauDataResponse.class);
            } else {
                log.error("Bureau entity does not exist, response code is {} ", response.getStatus());
                throw new RuntimeException();
            }
        } catch (Exception e) {
            log.error("Existing credit score does not exist, response code is {} ", response.getStatus());
            throw new RuntimeException();
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return bureauDataResponse;
    }

    @Override
    public BureauDataResponse getRefreshCreditScoreSm(String smUserId) {
        Response response = null;
        BureauDataResponse bureauDataResponse = null;
        try {
            Invocation.Builder invocationBuilder = null;

            String urlPath = String.format(Constants.UrlPaths.GET_REFRESH_CREDIT_SCORE_SM_PATH, smUserId);
            invocationBuilder = webTarget.path(urlPath)
                    .request(MediaType.APPLICATION_JSON);

            response = invocationBuilder.get();
            if (response.getStatus() == 200) {
                bureauDataResponse = response.readEntity(BureauDataResponse.class);
            } else {
                log.error("Bureau entity does not exist, response code is {} ", response.getStatus());
                throw new RuntimeException();
            }
        } catch (Exception e) {
            log.error("Could not get refresh credit score, response code is {} ", response.getStatus());
            throw new RuntimeException();
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return bureauDataResponse;
    }
    @Override
    public CrossMerchantConsentResponse putCrossMerchantConsent(CrossMerchantConsentRequest request){
        Response response = null;
        CrossMerchantConsentResponse crossMerchantConsentResponse = null;
        try {
            Invocation.Builder invocationBuilder = null;

            invocationBuilder = webTarget.path(Constants.UrlPaths.PUT_CROSS_MERCHANT_CONSENT)
                    .request(MediaType.APPLICATION_JSON);

            response = invocationBuilder.put(Entity.json(request));
            if (response.getStatus() == 200) {
                crossMerchantConsentResponse = response.readEntity(CrossMerchantConsentResponse.class);
            } else {
                log.error("putCrossMerchantConsent error response code is {} ", response.getStatus());
                throw new RuntimeException();
            }
        } catch (Exception e) {
            log.error("Could not put  putCrossMerchantConsent response code is {} ", response.getStatus());
            throw new RuntimeException();
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return crossMerchantConsentResponse;
    }

    private Boolean shouldUseV2API() {
        return true;
    }

    @Override
    public ProfileBasicDetailResponse getBasicProfile(String smUserId) {
        Response response = null;
        ProfileBasicDetailResponse profile = null;
        try {
            Invocation.Builder invocationBuilder = null;
            if (shouldUseV2API()) {
                invocationBuilder = webTarget.path(Constants.UrlPaths.GET_BASIC_PROFILE_PATH_V2)
                        .queryParam("smUserId", smUserId)
                        .request(MediaType.APPLICATION_JSON);
            } else {
                String urlPath = String.format(Constants.UrlPaths.GET_BASIC_PROFILE_PATH, smUserId);
                invocationBuilder = webTarget.path(urlPath)
                        .request(MediaType.APPLICATION_JSON);
            }
            response = invocationBuilder.get();
            if (response.getStatus() == 200) {
                profile = response.readEntity(ProfileBasicDetailResponse.class);
            }
        } catch (Exception e) {
            log.error("Profile does not exist, response code is {} ", response.getStatus());
            throw new RuntimeException();
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return profile;
    }


}

