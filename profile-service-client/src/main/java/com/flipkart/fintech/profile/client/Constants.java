package com.flipkart.fintech.profile.client;
public class Constants {

    public class UrlPaths{
        public final static String GET_PROFILE_BY_USER_ID_PATH_V2 = "/pinaka/2/profile/getAll";
        public final static String GET_CREDIT_SCORE_PATH_V2 = "/pinaka/2/bureau/credit_score";
        public final static String GET_REFRESH_CREDIT_SCORE_PATH_V2 = "/pinaka/2/bureau/refresh/credit_score";
        public final static String GET_EXISTING_CREDIT_SCORE_PATH_V2 = "/pinaka/2/bureau/existing/credit_score";
        public final static String GET_BASIC_PROFILE_PATH_V2 = "/pinaka/2/profile/basic-profile";

        public final static String GET_PROFILE_BY_USER_ID_PATH = "/pinaka/1/profile/getAll/%s";
        public final static String GET_CREDIT_SCORE_PATH = "/pinaka/1/bureau/credit_score";
        public final static String GET_REFRESH_CREDIT_SCORE_PATH = "/pinaka/1/bureau/refresh/credit_score/%s";
        public final static String GET_EXISTING_CREDIT_SCORE_PATH = "/pinaka/1/bureau/existing/credit_score/%s";
        public final static String GET_BASIC_PROFILE_PATH = "/pinaka/2/profile/basic-profile/%s";

        public final static String GET_CREDIT_SCORE_SM_PATH = "/pinaka/2/bureau/credit_score/sm";
        public final static String GET_EXISTING_CREDIT_SCORE_SM_PATH = "/pinaka/2/bureau/existing/credit_score/sm/%s";
        public final static String GET_REFRESH_CREDIT_SCORE_SM_PATH = "/pinaka/2/bureau/refresh/credit_score/sm/%s";
        public final static String PUT_CROSS_MERCHANT_CONSENT = "/pinaka/2/bureau/cs/consent/update";
    }

}
