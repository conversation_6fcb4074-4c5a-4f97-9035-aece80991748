package com.flipkart.fintech.profile.client;

import com.flipkart.fintech.profile.request.BureauDataRequest;
import com.flipkart.fintech.profile.request.BureauDataRequestSm;
import com.flipkart.fintech.profile.request.CrossMerchantConsentRequest;
import com.flipkart.fintech.profile.response.BureauDataResponse;
import com.flipkart.fintech.profile.response.CrossMerchantConsentResponse;
import com.flipkart.fintech.profile.response.ProfileBasicDetailResponse;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;

public interface ProfileClient {
    ProfileDetailedResponse getProfile(String merchantUserId, String smUserId, boolean isUnmaskedData);
    BureauDataResponse getCreditScore(BureauDataRequest bureauDataRequest);
    BureauDataResponse getRefreshCreditScore(String merchantUserId, String smUserId);
    BureauDataResponse getExistingCreditScore(String merchantUserId, String smUserId);
    BureauDataResponse getCreditScoreSm(BureauDataRequestSm bureauDataRequestSm);
    BureauDataResponse getExistingCreditScoreSm(String smUserId);
    BureauDataResponse getRefreshCreditScoreSm(String smUserId);
    CrossMerchantConsentResponse putCrossMerchantConsent(CrossMerchantConsentRequest request);
    ProfileBasicDetailResponse getBasicProfile(String smUserId);
}
