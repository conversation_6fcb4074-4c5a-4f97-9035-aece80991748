CREATE
DATABASE IF NOT EXISTS profile;

USE
profile;


DROP TABLE IF EXISTS `basic_details`;
CREATE TABLE `basic_details`
(
    `profile_id`    bigint unsigned NOT NULL,
    `first_name`    varchar(100),
    `last_name`     varchar(100)       DEFAULT NULL,
    `gender`        varchar(1)         DEFAULT NULL,
    `dob`           varchar(50)        DEFAULT NULL,
    `profile_name`  varchar(100)       DEFAULT NULL,
    `creation_date` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `last_modified` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`profile_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;


DROP TABLE IF EXISTS `profile`;
CREATE TABLE `profile`
(
    `profile_id` bigint unsigned NOT NULL AUTO_INCREMENT,
    `user_id`    varchar(50)  NOT NULL,
    `pan`        varchar(100) NOT NULL,
    `sm_user_id` varchar(50)  DEFAULT NULL,
    PRIMARY KEY (`profile_id`),
    UNIQUE (`user_id`, `pan`),
    UNIQUE (`sm_user_id`, `pan`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

DROP TABLE IF EXISTS `address_details`;
CREATE TABLE `address_details`
(
    `address_id` bigint(20) NOT NULL AUTO_INCREMENT,
    `pincode`    int COLLATE utf8_unicode_ci NOT NULL,
    `profile_id` bigint unsigned COLLATE utf8_unicode_ci NOT NULL,
    `active`     ENUM('True', 'False') COLLATE utf8_unicode_ci,
    PRIMARY KEY (`profile_id`, `address_id`),
    UNIQUE KEY (`address_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

CREATE TABLE `employment_details`
(
    `profile_id`      bigint(20) unsigned NOT NULL,
    `employment_type` enum('Salaried','SelfEmployed') COLLATE utf8_unicode_ci DEFAULT NULL,
    PRIMARY KEY (`profile_id`),
    UNIQUE KEY `profile_id` (`profile_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci ROW_FORMAT=COMPRESSED;

CREATE TABLE `contact_details`
(
    `profile_id` bigint(20) unsigned NOT NULL,
    `type`       varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
    `value`      varchar(100) COLLATE utf8_unicode_ci DEFAULT NULL,
    PRIMARY KEY (`profile_id`),
    UNIQUE KEY `profile_id` (`profile_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;