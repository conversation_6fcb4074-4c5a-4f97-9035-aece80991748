package com.flipkart.fintech.profile.eventPulisher;

import com.flipkart.fintech.profile.common.PinakaMetricRegistry;
import com.flipkart.usercluster.com.google.common.util.concurrent.MoreExecutors;
import com.google.api.core.ApiFuture;
import com.google.api.core.ApiFutureCallback;
import com.google.api.core.ApiFutures;
import com.google.inject.Inject;
import lombok.CustomLog;

import java.util.List;
import com.supermoney.publisher.EventPublisher;


@CustomLog
public class BQEventPublisher {
    private final EventPublisher eventPublisher;

    @Inject
    public BQEventPublisher(EventPublisher eventPublisher){
        this.eventPublisher = eventPublisher;
    }

    public void publishEventOptional(Object event) throws Exception {
        String BQ_INGESTION_SUCCESS = "BQ_INGESTION_SUCCESS";
        String BQ_INGESTION_FAILED = "BQ_INGESTION_FAILED";
        try {
            List<ApiFuture<String>> futures = eventPublisher.publishEvent(event);
            for (ApiFuture<String> future : futures) {
                ApiFutures.addCallback(future, new ApiFutureCallback<String>() {
                    public void onSuccess(String messageId) {
                        log.info("Published message with ID: {}", messageId);
                        PinakaMetricRegistry.getMetricRegistry().meter(BQ_INGESTION_SUCCESS).mark();
                    }

                    public void onFailure(Throwable t) {
                        log.error("Failed to publish message: {}", t.getMessage());
                        PinakaMetricRegistry.getMetricRegistry().meter(BQ_INGESTION_FAILED).mark();
                    }
                }, MoreExecutors.directExecutor());
            }
        } catch (Exception e) {
            log.error("Exception while publishing the event: {} to BigQuery: {}", String.valueOf(event), e.getMessage());
            PinakaMetricRegistry.getMetricRegistry().meter(BQ_INGESTION_FAILED).mark();
            throw new Exception("Error in publishing PubSubMessage", e);
        }
    }

}
