package com.flipkart.fintech.profile.bureau.models;

public class PeakFinder {
    /**
     * Find the peak element (largest number) in an array that first increases then decreases.
     * Returns an integer array where [0] is the peak value and [1] is the peak index.
     */
    public static int[] findPeak(int[] arr) {
        if (arr == null || arr.length == 0) return new int[]{-1, -1};

        if (arr.length == 1) return new int[]{arr[0], 0};

        int left = 0;
        int right = arr.length - 1;

        // Handle edge cases
        if (arr[0] > arr[1]) return new int[]{arr[0], 0};
        if (arr[right] > arr[right - 1]) return new int[]{arr[right], right};

        while (left <= right) {
            int mid = left + (right - left) / 2;

            // Check if mid is the peak
            if ((mid == 0 || arr[mid] > arr[mid - 1]) && (mid == arr.length - 1 || arr[mid] > arr[mid + 1])) {
                return new int[]{arr[mid], mid};
            }

            // If middle element is on the ascending side, move right
            if (mid > 0 && arr[mid] > arr[mid - 1]) {
                left = mid + 1;
            }
            // If middle element is on the descending side, move left
            else {
                right = mid - 1;
            }
        }

        // This should not happen if the array has a peak
        return new int[]{-1, -1};
    }

    public static void main(String[] args) {
        int[] arr = {1, 2, 3, 3, 3, 3, 3, 4, 6, 10, 15, 40, 80, 90, 91, 91, 94, 96, 90, 91, 91, 91, 15, 2, 2, 2, 1};
        int[] result = findPeak(arr);
        System.out.println(result[0] + ", indexOf(" + result[0] + ") = " + result[1]);
    }
}
