package com.flipkart.fintech.profile.response;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.payments.ResponseStatus;
import lombok.Data;

import java.util.HashMap;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class VerifyVpaResponse {
    private ResponseStatus status;
    private String responseCode;
    private String responseMessage;
    private HashMap<String, Object> payload;
}

