package com.flipkart.fintech.profile.service;

import com.flipkart.fintech.pinaka.library.entities.ExperianReport;
import com.flipkart.fintech.profile.response.BureauDataResponse;
import com.flipkart.fintech.profile.response.InitialUserDataResponse;

public interface BureauInsights {
    void generateInsights(ExperianReport report, BureauDataResponse bureauDataResponse);
    InitialUserDataResponse fetchUserInitialData(ExperianReport report);
}
