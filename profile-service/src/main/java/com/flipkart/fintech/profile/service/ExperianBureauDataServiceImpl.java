package com.flipkart.fintech.profile.service;

import com.flipkart.fintech.exception.ServiceException;
import com.flipkart.fintech.profile.bureau.utils.ErrorCode;
import com.flipkart.fintech.profile.bureau.utils.ProfileUtils;
import com.flipkart.fintech.profile.model.ExperianBureauDataDto;
import com.flipkart.fintech.profile.request.CrossMerchantConsentRequest;
import com.flipkart.fintech.profile.yak.BureauDataStore;
import com.flipkart.fintech.profile.yak.entities.BureauDataEntity;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import lombok.CustomLog;

import java.util.Objects;
import java.util.function.Function;


@CustomLog
public class ExperianBureauDataServiceImpl implements BureauDataService {

    private final BureauDataStore creditReportDao;


    @Inject
    public ExperianBureauDataServiceImpl(@Named("bigTableCreditReportDao")BureauDataStore creditReportDao){
        this.creditReportDao = creditReportDao;
    }

    Function<ExperianBureauDataDto, BureauDataEntity> dtoToEntity = experianBureauDataDto ->  {
        if (Objects.isNull(experianBureauDataDto.getId())){
            throw new ServiceException(ProfileUtils.getServiceErrorResponse(ErrorCode.INTERNAL_SERVER_ERROR, "Profile Id is not present"));
        }
        String rowKey = experianBureauDataDto.getId();
        BureauDataEntity bureauDataEntity = new BureauDataEntity(rowKey);
        updateExperianBureauDataEntity(bureauDataEntity, experianBureauDataDto);
        return bureauDataEntity;
    };

    Function<BureauDataEntity, ExperianBureauDataDto> entityToDto = bureauDataEntity -> {
        ExperianBureauDataDto experianBureauDataDto = new ExperianBureauDataDto();
        experianBureauDataDto.setId(bureauDataEntity.getId());
        experianBureauDataDto.setMerchant(bureauDataEntity.getMerchant());
        updateExperianBureauDataDto(bureauDataEntity, experianBureauDataDto);
        return experianBureauDataDto;
    };

    private void updateExperianBureauDataDto(BureauDataEntity bureauDataEntity, ExperianBureauDataDto experianBureauDataDto){
        if (Objects.nonNull(bureauDataEntity.getExperianConsent())){
            experianBureauDataDto.setConsent(bureauDataEntity.getExperianConsent());
        }
        if (Objects.nonNull(bureauDataEntity.getExperianBureauResponse())){
            experianBureauDataDto.setExperianBureauResponse(bureauDataEntity.getExperianBureauResponse());
        }
        if (Objects.nonNull(bureauDataEntity.getCreditScore())){
            experianBureauDataDto.setCreditScore(bureauDataEntity.getCreditScore());
        }
    }

    private void updateExperianBureauDataEntity(BureauDataEntity bureauDataEntity, ExperianBureauDataDto experianBureauDataDto){
        if (Objects.nonNull(experianBureauDataDto)){
            if (Objects.nonNull(experianBureauDataDto.getConsent())){
                bureauDataEntity.setExperianConsent(experianBureauDataDto.getConsent());
            }
            if (Objects.nonNull(experianBureauDataDto.getExperianBureauResponse())){
                bureauDataEntity.setExperianBureauResponse(experianBureauDataDto.getExperianBureauResponse());
            }
            if (Objects.nonNull(experianBureauDataDto.getCreditScore())){
                bureauDataEntity.setCreditScore(experianBureauDataDto.getCreditScore());
            }
        }
    }

    @Override
    public ExperianBureauDataDto getBureauInsight(String profileId, String smUserId) {
        BureauDataEntity bureauDataEntity = creditReportDao.getExperianInsight(profileId, smUserId);
        log.debug("bureauentity is : {} ", bureauDataEntity);
        if (Objects.isNull(bureauDataEntity)){
            return null;
        }
        return entityToDto.apply(bureauDataEntity);
    }

    @Override
    public ExperianBureauDataDto getBureauRawData(String profileId, String smUserId) {
        BureauDataEntity bureauDataEntity = creditReportDao.getExperianBureauRawData(profileId, smUserId);
        log.debug("bureauentity is : {} ", bureauDataEntity);
        if (Objects.isNull(bureauDataEntity)){
            return null;
        }
        return entityToDto.apply(bureauDataEntity);
    }

    @Override
    public void updateExperianCrossMerchantConsent(CrossMerchantConsentRequest crossMerchantConsentRequest, Long createdTimestampInMillis) {
        creditReportDao.updateExperianCrossMerchantConsent(crossMerchantConsentRequest, createdTimestampInMillis);
    }

    @Override
    public boolean getIfExperianCrossMerchantConsentExists(String accountId) {
        return creditReportDao.getIfExperianCrossMerchantConsentExists(accountId);
    }

    @Override
    public void deleteExperianConsent(String accountId) {
        creditReportDao.deleteExperianConsent(accountId);
    }

    @Override
    public void addBureauData(ExperianBureauDataDto experianBureauDataDto, String smUserId, String merchantKey) {
        BureauDataEntity bureauDataEntity = dtoToEntity.apply(experianBureauDataDto);
        bureauDataEntity.setMerchant(merchantKey);
        bureauDataEntity.setId(smUserId);
        creditReportDao.putExperianBureauData(bureauDataEntity, smUserId, null);
    }

}
