package com.flipkart.fintech.profile.dao;

import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.Timer;
import com.flipkart.fintech.profile.model.Profile;
import io.dropwizard.hibernate.AbstractDAO;
import lombok.CustomLog;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Criteria;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Restrictions;

import javax.inject.Inject;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Root;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static com.flipkart.fintech.profile.Constants.PAN;
import static com.flipkart.fintech.profile.Constants.SM_USER_ID;

@CustomLog
public class ProfileDaoImpl extends AbstractDAO<Profile> implements ProfileDao {

    private final MetricRegistry metricsRegistry;
    @Inject
    public ProfileDaoImpl(SessionFactory sessionFactory, MetricRegistry metricsRegistry) {
        super(sessionFactory);
      this.metricsRegistry = metricsRegistry;
    }

    public Profile saveOrUpdate(Profile profile){
        return persist(profile);
    }

    public Profile getProfileByUserIdPan(String userId,String pan){
        return (Profile) criteria().add(Restrictions.eq("pan", pan)).add(Restrictions.eq("userId", userId)).uniqueResult();
    }

    public List<Profile> getProfileBySMUserIdPan(String smUserId,String pan){
        if(StringUtils.isEmpty(smUserId)) return Collections.emptyList();
        CriteriaBuilder cb = currentSession().getCriteriaBuilder();
        CriteriaQuery<Profile> cq = cb.createQuery(Profile.class);
        Root<Profile> root = cq.from(Profile.class);
        cq.where(cb.and(
                cb.equal(root.get(SM_USER_ID), smUserId),
                cb.equal(root.get(PAN), pan)
        ));
        return list(cq);
    }

    @Override
    public Profile getAll(Long profileId){
        try (Timer.Context timer = metricsRegistry.timer("getAllProfileSQL").time()) {
            Profile profile = (Profile) criteria()
                    .createAlias("addressDetailsList", "a", Criteria.LEFT_JOIN)
                    .createAlias("basicDetails", "b", Criteria.LEFT_JOIN)
                    .createAlias("employmentDetails", "e", Criteria.LEFT_JOIN)
                    .createAlias("contactDetails", "c", Criteria.LEFT_JOIN)
                    .add(Restrictions.eq("profileId", profileId))
                    .setResultTransformer(Criteria.DISTINCT_ROOT_ENTITY)
                    .uniqueResult();
            if (profile == null) {
                log.error("No profile found for user_id: {}", profileId);
                return null;
            }
            return profile;
        } catch (Exception e) {
            throw new RuntimeException("Error while getting profile for user_id: " + profileId, e);
        }
    }

    @Override
    public Profile getAllByUserId(String userId,String smUserId){
        try  {
            List<Profile>profileList = getProfilesByUserId(userId,smUserId);
            if (profileList.isEmpty()) {
                log.error("No profile found for user_id: {}", userId);
                return null;
            }
            Collections.sort(profileList, (p1, p2) -> Long.compare(p2.getProfileId(), p1.getProfileId()));
            return profileList.get(0);
        } catch (Exception e) {
            log.error("Error while getting profile for user_id: " + userId, e);
            throw new RuntimeException("Error while getting profile for user_id: " + userId, e);
        }
    }
    public List<Profile> getProfilesByUserId(String userId){
        CriteriaBuilder cb = currentSession().getCriteriaBuilder();
        CriteriaQuery<Profile> cq = cb.createQuery(Profile.class);
        Root<Profile> root = cq.from(Profile.class);
        cq.where(cb.equal(root.get("userId"), userId));
        return list(cq);
    }

    public List<Profile> getProfilesByUserId(String userId, String smUserID ){
        List<Profile> profiles = getProfilesBySmUserId(smUserID);
        if(Objects.isNull(profiles) || profiles.isEmpty()) return getProfilesByUserId(userId);
        return profiles;
    }

    public List<Profile> getProfilesBySmUserId(String smUserID){
        if(StringUtils.isEmpty(smUserID)) return null;
        CriteriaBuilder cb = currentSession().getCriteriaBuilder();
        CriteriaQuery<Profile> cq = cb.createQuery(Profile.class);
        Root<Profile> root = cq.from(Profile.class);
        cq.where(cb.equal(root.get(SM_USER_ID), smUserID));
        List<Profile> profiles  = list(cq);
        return profiles;
    }

    @Override
    public Profile getProfileBySmUserId(final String smUserId) {
        CriteriaBuilder cb = currentSession().getCriteriaBuilder();
        CriteriaQuery<Profile> cq = cb.createQuery(Profile.class);
        Root<Profile> root = cq.from(Profile.class);
        cq.where(cb.equal(root.get(SM_USER_ID), smUserId));
        return uniqueResult(cq);
    }
}
