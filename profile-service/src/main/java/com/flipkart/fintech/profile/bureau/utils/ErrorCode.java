package com.flipkart.fintech.profile.bureau.utils;

import javax.ws.rs.core.Response;

public enum ErrorCode {
    AUTHENTICATION_FAILED(Response.Status.UNAUTHORIZED, "Invalid token"),
    NOT_FOUND(Response.Status.NOT_FOUND, "Not Found"),
    NOT_ALLOWED(Response.Status.METHOD_NOT_ALLOWED, "User Not Allowed"),
    INTERNAL_SERVER_ERROR(Response.Status.INTERNAL_SERVER_ERROR, "Something went wrong!!"),
    BAD_REQUEST(Response.Status.BAD_REQUEST, "Bad Request");

    private Response.Status httpCode;
    private String          message;

    private ErrorCode(Response.Status httpCode, String message) {
        this.httpCode = httpCode;
        this.message = message;
    }

    public String getMessage() {
        return message;
    }

    public Response.Status getHttpCode() {
        return httpCode;
    }
}
