package com.flipkart.fintech.profile.model;

import com.flipkart.fintech.profile.bureau.models.Consent;
import com.flipkart.fintech.profile.bureau.models.CreditScore;
import com.flipkart.fintech.profile.bureau.models.ExperianBureauResponse;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class ExperianBureauDataDto {
    private String id;
    public ExperianBureauDataDto(){}
    public ExperianBureauDataDto(String id){this.id = id;}

    private Consent consent;
    private ExperianBureauResponse experianBureauResponse;
    private CreditScore creditScore;
    private String merchant;
}