package com.flipkart.fintech.profile.response;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.profile.model.EmploymentType;
import com.flipkart.fintech.profile.model.IncomeSource;
import lombok.*;

import javax.annotation.Nullable;

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
public class ProfileDetailedResponse {
    private Integer userEnteredPincode;
    private String merchantUserId;
    private String smUserId;
    private Long profileId;
    private String firstName;
    private String lastName;
    private String gender;
    private String dob;
    private EmploymentType employmentType;
    private String phoneNo;
    private String pan;
    private String email;
    private String addressLine1;
    private String addressLine2;
    private Integer shippingPincode;
    private String companyName;
    private String organizationId;
    private Integer monthlyIncome;
    private Integer bonusIncome;
    private Integer annualTurnOver;
    private IncomeSource incomeSource;
    private String industryType;
    private String industryId;
}
