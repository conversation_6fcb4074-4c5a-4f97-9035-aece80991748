package com.flipkart.fintech.profile.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.profile.bureau.models.Consent;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BureauFeatureRequestSm {

    @NotBlank
    @JsonProperty("smAccountId")
    private String smAccountId;

    @NotNull
    @JsonProperty("consent")
    private Consent consent;

    @NotNull
    @JsonProperty("leadId")
    private String leadId;

    @NotNull
    @JsonProperty("monthlyIncome")
    private Integer monthlyIncome;
}
