package com.flipkart.fintech.profile.service;

import com.codahale.metrics.annotation.Metered;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.flipkart.fintech.pinaka.library.*;
import com.flipkart.fintech.profile.bureau.models.CreditScore;
import com.flipkart.fintech.profile.eventPulisher.BQEventPublisher;
import com.flipkart.fintech.profile.model.ReportDetails;
import com.flipkart.fintech.profile.model.ReportFeatureValue;
import com.flipkart.fintech.profile.module.MapEntryFDP;
import com.flipkart.fintech.profile.pubsub.CreditScorePublisher;
import com.flipkart.kloud.config.DynamicBucket;
import com.google.inject.Inject;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import javax.inject.Named;

import com.supermoney.events.common.domainevents.creditscore.CreditScoreEvent;
import com.supermoney.schema.PinakaService.BreDataIngestionEventV1;
import com.supermoney.schema.PinakaService.MapEntryFDPRecord;
import lombok.CustomLog;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.text.StringEscapeUtils;
import com.sumo.crisys.client.CrisysClient;
import com.sumo.crisys.request.RawDataRequest;
import com.sumo.crisys.api.Report;
import com.flipkart.fintech.pinaka.library.entities.ExperianReport;

import java.util.stream.Collectors;

import static com.flipkart.fintech.profile.Constants.CREDIT_SCORE_EVENT_TYPE;
import static com.flipkart.fintech.profile.Constants.PUBLISH_CREDIT_SCORE_EVENT;

@CustomLog
public class ExperianFeatureService {

    private final ReportFeatureCalculator reportFeatureCalculator;
    private final CrisysClient crisysClient;
    private final ExecutorService executorService;
    private final BQEventPublisher bqEventPublisher;
    private static final XmlMapper xmlMapper = new XmlMapper();
    private static final String dataSource = "EXPERIAN";
    private static final String version = "V1";
    private static final String sourceVersion = "Experian_V1";
    private static final SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
    private final CreditScorePublisher creditScorePublisher;
    private final DynamicBucket dynamicBucket;


    @Inject
    public ExperianFeatureService(ReportFeatureCalculator reportFeatureCalculator,
                                  CrisysClient crisysClient,
                                  @Named("experianRawReportExecutor") ExecutorService executorService,
                                  BQEventPublisher bqEventPublisher, CreditScorePublisher creditScorePublisher,
                                  DynamicBucket dynamicBucket) {
        this.reportFeatureCalculator = reportFeatureCalculator;
        this.crisysClient = crisysClient;
        this.executorService = executorService;
        this.bqEventPublisher = bqEventPublisher;
        this.creditScorePublisher = creditScorePublisher;
        this.dynamicBucket = dynamicBucket;
    }

    public void createAndStoreExperianFeature(String xmlReport, ReportDetails reportDetails, String bureauErrorMessage, Integer monthlyIncome) {
        if (Objects.isNull(bureauErrorMessage)) {
            if (xmlReport == null || xmlReport.isEmpty() || Objects.isNull(reportDetails) ||
                    StringUtils.isEmpty(reportDetails.getSmUserId())) {
                throw new IllegalArgumentException("xmlReport or smUserId cannot be null or empty");
            }
            String deCodedXmlReport = StringEscapeUtils.unescapeHtml4(xmlReport);

            ReportContext reportContext = new ReportContext();
            reportContext.setReport(deCodedXmlReport);
            reportContext.setUserDetails(new UserDetails());
            reportContext.getUserDetails().setAccountId(reportDetails.getMerchantUserId());
            reportContext.getUserDetails().setSmUserId(reportDetails.getSmUserId());
            reportContext.getUserDetails().setMonthlyIncome(monthlyIncome);
            reportContext.setReportType(ReportType.EXPERIAN_REPORT);
            FeatureReport featureReport = reportFeatureCalculator.calculateFeatureScore(reportContext, Feature.getAllFeatures(), bureauErrorMessage);
            try {
                Future<?> future = executorService.submit(() -> {
                    storeRawReport(deCodedXmlReport, reportDetails);
                });
            } catch (Exception e) {
                log.error("Exception in calling store raw report", e);
            }
            if (Boolean.TRUE.equals(dynamicBucket.getBoolean(PUBLISH_CREDIT_SCORE_EVENT))) {
                CompletableFuture.runAsync(() -> publishEventToPubSub(reportDetails.getSmUserId()));
            }
            sendEvent(reportContext, featureReport, reportDetails);
        } else {
            ReportContext reportContext = new ReportContext();
            reportContext.setReport(null);
            reportContext.setUserDetails(new UserDetails());
            reportContext.getUserDetails().setAccountId(reportDetails.getMerchantUserId());
            reportContext.getUserDetails().setSmUserId(reportDetails.getSmUserId());
            reportContext.getUserDetails().setMonthlyIncome(monthlyIncome);
            reportContext.setReportType(ReportType.EXPERIAN_REPORT);
            FeatureReport featureReport = reportFeatureCalculator.calculateFeatureScore(reportContext, Feature.getNtc(), bureauErrorMessage);
            sendEvent(reportContext, featureReport, reportDetails);
        }
    }


    public void publishEventToPubSub(String smUserId) {
        CreditScoreEvent event = new CreditScoreEvent();
        event.setSmUserId(smUserId);
        event.setTimestamp(new Date().getTime());
        event.setEventId(UUID.randomUUID().toString());
        event.setEventTimestampInMillis(System.currentTimeMillis());
        event.setEventType(CREDIT_SCORE_EVENT_TYPE);
        creditScorePublisher.publish(event);
    }

    private BreDataIngestionEventV1 breFDPtoBQEvent(ReportFeatureValue reportFeatureValue){

        List<MapEntryFDPRecord> mapEntryFDPRecordsList = new ArrayList<>();
        for(MapEntryFDP mapEntryFDP: reportFeatureValue.getFeatureValue()){
            MapEntryFDPRecord mapEntryFDPRecord = new MapEntryFDPRecord();
            mapEntryFDPRecord.setKey(mapEntryFDP.getKey());
            mapEntryFDPRecord.setValue(mapEntryFDP.getValue());
            mapEntryFDPRecordsList.add(mapEntryFDPRecord);
        }

        BreDataIngestionEventV1 breDataIngestionEvent = BreDataIngestionEventV1.newBuilder()
                .setEventId(UUID.randomUUID().toString())
                .setAccountId(reportFeatureValue.getMerchantUserId())
                .setSmUserId(reportFeatureValue.getSmUserId())
                .setReportDate(reportFeatureValue.getReportDate())
                .setFeatureValue(mapEntryFDPRecordsList)
                .setId(reportFeatureValue.getId())
                .setLeadId(reportFeatureValue.getLeadId())
                .setProfileId(reportFeatureValue.getProfileId())
                .setSource(reportFeatureValue.getSource())
                .build();
        return breDataIngestionEvent;
    }

    private void sendEvent(ReportContext reportContext, FeatureReport featureReport, ReportDetails reportDetails) {
        try{
            if (Objects.nonNull(featureReport)) {
                ReportFeatureValue reportFeatureValue = createReportFeatureValue(featureReport, reportContext.getReportType(), reportDetails);
                if (!reportFeatureValue.getFeatureValue().isEmpty()) {
                    bqEventPublisher.publishEventOptional(breFDPtoBQEvent(reportFeatureValue));
                }
            }
        }catch (Exception e){
            log.error(String.format("Could not ingest data to BQ: %s",e.getMessage()));
        }
    }
    private ReportFeatureValue createReportFeatureValue(FeatureReport featureReport, ReportType reportType, ReportDetails reportDetails) {
        return ReportFeatureValue.builder()
                .merchantUserId(reportDetails.getMerchantUserId())
                .smUserId(reportDetails.getSmUserId())
                .reportDate(featureReport.reportDateInYyyyMmDd)
                .id(reportDetails.getId())
                .leadId(reportDetails.getLeadId())
                .source(reportDetails.getSource())
                .profileId(reportDetails.getProfileId())
                .featureValue(getFeatureValue(featureReport, reportType))
                .build();
    }

    private List<MapEntryFDP> getFeatureValue(FeatureReport featureReport, ReportType reportType) {
        return featureReport.featureMap.entrySet().stream()
                .map(entry -> {
                    if (entry.getKey() != Feature.EXP_SCORE) {
                        return MapEntryFDP.builder()
                                .key(reportType.getName() + "_" + entry.getKey().getName())
                                .value(entry.getValue())
                                .build();
                    } else {
                        return getScoreMap(reportType.getName() + "_" + entry.getKey().getName(), entry.getValue());
                    }
                }).collect(Collectors.toList());
    }

    private MapEntryFDP getScoreMap(String key, String score) {
        return new MapEntryFDP(key, "A" + Integer.parseInt(score) / 10);
    }

    @Metered
    private void storeRawReport(String deCodedXmlReport, ReportDetails reportDetails){
        try {
            xmlMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            xmlMapper.configure(DeserializationFeature.FAIL_ON_MISSING_EXTERNAL_TYPE_ID_PROPERTY, false);
            ExperianReport experianReport = xmlMapper.readValue(deCodedXmlReport, ExperianReport.class);
            Map<String,Object> report = new HashMap<>();
            report.put("report",experianReport);
            Report rawReport = new Report();
            rawReport.setReport(report);
            String accountId = reportDetails.getMerchantUserId();
            Timestamp createdAt = Timestamp.from(Instant.now());
            Timestamp updatedAt = Timestamp.from(Instant.now());
            RawDataRequest rawDataRequest = RawDataRequest.builder()
                .accountId(accountId)
                .dataSource(dataSource)
                .version(version)
                .smUserId(reportDetails.getSmUserId())
                .sourceVersion(sourceVersion)
                .rawReport(rawReport)
                .createdAt(createdAt)
                .updatedAt(updatedAt)
                .build();
            crisysClient.addRawData(rawDataRequest);
        } catch (Exception e) {
            log.error("Exception While inserting Raw Experian Report for Account ID {}", reportDetails.getMerchantUserId(), e);
        }
    }

    public CreditScore getCreditScoreFromReport(String reportHtml, Date createdAt) {
        if (Objects.nonNull(reportHtml)) {
            CreditScore creditScore = new CreditScore();
            String deCodedXmlReport = StringEscapeUtils.unescapeHtml4(reportHtml);
            ReportContext reportContext = new ReportContext();
            reportContext.setReport(deCodedXmlReport);
            reportContext.setReportType(ReportType.EXPERIAN_REPORT);
            ExperianReport experianReport = reportFeatureCalculator.getParsedReport(reportContext);
            if (experianReport != null) {
                if (experianReport.getScore() != null) {
                    creditScore.setCreditScore(String.valueOf(experianReport.getScore().getBureauScore()));
                }
                if (experianReport.getReportDate() != null) {
                    creditScore.setCreatedAt(createdAt);
                }
            }
            return creditScore;
        }
        return null;
    }

}