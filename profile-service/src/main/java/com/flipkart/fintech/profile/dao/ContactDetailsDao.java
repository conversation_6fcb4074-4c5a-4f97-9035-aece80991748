package com.flipkart.fintech.profile.dao;

import com.flipkart.fintech.profile.model.ContactDetails;
import io.dropwizard.hibernate.AbstractDAO;
import org.hibernate.SessionFactory;

public class ContactDetailsDao extends AbstractDAO<ContactDetails> {
    public ContactDetailsDao(SessionFactory sessionFactory) {
        super(sessionFactory);
    }

    public ContactDetails saveOrUpdate(ContactDetails contactDetails){
        return persist(contactDetails);
    }
}