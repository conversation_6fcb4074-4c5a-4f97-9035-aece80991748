package com.flipkart.fintech.profile.web.v2;


import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.profile.request.ProfileRequest;
import com.flipkart.fintech.profile.response.ProfileBasicDetailResponse;
import com.flipkart.fintech.profile.response.ProfileCRUDResponse;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.fintech.profile.service.ProfileService;

import io.dropwizard.hibernate.UnitOfWork;
import lombok.CustomLog;

import javax.inject.Inject;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;

@CustomLog
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("2/profile")
public class ProfileServiceResourceV2 {

    private final ProfileService profileService;

    @Inject
    public ProfileServiceResourceV2(ProfileService profileService) {
        this.profileService = profileService;
    }

    @POST
    @Timed
    @ExceptionMetered
    @Path("/create")
    @UnitOfWork(value = "profile_service")
    public ProfileCRUDResponse createProfile(@Valid ProfileRequest request) {
        log.debug("Creating profile for userId: {}", request.getUserId());
        ProfileCRUDResponse profileResponse = this.profileService.createProfile(request);
        return profileResponse;
    }

    @GET
    @Timed
    @ExceptionMetered
    @Path("/all/{profile_id}")
    @UnitOfWork(value = "profile_service")
    public ProfileDetailedResponse getAll(@PathParam("profile_id") Long profileId) {
        log.info("Getting latest profile for profile_id: {}", profileId);
         return this.profileService.getProfileById(profileId);
    }

    @GET
    @Timed
    @ExceptionMetered
    @Path("/getAll")
    @UnitOfWork(value = "profile_service")
    public ProfileDetailedResponse getAll(@QueryParam(value = "merchantUserId") String merchantUserId,
                                          @NotNull @QueryParam(value = "smUserId") String smUserId,
                                          @QueryParam(value ="isUnmaskedData") @DefaultValue("false") boolean isUnmaskedData) {
        log.info("Getting latest profile for merchantUserId: {} ", merchantUserId);
        return this.profileService.getProfileByUserId(merchantUserId, smUserId, isUnmaskedData);
    }


    @GET
    @Timed
    @ExceptionMetered
    @Path("/basic-profile")
    @UnitOfWork(value = "profile_service")
    public ProfileBasicDetailResponse getProfile(@NotNull @QueryParam(value = "smUserId") String smUserId) {
        log.info("Fetch profile for smUserId: {}", smUserId);
        ProfileBasicDetailResponse profileBasicDetailResponse = profileService.getProfile(smUserId);
        return profileBasicDetailResponse;
    }
}