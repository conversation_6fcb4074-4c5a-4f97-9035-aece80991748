package com.flipkart.fintech.profile.common;

import com.codahale.metrics.MetricRegistry;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import lombok.Data;

@Data
public class PinakaMetricRegistry {

    private static MetricRegistry metricRegistry;

    @Inject
    public PinakaMetricRegistry(@Named("metricRegistryForProfileService") MetricRegistry metricRegistry) {
        PinakaMetricRegistry.metricRegistry = metricRegistry;
    }

    public static synchronized MetricRegistry getMetricRegistry() {
        if (metricRegistry == null) {
            // if instance is null, initialize
            metricRegistry = new MetricRegistry();
        }
        return metricRegistry;
    }
}
