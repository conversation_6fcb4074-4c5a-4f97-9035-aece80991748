package com.flipkart.fintech.profile.request;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import javax.annotation.Nullable;


@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Getter
@ToString
public class ProfileRequest {
    private String userId;
    private String smUserId;
    private String pan;
    @Nullable
    private String dob;
    @Nullable
    private String gender;
    @Nullable
    private String employmentType;
    private String firstName;
    private String lastName;
    private String emailId;
    @Nullable
    private Integer pincode;

    @Nullable
    private String addressLine1;
    @Nullable
    private String addressLine2;
    @Nullable
    private String companyName;
    @Nullable
    private String organizationId;
    @Nullable
    private String monthlyIncome;
    @Nullable
    private String bonusIncome;
    @Nullable
    private String incomeSource;
    @Nullable
    private String industryType;
    @Nullable
    private String industryId;

    @Nullable
    private String annualTurnOver;
    @Nullable
    private String verifiedFirstName;

    @Nullable
    private String verifiedLastName;

    @Nullable
    private Boolean match;

    @Nullable
    private Integer matchScore;

    @JsonIgnore
    public boolean isValidBonusIncome() {
        return StringUtils.isNotEmpty(bonusIncome) && StringUtils.isNumeric(bonusIncome) && Integer.parseInt(bonusIncome) > 0;

    }
}
