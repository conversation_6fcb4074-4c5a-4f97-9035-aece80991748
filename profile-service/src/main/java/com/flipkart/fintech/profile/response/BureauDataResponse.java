package com.flipkart.fintech.profile.response;

import com.flipkart.fintech.profile.model.AccountType;
import com.flipkart.fintech.profile.model.bureauLender;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class BureauDataResponse {
    private String creditScore;
    private String error;
    private String status;
    private String lastUpdatedAt;
    private Integer onTimePayment;
    private Double onTimePaymentPer;
    private Integer delayedPayment;
    private Integer totalPayment;
    private Double creditUsage;
    private Integer creditLimit;
    private Integer currentBalance;
    private List<bureauLender> lenders;
    private String creditAge;
    private String creditMix;
    private Map<String, AccountType> accountTypeMap;
    private Integer last180DaysCreditEnquiries;
    private boolean reportValid;
    private boolean isConsentValid;
    private String merchant;
    private boolean isCrossMerchantConsentExists;
}
