package com.flipkart.fintech.profile.dao;

import com.flipkart.fintech.profile.model.AddressDetails;
import com.flipkart.fintech.profile.model.BasicDetails;
import io.dropwizard.hibernate.AbstractDAO;
import org.hibernate.SessionFactory;

public class BasicDetailsDao extends AbstractDAO<BasicDetails> {
    public BasicDetailsDao(SessionFactory sessionFactory) {
        super(sessionFactory);
    }
    public BasicDetails saveOrUpdate(BasicDetails basicDetails){
        return persist(basicDetails);
    }
}
