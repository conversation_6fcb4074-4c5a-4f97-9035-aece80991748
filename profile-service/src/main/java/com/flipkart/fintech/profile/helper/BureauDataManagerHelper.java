package com.flipkart.fintech.profile.helper;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.profile.api.request.BureauRequest;
import com.flipkart.fintech.profile.api.request.RefreshBureauRequest;
import com.flipkart.fintech.profile.api.response.BureauResponse;
import com.flipkart.fintech.profile.bureau.models.BureauDataForInsight;
import com.flipkart.fintech.profile.bureau.models.Consent;
import com.flipkart.fintech.profile.bureau.models.ExperianBureauResponse;
import com.flipkart.fintech.profile.bureau.models.INProfileResponse;
import com.flipkart.fintech.profile.bureau.utils.BureauUtils;
import com.flipkart.fintech.profile.model.ExperianBureauDataDto;
import com.flipkart.fintech.profile.model.ReportDetails;
import com.flipkart.fintech.profile.model.ReportFetchSource;
import com.flipkart.fintech.profile.request.BureauDataRequest;
import com.flipkart.fintech.profile.request.ProfileRequest;
import com.flipkart.fintech.profile.response.BureauDataResponse;
import com.flipkart.fintech.profile.response.CrossMerchantConsentResponse;
import com.flipkart.fintech.profile.response.DeleteBureauConsentResponse;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.fintech.profile.service.BureauDataManagerImpl;
import com.flipkart.fintech.profile.service.ExceptionConstants;
import com.flipkart.fintech.profile.service.ProfileException;
import com.flipkart.fintech.security.aes.AESService;
import lombok.CustomLog;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;

import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import java.io.InputStream;
import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Base64;
import java.util.Date;
import java.util.Objects;
import static com.flipkart.fintech.profile.Constants.*;

@CustomLog
public class BureauDataManagerHelper {

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static CrossMerchantConsentResponse buildUpdateConsentResponseForSuccess() {
        CrossMerchantConsentResponse consentResponse = new CrossMerchantConsentResponse();
        consentResponse.setStatus(SUCCESS);
        return consentResponse;
    }


    public static DeleteBureauConsentResponse buildDeleteBureauConsentResponse(String errorMessage) {
        DeleteBureauConsentResponse consentResponse = new DeleteBureauConsentResponse();
        if (Objects.isNull(errorMessage)) {
            consentResponse.setMessage("Successfully deleted bureau consent");
            consentResponse.setSuccess(Boolean.TRUE);
        }else{
            consentResponse.setMessage(errorMessage);
            consentResponse.setSuccess(Boolean.FALSE);
        }
        return consentResponse;
    }

    public static CrossMerchantConsentResponse buildUpdateConsentResponseForFailure(String errorMsg) {
        CrossMerchantConsentResponse consentResponse = new CrossMerchantConsentResponse();
        consentResponse.setStatus(FAILURE);
        consentResponse.setErrorMsg(errorMsg);
        return consentResponse;
    }

    public static String getEncryptedData(String data) {
        byte[] ciphertextBytes = AESService.encrypt(ENCRYPT_KEY, data.getBytes(StandardCharsets.UTF_8));
        return new String(Base64.getEncoder().encode(ciphertextBytes), StandardCharsets.UTF_8);
    }

    public static RefreshBureauRequest buildRefreshBureauRequest(String hitId) {
        RefreshBureauRequest refreshBureauRequest = new RefreshBureauRequest();
        refreshBureauRequest.setHitId(hitId);
        return refreshBureauRequest;
    }

    public static BureauRequest buildBureauRequestFromProfile(ProfileDetailedResponse profile) {
        BureauRequest bureauRequest = new BureauRequest();
        bureauRequest.setMobileNo(profile.getPhoneNo());
        if (!StringUtils.isBlank(profile.getFirstName())) {
            bureauRequest.setFirstName(profile.getFirstName());
        }
        if (!StringUtils.isBlank(profile.getLastName())) {
            bureauRequest.setLastName(profile.getLastName());
        }
        bureauRequest.setPan(profile.getPan());
        bureauRequest.setEmail(profile.getEmail());
        return bureauRequest;
    }

    public static boolean isConsentValidForRefreshScore(Consent consent, Long ttl) {
        if (Objects.isNull(consent)) {
            return false;
        }
        long dateTime = consent.getTs();
        long currTime = new Date().getTime();
        long durationInMilliSeconds = currTime - dateTime;
        if (durationInMilliSeconds <= ttl) {
            return true;
        }
        return false;
    }

    public static BureauRequest buildBureauRequest(BureauDataRequest bureauDataRequest, ProfileDetailedResponse profile) {
        BureauRequest bureauRequest = new BureauRequest();
        bureauRequest.setFirstName(bureauDataRequest.getFirstName());
        bureauRequest.setLastName(bureauDataRequest.getLastName());
        bureauRequest.setPan(bureauDataRequest.getPan());
        bureauRequest.setMobileNo(profile.getPhoneNo());
        bureauRequest.setEmail(profile.getEmail());
        return bureauRequest;
    }

    public static ExperianBureauDataDto buildExperianBureauDataDto(Consent consent, BureauResponse bureauResponse, String smUserId) {
        ExperianBureauDataDto experianBureauDataDto = new ExperianBureauDataDto();
        experianBureauDataDto.setId(smUserId);
        experianBureauDataDto.setConsent(consent);
        experianBureauDataDto.setExperianBureauResponse(new ExperianBureauResponse(bureauResponse.getRawData(), new Date(), bureauResponse.getStgOneHitId(), bureauResponse.getStgTwoHitId(), bureauResponse.getExperianUserId()));
        return experianBureauDataDto;
    }

    public static BureauResponse getMockedBureauReport() {
        BureauResponse bureauResponse;
        try {
            InputStream inputStream = BureauDataManagerImpl.class.getClassLoader().getResourceAsStream("test.json");
            bureauResponse = objectMapper.readValue(inputStream, BureauResponse.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return bureauResponse;
    }

    public static String getCreditScore(String rawData) {
        rawData = StringEscapeUtils.unescapeXml(rawData);
        try {
            JAXBContext jaxbContext = JAXBContext.newInstance(INProfileResponse.class);
            Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
            INProfileResponse inProfileResponse = (INProfileResponse) unmarshaller.unmarshal(new StringReader(rawData));
            return inProfileResponse.getScore().getBureauScore();
        } catch (JAXBException e) {
            log.info("getting error in getting credit score from rawdata exception {}", e.getMessage());
            throw new ProfileException(ExceptionConstants.CREDIT_SCORE_CONVERSION_FAILED, e);
        }
    }

    public static ProfileRequest buildProfileRequest(BureauDataRequest bureauDataRequest) {
        ProfileRequest profileRequest = ProfileRequest.builder()
                .dob(bureauDataRequest.getDob())
                .pan(bureauDataRequest.getPan())
                .userId(bureauDataRequest.getMerchantUserId())
                .smUserId(bureauDataRequest.getSmUserId())
                .pincode(0)
                .firstName(bureauDataRequest.getFirstName())
                .lastName(bureauDataRequest.getLastName())
                .emailId(bureauDataRequest.getEmailId())
                .build();
        return profileRequest;
    }

    public static BureauDataResponse buildBureauDataResponseForFailure(BureauDataResponse bureauDataResponse) {
        bureauDataResponse.setError(BureauUtils.CONSENT_VALIDITY_OVER_);
        bureauDataResponse.setStatus(BureauUtils.OK);
        return bureauDataResponse;
    }


    public static BureauDataResponse buildBureauDataForMismatch(BureauDataResponse bureauDataResponse) {
        bureauDataResponse.setError("Mobile & Pan Mismatched for user");
        bureauDataResponse.setStatus(BureauUtils.OK);
        return bureauDataResponse;
    }


    public static BureauDataResponse buildBureauDataNotExist(BureauDataResponse bureauDataResponse) {
        bureauDataResponse.setError(BureauUtils.BUREAU_DOES_NOT_EXIST);
        bureauDataResponse.setStatus(BureauUtils.OK);
        return bureauDataResponse;
    }

    public static ReportDetails getReportDetails(String accountId, String smUserId, String profileId, String reportId, ReportFetchSource reportFetchSource, String leadId) {
        return ReportDetails.builder()
                .source(reportFetchSource.toString())
                .id(reportId)
                .merchantUserId(accountId)
                .smUserId(smUserId)
                .profileId(profileId)
                .leadId(leadId)
                .build();
    }


    public static BureauDataForInsight buildBureauDataForReport(String report) {
        return BureauDataForInsight.builder().
                report(report).
                build();
    }

    public static BureauDataForInsight buildBureauDataForNoReport(String error) {
        return BureauDataForInsight.builder().
                errorString(error).
                build();
    }

    public static boolean isOlderThan30Days(Date createdAt) {
        if(createdAt == null) {
            return true;
        }
        LocalDate localDate = createdAt.toInstant().
                atZone(ZoneId.systemDefault()).toLocalDate();

        LocalDate thirtyDaysAgo = LocalDate.now().minusDays(30);
        return localDate.isBefore(thirtyDaysAgo);
    }

}
