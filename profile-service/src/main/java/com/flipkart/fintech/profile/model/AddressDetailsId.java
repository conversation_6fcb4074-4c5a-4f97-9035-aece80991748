package com.flipkart.fintech.profile.model;

import lombok.Setter;

import java.io.Serializable;
import java.util.Objects;

import javax.persistence.*;

@Embeddable
public class AddressDetailsId implements Serializable {

    @Column(name = "profile_id")
    private Long profileId;

    public AddressDetailsId(Long profileId, Long addressId) {
        this.profileId = profileId;
        this.addressId = addressId;
    }

    @Column(name = "address_id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long addressId;

    public Long getProfileId() {
        return profileId;
    }

    public void setProfileId(Long profileId) {
        this.profileId = profileId;
    }

    public Long getAddressId() {
        return addressId;
    }

    public void setAddressId(Long addressId) {
        this.addressId = addressId;
    }

    public AddressDetailsId() {

    }

}