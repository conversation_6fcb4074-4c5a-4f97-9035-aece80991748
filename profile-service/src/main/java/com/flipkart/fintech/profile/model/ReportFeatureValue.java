package com.flipkart.fintech.profile.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.profile.module.MapEntryFDP;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@Builder
@ToString
public class ReportFeatureValue {
    @JsonProperty("AccountId")
    public String merchantUserId;
    @JsonProperty("SmUserId")
    public String smUserId;
    @JsonProperty("ReportDate")
    public String reportDate;
    @JsonProperty("FeatureValue")
    public List<MapEntryFDP> featureValue;
    @JsonProperty("Id")
    public String id;
    @JsonProperty("Source")
    public String source;
    @JsonProperty("LeadId")
    public String leadId;
    @JsonProperty("ProfileId")
    public String profileId;
}
