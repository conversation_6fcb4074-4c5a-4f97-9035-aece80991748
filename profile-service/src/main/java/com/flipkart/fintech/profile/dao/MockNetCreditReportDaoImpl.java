package com.flipkart.fintech.profile.dao;

import com.flipkart.fintech.profile.request.CrossMerchantConsentRequest;
import com.flipkart.fintech.profile.yak.BureauDataStore;
import com.flipkart.fintech.profile.yak.entities.BureauDataEntity;

public class MockNetCreditReportDaoImpl implements BureauDataStore {
    @Override
    public BureauDataEntity getExperianInsight(String profileId, String accountId) {
        return new BureauDataEntity();
    }

    @Override
    public BureauDataEntity getExperianBureauRawData(String profileId, String accountId) {
        return new BureauDataEntity();
    }

    @Override
    public void putExperianBureauData(BureauDataEntity bureauDataEntity, String accountId, Long createdTimestampInMillis) {

    }

    @Override
    public void updateExperianCrossMerchantConsent(CrossMerchantConsentRequest crossMerchantConsentRequest, Long createdTimestampInMillis) {

    }

    @Override
    public boolean getIfExperianCrossMerchantConsentExists(String accountId) {
        return false;
    }

    @Override
    public void deleteExperianConsent(String accountId) {

    }
}
