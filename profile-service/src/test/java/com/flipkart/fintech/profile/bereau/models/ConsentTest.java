package com.flipkart.fintech.profile.bereau.models;

import com.flipkart.fintech.profile.bureau.models.Consent;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class ConsentTest {

    @Test
    void setTsShouldSetProvidedTimestampWhenItIsNotInFuture() {
        Consent consent = new Consent();
        long validTs = System.currentTimeMillis() - 1000; // 1 second in the past
        consent.setTs(validTs);
        assertEquals(validTs, consent.getTs());
    }

    @Test
    void setTsShouldSetCurrentTimestampWhenProvidedTimestampIsInFuture() {
        Consent consent = new Consent();
        long futureTs = System.currentTimeMillis() + 10000; // 10 seconds in the future
        consent.setTs(futureTs);
        assertTrue(consent.getTs() <= System.currentTimeMillis());
    }

    @Test
    void setTsShouldHandleNullTimestampGracefully() {
        Consent consent = new Consent();
        consent.setTs(null);
        assertNull(consent.getTs());
    }
}