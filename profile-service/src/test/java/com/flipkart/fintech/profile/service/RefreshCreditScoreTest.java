package com.flipkart.fintech.profile.service;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.isNull;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;


import com.flipkart.fintech.pandora.client.CheckBureauScoreClient;
import com.flipkart.fintech.pinaka.library.ReportFeatureCalculator;
import com.flipkart.fintech.profile.Decrypter.Decrypter;
import com.flipkart.fintech.profile.api.response.BureauResponse;
import com.flipkart.fintech.profile.bureau.models.BureauDataForInsight;
import com.flipkart.fintech.profile.bureau.models.ExperianBureauResponse;
import com.flipkart.fintech.profile.config.ProfileServiceConfig;
import com.flipkart.fintech.profile.model.ExperianBureauDataDto;
import com.flipkart.fintech.user.data.client.UserDataClient;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockitoAnnotations;
import java.lang.reflect.Field;

public class RefreshCreditScoreTest {

    private BureauDataManagerImpl bureauDataManager;

    private BureauDataService bureauDataService;
    private BureauInsights bureauInsights;
    private ExperianBureauDataDto experianBureauDataDto;
    private ExperianBureauResponse experianBureauResponse;
    private ProfileServiceConfig profileServiceConfig;
    private CheckBureauScoreClient checkBureauScoreClient;

    private final String smUserId = "smUser123";
    private final String merchantUserId = "merchant456";

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        // Mock all required dependencies
        ProfileService profileService = mock(ProfileService.class);
        checkBureauScoreClient = mock(CheckBureauScoreClient.class);
        bureauDataService = mock(BureauDataService.class);
        ExperianFeatureService experianFeatureService = mock(ExperianFeatureService.class);
        ReportFeatureCalculator reportFeatureCalculator = mock(ReportFeatureCalculator.class);
        bureauInsights = mock(BureauInsights.class);
        UserDataClient userDataClient = mock(UserDataClient.class);
        profileServiceConfig = mock(ProfileServiceConfig.class);
        Decrypter decrypter = mock(Decrypter.class);

        // Create instance with all dependencies
        bureauDataManager = new BureauDataManagerImpl(
                profileService,
                checkBureauScoreClient,
                bureauDataService,
                experianFeatureService,
                reportFeatureCalculator,
                bureauInsights,
                userDataClient,
                profileServiceConfig,
                decrypter
        );

        experianBureauDataDto = mock(ExperianBureauDataDto.class);
        experianBureauResponse = mock(ExperianBureauResponse.class);
    }

    @Test
    public void testRefreshBureauDataSm_WhenValidBureauDataExists_ReturnsNonNull() {
        String html = "mocked-raw-html";

        BureauDataForInsight expected = mock(BureauDataForInsight.class);
        when(expected.getReport()).thenReturn(html);

        BureauResponse bureauResponse = mock(BureauResponse.class);
        when(bureauResponse.getErrorMsg()).thenReturn(null);
        when(bureauResponse.getRawData()).thenReturn(html);

        when(bureauDataService.getBureauRawData("", smUserId)).thenReturn(experianBureauDataDto);
        when(experianBureauDataDto.getExperianBureauResponse()).thenReturn(experianBureauResponse);
        when(experianBureauResponse.getShowHtmlReportForCreditReport()).thenReturn(html);

        when(profileServiceConfig.isExperianMocked()).thenReturn(false);
        when(checkBureauScoreClient.getCreditReportByExperian(any())).thenReturn(bureauResponse);

        BureauDataForInsight result = bureauDataManager.refreshBureauDataSm(smUserId, merchantUserId);

        assertNotNull(result);
        assertEquals(html, result.getReport());
    }

    @Test
    public void testRefreshBureauDataSm_WhenBureauDataIsNull_ReturnsNull() {
        when(bureauDataService.getBureauRawData("", smUserId)).thenReturn(null);

        BureauDataForInsight result = bureauDataManager.refreshBureauDataSm(smUserId, merchantUserId);

        assertNull(result);
    }

    @Test
    public void testRefreshBureauDataSm_WhenExperianResponseIsNull_ReturnsNull() {
        when(bureauDataService.getBureauRawData("", smUserId)).thenReturn(experianBureauDataDto);
        when(experianBureauDataDto.getExperianBureauResponse()).thenReturn(null);

        BureauDataForInsight result = bureauDataManager.refreshBureauDataSm(smUserId, merchantUserId);

        assertNull(result);
    }

    @Test
    public void testRefreshBureauDataSm_WhenHtmlReportIsNull_ReturnsNull() {
        when(bureauDataService.getBureauRawData("", smUserId)).thenReturn(experianBureauDataDto);
        when(experianBureauDataDto.getExperianBureauResponse()).thenReturn(experianBureauResponse);
        when(experianBureauResponse.getShowHtmlReportForCreditReport()).thenReturn(null);

        BureauDataForInsight result = bureauDataManager.refreshBureauDataSm(smUserId, merchantUserId);

        assertNull(result);
    }

}
