# Apply-Now API Latency Improvement Plan

## Executive Summary
Based on production trace analysis of **6 traces** spanning June 1-2, 2025, the apply-now API shows performance ranging from **162ms to 492ms**. This document identifies specific code changes required to reduce latency to target **~150ms** (consistent performance).

## Performance Analysis Summary

### Trace Performance Comparison
| Trace ID | Date/Time | Duration | Key Bottlenecks |
|----------|-----------|----------|-----------------|
| Trace-3fc1f2 | 2025-06-01 16:26:02 | 492.53ms | Profile: 109ms, Winterfell: 100ms, Dexter: 69ms |
| Trace-5ac01b (original) | 2025-06-02 15:50:19 | 442.42ms | Similar pattern to 3fc1f2 |
| Trace-5ac01b (new) | 2025-06-02 16:52:26 | **442.42ms** | **Profile: 93ms, Winterfell: 110ms, Dexter: 58ms** |
| Trace-11a65b | 2025-06-02 15:44:08 | 404.22ms | Moderate performance |
| Trace-7490e0 | 2025-06-02 15:51:07 | 240.46ms | Better performance |
| Trace-45d7f6 | 2025-06-02 15:53:55 | **162.92ms** | **Best performance** |

### Key Findings from Latest Trace (5ac01b-16:52:26)
- **Total Response Time**: 442.42ms
- **Profile Service Call**: 93.04ms (21%)
- **Winterfell Application Creation**: 110.28ms (25%)
- **Dexter User Profile Calls**: 58.01ms total (13%)
  - First call: 29.49ms
  - Second call: 28.53ms
- **Winterfell Active Apps Lookup**: 16.89ms total (4%)
- **Other Operations**: 163.20ms (37%)

## Required Code Changes for Latency Reduction

### 1. Profile Service Database Query Optimization

**Current Issue**: Complex JOIN query taking 109ms
**Target Reduction**: 80-90ms savings

**File**: `profile-service/src/main/java/com/flipkart/fintech/profile/dao/ProfileDaoImpl.java`

**Current Code**:
```java
@Override
public Profile getAll(Long profileId){
    try (Timer.Context timer = metricsRegistry.timer("getAllProfileSQL").time()) {
        Profile profile = (Profile) criteria()
                .createAlias("addressDetailsList", "a", Criteria.LEFT_JOIN)
                .createAlias("basicDetails", "b", Criteria.LEFT_JOIN)
                .createAlias("employmentDetails", "e", Criteria.LEFT_JOIN)
                .createAlias("contactDetails", "c", Criteria.LEFT_JOIN)
                .add(Restrictions.eq("profileId", profileId))
                .setResultTransformer(Criteria.DISTINCT_ROOT_ENTITY)
                .uniqueResult();
```

**Required Change**: This query needs optimization - either through indexing, query restructuring, or caching.

### 2. Sequential Dexter Calls Parallelization

**Current Issue**: Two sequential Dexter calls taking 69ms total
**Target Reduction**: 35ms savings

**File**: `pinaka-service/src/main/java/com/flipkart/fintech/pinaka/service/core/v7/CreateLeadRequestFactory.java`

**Current Code**:
```java
Boolean dexterFlag = dynamicBucket.getBoolean(DexterConstants.DEXTER_FLAG);
if(dexterFlag){
    fetchUserProfileResponse = userProfileScores.getUserProfileByDexter(requestId, merchantUser);
}else{
    userProfileResponseV3 = userProfileScores.getUserProfile(merchantUser);
}
```

**File**: `pinaka-common/src/main/java/com/flipkart/fintech/pinaka/common/userprofilescores/UserProfileScoresImpl.java`

**Current Code**:
```java
@Override
public FetchUserProfileResponse getUserProfileByDexter(String requestId, MerchantUser merchantUser) {
    try {
        FetchUserProfileRequest fetchUserProfileRequest = new FetchUserProfileRequest();
        fetchUserProfileRequest.setAccountId(merchantUser.getMerchantUserId());

        return dexterClient.fetchUserProfile(fetchUserProfileRequest, DexterConstants.DEXTER_CLIENT_ID, requestId);
    } catch (Exception e) {
        log.error("Error while calling fetching user profile from Dexter Service for accountId {}", merchantUser.getMerchantUserId(), e);
        return null;
    }
}
```

**Required Change**: Multiple Dexter calls need to be made in parallel using CompletableFuture or similar async mechanism.

### 3. Profile Service Response Caching

**Current Issue**: Profile data fetched on every request
**Target Reduction**: 100ms savings

**File**: `pinaka-service/src/main/java/com/flipkart/fintech/lending/orchestrator/service/PersonalLoanOrchestrator.java`

**Current Code**:
```java
if (LeadDetails.LeadState.valueOf(applicationDataResponse.getApplicationState()).equals(LeadDetails.LeadState.CREATE_PROFILE_END)) {
    ProfileCRUDResponse profileCRUDResponse = objectMapper.convertValue(applicationDataResponse.getApplicationData().get("createProfile"), ProfileCRUDResponse.class);
    profile = profileService.getProfileById(profileCRUDResponse.getProfileId());
    if (Objects.isNull(profile)) {
        throw new PinakaException("Profile fetch by ID failed: " + profileCRUDResponse.getProfileId());
    }
    readRepair(profile, applicationDataResponse);
}
```

**Required Change**: Add caching layer before `profileService.getProfileById()` call.

### 4. Winterfell Application Creation Optimization

**Current Issue**: Synchronous application creation taking 100ms
**Target Reduction**: 30-50ms savings

**File**: `ams-connector/src/main/java/com/flipkart/ams/WinterfellUtils.java`

**Current Code**:
```java
@HystrixCommand(
        groupKey = GROUP_KEY,
        commandKey = "PL_CREATE_APPLICATION",
        threadPoolKey = THREAD_POOL_KEY,
        commandProperties = {
                @HystrixProperty(name = EXECUTION_ISOLATION_STRATEGY, value = "THREAD"),
                @HystrixProperty(name = CIRCUIT_BREAKER_ENABLED, value = "true"),
                @HystrixProperty(name = EXECUTION_TIMEOUT_ENABLED, value = "true"),
                @HystrixProperty(
                        name = EXECUTION_ISOLATION_THREAD_TIMEOUT_IN_MILLISECONDS,
                        value = "5000"),
                @HystrixProperty(name = REQUEST_CACHE_ENABLED, value = "false")
        },
        threadPoolProperties = {
                @HystrixProperty(name = CORE_SIZE, value = "65"),
                @HystrixProperty(name = MAX_QUEUE_SIZE, value = "100")
        })
public ApplicationResponse createApplication(CreateApplicationRequest createApplicationRequest) throws WinterfellServiceException {
    try {
        return winterfellClient.createApplication(tenant.name(), createApplicationRequest);
    } catch (Exception e) {
        log.error("Error while creating application at Winterfell. userId: {}, applicationType: {}. error: {}",
                createApplicationRequest.getExternalUserId(), createApplicationRequest.getApplicationType(),
                e.getMessage(), e);
        throw new WinterfellServiceException(e.getMessage());
    }
}
```

**Required Change**: Connection pooling, request batching, or async processing needs to be implemented.

### 5. Active Applications Lookup Optimization

**Current Issue**: Multiple sequential calls to Winterfell
**Target Reduction**: 10-15ms savings

**File**: `pinaka-service/src/main/java/com/flipkart/fintech/lending/orchestrator/service/PersonalLoanOrchestrator.java`

**Current Code**:
```java
ActiveApplicationsResponse activeApplicationsResponse = applicationService.findActiveApplicationsForProductTypeV2(merchantUser, PERSONAL_LOAN);
PlLandingPageStates pageState = plLandingPageOrchestrator.resolveLandingPage(pageRequest, merchantId, userAgent, requestId, activeApplicationsResponse);
switch (pageState){
  case REPEAT_LOAN_LP:
    return pageState.getPageActionResponse();
  default:
    Optional<ApplicationDataResponse> activeApplication = applicationService.getLatestActiveApplicationFromList(
            activeApplicationsResponse.getApplications().stream().filter(applicationData -> applicationData.getProductType().equals(PERSONAL_LOAN.name()))
                    .collect(Collectors.toList()), merchantUser, PERSONAL_LOAN);
    return getStatusV2(pageRequest, merchantUser, requestId, userAgent, activeApplication);
}
```

**Required Change**: Batch the multiple Winterfell calls or cache active applications response.

### 6. Lead Service Processing Optimization

**Current Issue**: Duplicate active application lookups
**Target Reduction**: 10-20ms savings

**File**: `pinaka-service/src/main/java/com/flipkart/fintech/lead/service/LeadServiceImpl.java`

**Current Code**:
```java
@Override
public LeadResponse getCurrentLeadStatus(MerchantUser merchantUser, String requestId) throws PinakaException {
    try {
        Optional<ApplicationDataResponse> activeApplication = applicationService.findLatestActiveApplicationV2(
            merchantUser, productType);
        if (activeApplication.isPresent()) {
            ApplicationDataResponse applicationDataResponse = activeApplication.get();
            String applicationId = applicationDataResponse.getApplicationId();
            String monthlyIncome = getMonthlyIncomeFromApplication(applicationDataResponse);
            log.warn("Cannot create another lead as there is already an active lead for MerchantUser {}, " +
                "resuming current lead {}", merchantUser, applicationId);
```

**Required Change**: Reuse already fetched active applications data instead of making duplicate calls.

### 7. HTTP Client Configuration Optimization

**Current Issue**: Default timeouts and no connection pooling
**Target Reduction**: 20-30ms savings

**File**: `pinaka-common/src/main/java/com/flipkart/fintech/pinaka/common/dexterClient/DexterClientImpl.java`

**Current Code**:
```java
@Inject
public DexterClientImpl(Client client, @Named("dexterWebTarget") WebTarget fkWebTarget, AuthTokenService authTokenService, DexterClientConfig dexterClientConfig) {
    this.fkWebTarget = fkWebTarget;
    this.authTokenService = authTokenService;
    this.dexterClientConfig = dexterClientConfig;
}
```

**File**: `pinaka-service/src/main/java/com/flipkart/fintech/pinaka/service/application/PinakaModule.java`

**Current Code**:
```java
@Provides
@Singleton
public Client provideClient() {
    Client client = ClientBuilder.newClient();
    client.property(ClientProperties.CONNECT_TIMEOUT, 60000);
    client.property(ClientProperties.READ_TIMEOUT, 60000);
    return client;
}
```

**Required Change**: Add connection pooling and optimize timeout values for better performance.

## Implementation Priority

### High Priority (Immediate Impact)
1. **Profile Service Caching** - 100ms savings
2. **Database Query Optimization** - 80ms savings  
3. **Dexter Calls Parallelization** - 35ms savings

### Medium Priority
4. **Winterfell Optimization** - 30ms savings
5. **HTTP Client Configuration** - 20ms savings
6. **Duplicate Call Elimination** - 15ms savings

## Expected Results

### Current Performance Range
- **Worst Case**: 492ms (Trace-3fc1f2)
- **Average Case**: 348ms (across 6 traces)
- **Best Case**: 162ms (Trace-45d7f6)
- **Latest Trace**: 442ms (Trace-5ac01b-16:52:26)

### Optimization Targets
- **After High Priority Changes**: ~200-250ms (30-40% improvement)
- **After All Changes**: ~120-150ms (65-70% improvement)
- **Target Consistency**: 90% of requests under 200ms

### Validation from Best-Case Trace
The 162ms trace (Trace-45d7f6) proves that sub-200ms performance is achievable, indicating that the slower traces have addressable bottlenecks rather than fundamental architectural limitations.

## Updated Insights from Multi-Trace Analysis

### Consistent Bottlenecks Across All Traces
1. **Profile Service Calls**: 93-109ms (consistently 20-25% of total time)
2. **Winterfell Application Creation**: 100-110ms (consistently 20-25% of total time)
3. **Dexter User Profile Calls**: 58-69ms (consistently 13-15% of total time)

### Performance Variability Factors
- **3x performance difference** between best (162ms) and worst (492ms)
- **Conditional processing paths** likely cause variance
- **External service performance** fluctuations impact overall latency

---
*Document prepared for technical review*
*Based on trace analysis: 6 production traces from June 1-2, 2025*
*Latest trace: 27fde97aa0cac29832aed957aa5ac01b (442.42ms)*
*Date: January 25, 2025*
