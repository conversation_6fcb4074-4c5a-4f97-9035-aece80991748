package com.flipkart.fintech.pinaka.library.entities;

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class CAISHolderDetails {

    @JacksonXmlProperty(localName = "Date_of_birth")
    private String dateOfBirth;

    @JacksonXmlProperty(localName = "Gender_Code")
    private String genderCode;
}
