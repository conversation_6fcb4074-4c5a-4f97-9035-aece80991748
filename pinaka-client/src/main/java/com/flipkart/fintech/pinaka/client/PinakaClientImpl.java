package com.flipkart.fintech.pinaka.client;

import com.flipkart.fintech.pandora.api.model.request.uat.ApplicationCreationRequest;
import com.flipkart.fintech.pandora.api.model.request.uat.LenderConfirmationRequest;
import com.flipkart.fintech.pandora.api.model.response.uat.ApplicationResponse;
import com.flipkart.fintech.pinaka.api.request.*;
import com.flipkart.fintech.pinaka.api.response.*;
import com.flipkart.fintech.pinaka.api.response.v3.CreateLoanResponse;
import com.google.inject.Inject;
import org.glassfish.jersey.client.ClientProperties;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.GenericType;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.Base64;
import java.util.List;

import static com.flipkart.fintech.pinaka.client.Utils.addTraceId;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 04/09/17.
 */
public class PinakaClientImpl implements PinakaClient {

    private final PinakaClientConfig clientConfig;
    private WebTarget webTarget;
    private static final String ERROR_PINAKA = "error response from pinaka service";

    @Inject
    public PinakaClientImpl(PinakaClientConfig clientConfig, Client client){

        this.clientConfig = clientConfig;
        this.webTarget = client.target(clientConfig.getUrl()).path(Constants.APP_CONTEXT_PATH);
    }

    @Override
    public ApplicationCreateResponse createApplication(
            ApplicationCreateRequest applicationCreateRequest, String merchant) throws PinakaClientException{
        Response response = null;
        ApplicationCreateResponse applicationCreateResponse = null;
        try{
            Invocation.Builder invocationBuilder = webTarget.path(Constants.CREATE_APPLICATION_PATH).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchant);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchant));
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(applicationCreateRequest));
            if(response.getStatus() == 201){
                applicationCreateResponse = response.readEntity(ApplicationCreateResponse.class);
            }else{
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        }catch(Exception e){
            throw new PinakaClientException(e);
        }finally {
            if(response != null){response.close();}
        }
        return applicationCreateResponse;
    }

    @Override
    public CreateLoanResponse createLoan(String trackingId, String merchant, String accountId) throws PinakaClientException {
        Response response = null;
        CreateLoanResponse createLoanResponse = null;
        String path = String.format(Constants.CREATE_LOAN_PATH, trackingId);

        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchant);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchant));
            addTraceId(invocationBuilder);
            invocationBuilder.header(Constants.X_ACCOUNT_ID, accountId);
            response = invocationBuilder.post(null);
            if(response.getStatus() == 200){
                createLoanResponse = response.readEntity(CreateLoanResponse.class);
            }else{
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        }catch (Exception e){
            throw new PinakaClientException(e);
        }finally {
            if (response != null){response.close();}
        }
        return createLoanResponse;
    }

    @Override
    public ApplicationUpdateResponse updateApplication(ApplicationUpdateRequest updateRequest,
                                                       String merchant) throws PinakaClientException {
        Response response = null;
        ApplicationUpdateResponse updateResponse = null;
        String path = String.format(Constants.UPDATE_APPLICATION_PATH, updateRequest.getTrackingId());

        try{
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchant);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchant));
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(updateRequest));
            if(response.getStatus() == 200){
                updateResponse = response.readEntity(ApplicationUpdateResponse.class);
            }else{
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        }catch(Exception e){
            throw new PinakaClientException(e);
        }finally {
            if(response != null){response.close();}
        }
        return updateResponse;
    }


    @Override
    public void cancelFluxEvent(String trackingId, String eventName, String merchant) throws PinakaClientException{
        Response response = null;
        String path = String.format(Constants.CANCEL_EVENT_PATH, trackingId, eventName);
        try{
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchant);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchant));
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(null);
            if(response.getStatus() != 200){
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        }catch(Exception e){
            throw new PinakaClientException(e);
        }finally {
            if(response != null){response.close();}
        }
    }

    @Override
    public void applicationPostProcessingTask(String trackingId, String merchant) throws PinakaClientException {
        Response response = null;
        String path = String.format(Constants.POST_PROCESSING_TASK_PATH, trackingId);
        try{
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchant);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchant));
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(null);
            if(response.getStatus() != 200){
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        }catch(Exception e){
            throw new PinakaClientException(e);
        }finally {
            if(response != null){response.close();}
        }
    }

    private String generateAuthToken(String merchant) {
        StringBuilder keyReverse = new StringBuilder(merchant);
        String input = merchant+":"+keyReverse.reverse();
        return Base64.getEncoder().encodeToString(input.getBytes());
    }


    @Override
    public BnplApplicationCreateParameters fetchBnplApplicationParameters(String merchant, String userId)
            throws PinakaClientException {
        Response response = null;
        BnplApplicationCreateParameters bnplApplicationCreateParameters = null;
        String path = String.format(Constants.FETCH_BNPL_APPLICATION_DATA_PATH, userId);
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchant);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchant));
            addTraceId(invocationBuilder);
            response = invocationBuilder.get();
            if (response.getStatus() == 200) {
                bnplApplicationCreateParameters = response.readEntity(BnplApplicationCreateParameters.class);
            } else if (response.getStatus() != 404) {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return bnplApplicationCreateParameters;
    }

    @Override
    public void insightsGenerated(String merchant, String userId)
            throws PinakaClientException {
        Response response = null;
        String path = String.format(Constants.INSIGHTS_GENERATED_DATA_PATH, userId);
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchant);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchant));
            addTraceId(invocationBuilder);
            response = invocationBuilder.get();
            if (response.getStatus() != 200) {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }


    @Override
    public BnplApplicationUpdateResponse updateBnplApplication(BnplApplicationUpdateRequest updateRequest,
                                                           String merchant) throws PinakaClientException {
        Response response = null;
        BnplApplicationUpdateResponse updateResponse = null;
        String path = String.format(Constants.UPDATE_BNPL_APPLICATION_DATA_PATH, updateRequest.getTrackingId());

        try{
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchant);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchant));
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(updateRequest));
            if(response.getStatus() == 200){
                updateResponse = response.readEntity(BnplApplicationUpdateResponse.class);
            }else{
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        }catch(Exception e){
            throw new PinakaClientException(e);
        }finally {
            if(response != null){response.close();}
        }
        return updateResponse;
    }


    @Override
    public List<FetchApplicationResponse> fetchAllActiveApplications(String merchant, String userId)
            throws PinakaClientException {
        Response response = null;
        List<FetchApplicationResponse> applications = null;
        String path = String.format(Constants.FETCH_ALL_APPLICATIONS_PATH, userId);
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchant);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchant));
            addTraceId(invocationBuilder);
            response = invocationBuilder.get();
            if (response.getStatus() == 200) {
                applications = response.readEntity(new GenericType<List<FetchApplicationResponse>>() {});
            } else if (response.getStatus() != 404) {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return applications;
    }


    @Override
    public void sendCommunication(String trackingId, String merchantId, AsyncCommRequest asyncCommRequest)
            throws PinakaClientException {
        Response response = null;
        String path = String.format(Constants.SEND_COMMUNICATION_PATH, trackingId);
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(asyncCommRequest));
            if (response.getStatus() != 200) {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }

    @Override
    public AccountAccessCodeResponse generateAccountAccessCode(String trackingId, String merchant, String accountId)
            throws PinakaClientException {
        Response response = null;
        AccountAccessCodeResponse checkResponse = null;
        String path = String.format(Constants.GENERATE_ACCESS_CODE_PATH, trackingId);

        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchant);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchant));
            invocationBuilder.header(Constants.X_ACCOUNT_ID, accountId);
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(null);
            if(response.getStatus() == 200){
                checkResponse = response.readEntity(AccountAccessCodeResponse.class);
            }else{
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        }catch (Exception e){
            throw new PinakaClientException(e);
        }finally {
            if (response != null){response.close();}
        }
        return checkResponse;
    }

    @Override
    public FormFieldsResponse getFormFields(String trackingId, String merchant) throws PinakaClientException {
        Response response = null;
        FormFieldsResponse formFieldsResponse = null;
        String path = String.format(Constants.FETCH_FORM_FIELDS_PATH, trackingId);
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchant);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchant));
            addTraceId(invocationBuilder);
            response = invocationBuilder.get();
            if (response.getStatus() == 200) {
                formFieldsResponse = response.readEntity(FormFieldsResponse.class);
            } else if (response.getStatus() != 404) {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return formFieldsResponse;
    }

    @Override
    public BorrowerDetailsResponse fetchBorrowerDetails(String lenderUserRefId)
        throws PinakaClientException {
        Response response = null;
        BorrowerDetailsResponse borrowerDetailsResponse = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(
                Constants.FETCH_BORROWER_DETAILS_PATH)
                .queryParam(Constants.LENDER_USER_REF_ID_PARAM, lenderUserRefId)
                .request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, clientConfig.getMerchant());
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(clientConfig.getMerchant()));
            addTraceId(invocationBuilder);
            response = invocationBuilder.get();
            if (response.getStatus() == 200) {
                borrowerDetailsResponse = response.readEntity(BorrowerDetailsResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return borrowerDetailsResponse;
    }

    @Override
    public LenderAccountDetailsResponse fetchLenderAccountDetails(String userId,
                                                                  String productType) throws PinakaClientException {
        Response response = null;
        LenderAccountDetailsResponse lenderAccountDetailsResponse = null;
        String path = String.format(Constants.FETCH_LENDER_ACCOUNT_DETAILS_PATH, userId, productType);
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path)
                .request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, clientConfig.getMerchant());
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(clientConfig.getMerchant()));
            addTraceId(invocationBuilder);
            response = invocationBuilder.get();
            if (response.getStatus() == 200) {
                lenderAccountDetailsResponse = response.readEntity(LenderAccountDetailsResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return lenderAccountDetailsResponse;
    }

    public AutopayDetailsResponse fetchAutopayDetails(AutopayDetailsRequest autopayDetailsRequest, String merchantId) throws PinakaClientException {
        Response response = null;
        AutopayDetailsResponse autopayDetailsResponse = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(Constants.FETCH_AUTOPAY_DETAILS_PATH)
                    .request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(autopayDetailsRequest));
            if (response.getStatus() == 200) {
                autopayDetailsResponse = response.readEntity(AutopayDetailsResponse.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return autopayDetailsResponse;
    }


    @Override
    public WhitelistCreateResponse createWhitelist(WhitelistCreateRequest whitelistCreateRequest) throws PinakaClientException {
        Response response = null;
        WhitelistCreateResponse whitelistCreateResponse = null;
        String path = String.format(Constants.WHITELIST_CREATE);
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, clientConfig.getMerchant());
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(clientConfig.getMerchant()));
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(whitelistCreateRequest));
            if (response.getStatus() == 201) {
                whitelistCreateResponse = response.readEntity(WhitelistCreateResponse.class);
            } else if (response.getStatus() != 404) {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return whitelistCreateResponse;
    }

    @Override
    public LenderConfigResponse fetchLenderConfig(String userId, String productType) throws PinakaClientException {
            Response response = null;
            LenderConfigResponse lenderConfig = null;
            String path = String.format(Constants.FETCH_LENDER_CONFIG_PATH, userId, productType);
            try {
                Invocation.Builder invocationBuilder = webTarget.path(path)
                        .request(MediaType.APPLICATION_JSON_TYPE);
                invocationBuilder.header(Constants.X_MERCHANT_ID, clientConfig.getMerchant());
                invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
                invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(clientConfig.getMerchant()));
                addTraceId(invocationBuilder);
                response = invocationBuilder.get();
                if (response.getStatus() == 200) {
                    lenderConfig = response.readEntity(LenderConfigResponse.class);
                } else {
                    throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
                }
            } catch (Exception e) {
                throw new PinakaClientException(e);
            } finally {
                if (response != null) {
                    response.close();
                }
            }
            return lenderConfig;
    }

    @Override
    public WhitelistResponse fetchWhitelist(String whitelistName, String product) throws PinakaClientException {
        Response response = null;
        WhitelistResponse whitelistResponse = null;
        String path = String.format(Constants.FETCH_WHITELIST, whitelistName, product);
        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, clientConfig.getMerchant());
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(clientConfig.getMerchant()));
            addTraceId(invocationBuilder);
            response = invocationBuilder.get();
            if (response.getStatus()/100 == 2) {
                whitelistResponse = response.readEntity(WhitelistResponse.class);
            } else if (response.getStatus() != 404) {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return whitelistResponse;
    }



    @Override
    public AltDataDetails fetchAltDataDetails(String productType, String merchantId, String accountId) throws PinakaClientException {
        Response response = null;
        AltDataDetails altDataDetails = null;
        try {
            Invocation.Builder invocationBuilder = webTarget.path(String.format(Constants.FETCH_ALT_DATA_DETAILS, productType)).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            invocationBuilder.header(Constants.X_ACCOUNT_ID, accountId);
            addTraceId(invocationBuilder);
            response = invocationBuilder.get();

            if (response.getStatus() == 200) {
                altDataDetails = response.readEntity(AltDataDetails.class);
            } else {
                throw new PinakaClientException(ERROR_PINAKA + response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return altDataDetails;
    }

    @Override
    public PincodeCreateResponse createPincode(
            PincodeRequest pincodeRequest, String merchantId) throws PinakaClientException {
        Response response = null;
        PincodeCreateResponse pincodeCreateResponse = null;
        try{
            Invocation.Builder invocationBuilder = webTarget.path(Constants.PINCODE_CREATE_PATH).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(pincodeRequest));
            if(response.getStatus() == 200){
                pincodeCreateResponse = response.readEntity(PincodeCreateResponse.class);
            }else{
                throw new PinakaClientException("error response from pinaka service while creating pincode " +
                        response.readEntity(String.class));
            }
        }catch(Exception e){
            throw new PinakaClientException(e);
        }finally {
            if(response != null){response.close();}
        }
        return pincodeCreateResponse;
    }

    @Override
    public PincodeFetchResponse fetchPincode(
            PincodeRequest pincodeRequest, String merchantId) throws PinakaClientException {
        Response response = null;
        PincodeFetchResponse pincodeFetchResponse = null;
        try{
            Invocation.Builder invocationBuilder = webTarget.path(Constants.PINCODE_FETCH_PATH).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(pincodeRequest));
            if(response.getStatus() == 200){
                pincodeFetchResponse = response.readEntity(PincodeFetchResponse.class);
            }else{
                throw new PinakaClientException("error response from pinaka service while fetching pincode " +
                        response.readEntity(String.class));
            }
        }catch(Exception e){
            throw new PinakaClientException(e);
        }finally {
            if(response != null){response.close();}
        }
        return pincodeFetchResponse;
    }

    @Override
    public String removePincode(
            PincodeRequest pincodeRequest, String merchantId) throws PinakaClientException {
        Response response = null;
        String finalResponse = null;
        try{
            Invocation.Builder invocationBuilder = webTarget.path(Constants.PINCODE_REMOVE_PATH).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(pincodeRequest));
            if(response.getStatus() == 200){
                finalResponse = response.readEntity(String.class);
            }else{
                throw new PinakaClientException("error response from pinaka service while fetching pincode " +
                        response.readEntity(String.class));
            }
        }catch(Exception e){
            throw new PinakaClientException(e);
        }finally {
            if(response != null){response.close();}
        }
        return finalResponse;
    }

    @Override
    public ApplicationResponse createAndSubmitApplication(ApplicationCreationRequest applicationCreationRequest, String merchantId) throws PinakaClientException {
        Response response = null;
        ApplicationResponse finalResponse;
        try{
            Invocation.Builder invocationBuilder = webTarget.property(ClientProperties.READ_TIMEOUT, 60000).path(Constants.CREATE_AND_SUBMIT_APPLICATION).
                    request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(applicationCreationRequest));
            if(response.getStatus() == 200){
                finalResponse = response.readEntity(ApplicationResponse.class);
            }else{
                throw new PinakaClientException("error response from pinaka service while creating and submitting application" +
                        response.readEntity(String.class));
            }
        }catch(Exception e){
            throw new PinakaClientException(e);
        }finally {
            if(response != null){response.close();}
            webTarget.property(ClientProperties.READ_TIMEOUT, null);
        }
        return finalResponse;
    }

    @Override
    public ApplicationResponse lenderConfirmation(LenderConfirmationRequest lenderConfirmationRequest, String merchantId) throws PinakaClientException {
        Response response = null;
        ApplicationResponse finalResponse;
        try{
            Invocation.Builder invocationBuilder = webTarget.property(ClientProperties.READ_TIMEOUT, 30000).path(Constants.LENDER_CONFIRMATION)
                   .request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(Constants.X_MERCHANT_ID, merchantId);
            invocationBuilder.header(Constants.X_CLIENT_ID, clientConfig.getClient());
            invocationBuilder.header(Constants.X_AUTHORIZATION, generateAuthToken(merchantId));
            addTraceId(invocationBuilder);
            response = invocationBuilder.post(Entity.json(lenderConfirmationRequest));
            if(response.getStatus() == 200){
                finalResponse = response.readEntity(ApplicationResponse.class);
            }else{
                throw new PinakaClientException("error response from pinaka service while doing lender confirmation" +
                        response.readEntity(String.class));
            }
        }catch(Exception e){
            throw new PinakaClientException(e);
        }finally {
            if(response != null){response.close();}
            webTarget.property(ClientProperties.READ_TIMEOUT, null);
        }
        return finalResponse;
    }
}
