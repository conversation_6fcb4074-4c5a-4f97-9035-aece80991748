package com.flipkart.fintech.pinaka.client.v6;

import com.flipkart.fintech.pinaka.api.request.v6.*;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.BureauActionRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.FormSubmitRequestV2;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.pinaka.api.response.SecurityKeyResponse;
import com.flipkart.fintech.pinaka.api.response.v6.*;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.rome.datatypes.request.fintech.calm.IfscSearchRequest;
import com.flipkart.rome.datatypes.response.fintech.calm.IfscSearchResponse;

import java.io.IOException;
import java.io.InputStream;

public interface PinakaClientV6 {
    PageActionResponse applyNow(LandingPageRequest landingPageRequest, String merchantId, String requestId, String userAgent) throws PinakaClientException;

    PageActionResponse resume(ResumePageRequest resumePageRequest, String merchantId, String requestId, String userAgent) throws PinakaClientException;

    PageActionResponse submitUserAction(SubmitRequest submitRequest, String merchantId, String requestId) throws PinakaClientException;

    WebhooksResponse submitLenderEvent(AxisWebhooksRequest webhooksRequest, String requestId) throws PinakaClientException;

    WebhooksResponse submitSandboxLenderEvent(SandboxWebhooksRequest webhooksRequest, String requestId) throws PinakaClientException;

    KfsResponse getKfsDetails(KfsRequest kfsRequest, String merchantId, String requestId) throws PinakaClientException;

    StatusResponse getStatus(String applicationId, String requestId, String accountId, String merchantId) throws PinakaClientException;

    SearchResponse getEmpSearchSug(String prefix, String merchantId) throws PinakaClientException;

    FetchBulkDataResponse fetchBulkData(FetchBulkDataRequest fetchBulkDataRequest, String merchantId, String requestId) throws PinakaClientException;

    SecurityKeyResponse getEncryptionKey(String requestId, String merchantId) throws PinakaClientException;
    PageActionResponse submit(UserActionRequest submitRequest, String requestId, String userAgent) throws PinakaClientException;

    StateChangeResponse hasStateChanged(StateChangeRequest stateChangeRequest, String requestId)
            throws PinakaClientException;

    FetchBulkDataResponseV2 fetchBulkDataV2(PageServiceRequest fetchBulkDataRequest, String requestId)
            throws PinakaClientException, IOException;

    IfscSearchResponse searchIfsc(IfscSearchRequest ifscSearchRequest, String requestId)
            throws PinakaClientException;

    PageActionResponse dummyResponse(String url , String requestId) throws PinakaClientException;

    DocumentUploadResponse documentUpload(String accountId, String documentType, String applicationId, String encryptedSymmetricKey, String publicKeyRefId,
                                          InputStream documentInputStream, String documentPassword,
                                          String requestId) throws PinakaClientException;

    UserOfferDataResponse getUserOfferData(String applicationId, String smUserId, String lender) throws PinakaClientException;
    PincodeDetailsResponse checkPincodeExistence(String pincode, String merchantId) throws PinakaClientException;
}
