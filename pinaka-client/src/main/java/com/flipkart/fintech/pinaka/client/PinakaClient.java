package com.flipkart.fintech.pinaka.client;

import com.flipkart.fintech.pandora.api.model.request.uat.ApplicationCreationRequest;
import com.flipkart.fintech.pandora.api.model.request.uat.LenderConfirmationRequest;
import com.flipkart.fintech.pandora.api.model.response.uat.ApplicationResponse;
import com.flipkart.fintech.pinaka.api.request.*;
import com.flipkart.fintech.pinaka.api.response.*;
import com.flipkart.fintech.pinaka.api.response.v3.CreateLoanResponse;

import java.util.List;

/**
 * Created by sujee<PERSON><PERSON>.r on 04/09/17.
 */
public interface PinakaClient {

    ApplicationCreateResponse createApplication(
            ApplicationCreateRequest createRequest, String merchant) throws PinakaClientException;

    CreateLoanResponse createLoan(
            String trackingId, String merchant, String accountId) throws PinakaClientException;

    ApplicationUpdateResponse updateApplication(
            ApplicationUpdateRequest updateRequest, String merchant) throws PinakaClientException;

    void cancelFluxEvent(String trackingId, String eventName, String merchant) throws PinakaClientException;

    void applicationPostProcessingTask(String trackingId, String merchant) throws PinakaClientException;

    BnplApplicationCreateParameters fetchBnplApplicationParameters(String merchant, String userId)
            throws PinakaClientException;

    void insightsGenerated(String merchant, String userId)
            throws PinakaClientException;

    BnplApplicationUpdateResponse updateBnplApplication(
            BnplApplicationUpdateRequest updateRequest, String merchant) throws PinakaClientException;

    List<FetchApplicationResponse> fetchAllActiveApplications(String merchant, String userId)
            throws PinakaClientException;

    void sendCommunication(String trackingId, String merchantId, AsyncCommRequest asyncCommRequest)
            throws PinakaClientException;

    AccountAccessCodeResponse generateAccountAccessCode(String trackingId, String merchant, String accountId)
            throws PinakaClientException;

    FormFieldsResponse getFormFields(String trackingId, String merchant) throws PinakaClientException;

    BorrowerDetailsResponse fetchBorrowerDetails(String lenderUserRefId) throws PinakaClientException;

    LenderAccountDetailsResponse fetchLenderAccountDetails(String userId, String productType) throws PinakaClientException;

    AutopayDetailsResponse fetchAutopayDetails(AutopayDetailsRequest autopayDetailsRequest, String merchantId) throws PinakaClientException;

    WhitelistCreateResponse createWhitelist(WhitelistCreateRequest whitelistCreateRequest) throws PinakaClientException;

    LenderConfigResponse fetchLenderConfig(String userId, String productType) throws PinakaClientException;

    WhitelistResponse fetchWhitelist(String whitelistName, String product) throws PinakaClientException;

    AltDataDetails fetchAltDataDetails(String productType, String merchantId, String accountId) throws PinakaClientException;

    PincodeCreateResponse createPincode(PincodeRequest pincodeRequest, String merchantId) throws PinakaClientException;

    PincodeFetchResponse fetchPincode(PincodeRequest pincodeRequest, String merchantId) throws PinakaClientException;

    String removePincode(PincodeRequest pincodeRequest, String merchantId) throws PinakaClientException;

    ApplicationResponse createAndSubmitApplication(ApplicationCreationRequest applicationCreationRequest, String merchantId) throws PinakaClientException;

    ApplicationResponse lenderConfirmation(LenderConfirmationRequest lenderConfirmationRequest, String merchantId) throws PinakaClientException;
}
