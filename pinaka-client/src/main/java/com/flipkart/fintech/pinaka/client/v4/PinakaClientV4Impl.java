package com.flipkart.fintech.pinaka.client.v4;

import com.flipkart.fintech.pinaka.api.response.FetchApplicationResponse;
import com.flipkart.fintech.pinaka.client.Constants;
import com.flipkart.fintech.pinaka.client.PinakaClientConfig;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.google.inject.Inject;

import javax.ws.rs.client.Client;
import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.GenericType;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.List;

import static com.flipkart.fintech.pinaka.client.Utils.addTraceId;

/**
 * <AUTHOR>
 * @since 04/12/20.
 */
public class PinakaClientV4Impl implements PinakaClientV4 {

    private WebTarget webTarget;

    @Inject
    public PinakaClientV4Impl(PinakaClientConfig clientConfig, Client client) {

        this.webTarget = client.target(clientConfig.getUrl()).path(Constants.APP_CONTEXT_PATH);
    }

    @Override
    public List<FetchApplicationResponse> fetchApplicationForKycPrePopulation(String merchantAccountId, String financialProvider) throws PinakaClientException {
        Response                       response = null;
        List<FetchApplicationResponse> fetchApplicationResponseList;
        String                         path     = String.format(Constants.FETCH_PINAKA_APPLICATION_PATH, merchantAccountId, financialProvider);

        try {
            Invocation.Builder invocationBuilder = webTarget.path(path).queryParam("financial_provider", financialProvider).request(MediaType.APPLICATION_JSON_TYPE);
            addTraceId(invocationBuilder);
            response = invocationBuilder.get();
            if (response.getStatus() == 200) {
                fetchApplicationResponseList = response.readEntity(new GenericType<List<FetchApplicationResponse>>() {
                });
            } else {
                throw new PinakaClientException("Error response from Pinaka service" +
                        response.readEntity(String.class));
            }
        } catch (Exception e) {
            throw new PinakaClientException(e);
        } finally {
            if (response != null) {
                response.close();
            }
        }
        return fetchApplicationResponseList;
    }
}
