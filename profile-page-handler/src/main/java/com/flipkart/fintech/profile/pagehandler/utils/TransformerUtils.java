package com.flipkart.fintech.profile.pagehandler.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.CustomLog;
import org.apache.commons.io.IOUtils;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@CustomLog
public class TransformerUtils {
    private TransformerUtils() {
    }

    public static String readFileasString(String fileName) {
        try (InputStream inputStream = TransformerUtils.class.getClassLoader().getResourceAsStream(fileName)) {
            return IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("Widget Template file reading failed  with error : {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    public static Map<String, String> readFileAsMapOfString(String fileName) {
        try {
            return ObjectMapperUtil.get().readValue(TransformerUtils.class.getClassLoader().getResourceAsStream(fileName),
                    new TypeReference<Map<String, String>>() {
                    });
        } catch (Exception e) {
            log.error("Widget Template file reading failed  with error : {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }
}
