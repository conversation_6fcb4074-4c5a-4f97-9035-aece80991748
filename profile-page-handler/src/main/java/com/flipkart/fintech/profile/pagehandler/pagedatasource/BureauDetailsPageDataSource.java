package com.flipkart.fintech.profile.pagehandler.pagedatasource;


import com.flipkart.fintech.profile.pagehandler.pagedatasource.responses.BureauDetailsPageDataSourceResponse;
import com.flipkart.fintech.profile.client.ProfileClientConfiguration;
import com.flipkart.fintech.profile.client.ProfileClientImpl;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;


import javax.inject.Inject;
import java.util.Objects;

public class BureauDetailsPageDataSource implements BureauPageDataSource<BureauDetailsPageDataSourceResponse> {
    private final ProfileClientImpl profileClient;
    private EncryptionData encryptionData;
    @Inject
    public BureauDetailsPageDataSource(ProfileClientConfiguration profileClientConfiguration, EncryptionData encryptionData){
        this.profileClient  = new ProfileClientImpl(profileClientConfiguration);
        this.encryptionData = encryptionData;
    }
    
    public BureauDetailsPageDataSourceResponse getData(String merchantUserId ,String smUserId) {
        BureauDetailsPageDataSourceResponse bureauDetailsPageDataSourceResponse = new BureauDetailsPageDataSourceResponse();
        ProfileDetailedResponse profile = profileClient.getProfile(merchantUserId,smUserId, false);
        if (Objects.nonNull(profile)){
            bureauDetailsPageDataSourceResponse.setProfile(profile);
        }
        bureauDetailsPageDataSourceResponse.setEncryptionData(encryptionData);
        return bureauDetailsPageDataSourceResponse;
    }
}