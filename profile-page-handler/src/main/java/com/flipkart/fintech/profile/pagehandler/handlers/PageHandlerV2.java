package com.flipkart.fintech.profile.pagehandler.handlers;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.api.request.v6.PageServiceRequest;
import com.flipkart.fintech.pinaka.api.request.v6.SlotInfo;
import com.flipkart.fintech.pinaka.api.response.idfc.WidgetEntity;
import com.flipkart.fintech.pinaka.client.PinakaCalvinClientConfig;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.profile.client.ProfileClientConfiguration;
import com.flipkart.fintech.profile.config.ProfileServiceConfig;
import com.flipkart.fintech.profile.model.bureauLender;
import com.flipkart.fintech.profile.pagehandler.pagedatasource.CreditScorePageDataSource;
import com.flipkart.fintech.profile.pagehandler.pagedatasource.responses.CreditScorePageDataSourceResponse;
import com.flipkart.fintech.profile.pagehandler.widgettransformer.BureauFormWidgetTransformers;
import com.flipkart.fintech.profile.pagehandler.widgettransformer.CreditScoreDetailsFormTransformer;
import com.flipkart.fintech.profile.pagehandler.widgettransformer.CreditScoreWidgetTransformer;
import com.flipkart.fintech.profile.pagehandler.widgettransformer.MarkUpWidgetTransformer;
import com.flipkart.fintech.profile.response.BureauDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import com.flipkart.rome.datatypes.response.page.v4.WidgetData;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import com.google.inject.Inject;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

import javax.annotation.Nullable;

import javax.ws.rs.client.Client;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;

public class PageHandlerV2 {
    private final ProfileClientConfiguration profileClientConfiguration;
    private final EncryptionData encryptionData;
    private final PinakaCalvinClientConfig pinakaCalvinClientConfig;
    private final ExecutorService executorService;
    private final CreditScoreWidgetTransformer creditScoreWidgetTransformer;
    private final ProfileServiceConfig profileServiceConfig;
    private final DynamicBucket dynamicBucket ;

    @Inject
    public PageHandlerV2(ProfileClientConfiguration profileClientConfiguration, EncryptionData encryptionData, PinakaCalvinClientConfig pinakaClientConfig, ExecutorService executorService, ProfileServiceConfig profileServiceConfig
            , Client client, DynamicBucket dynamicBucket) {
        this.profileClientConfiguration = profileClientConfiguration;
        this.encryptionData = encryptionData;
        this.pinakaCalvinClientConfig = pinakaClientConfig;
        this.executorService = executorService;
        this.profileServiceConfig = profileServiceConfig;
        this.dynamicBucket = dynamicBucket;
        this.creditScoreWidgetTransformer = new CreditScoreWidgetTransformer(encryptionData, pinakaCalvinClientConfig, executorService, client, profileServiceConfig);
    }

    public List<WidgetEntity> createPageHandlerResponse(PageServiceRequest pageServiceRequest)
            throws PinakaException, IOException, PinakaClientException, ParseException {
        List<WidgetEntity> list = new ArrayList<>();
        for (SlotInfo slotInfo : pageServiceRequest.getSlotInfoList()) {
            WidgetEntity widgetEntity = getWidgetEntity(slotInfo, pageServiceRequest);
            if (Objects.nonNull(widgetEntity)) {
                list.add(widgetEntity);
            }
        }
        return list;
    }

    private WidgetEntity getWidgetEntity(SlotInfo slotInfo, PageServiceRequest pageServiceRequest)
            throws IOException, PinakaClientException, PinakaException, ParseException {
        WidgetTypeV4 widgetType = slotInfo.getWidgetTypeV4();
        WidgetData widgetData = getWidgetData(slotInfo, widgetType, pageServiceRequest);
        if (Objects.nonNull(widgetData)) {
            return new WidgetEntity(slotInfo.getSlotId(), widgetType, widgetData);
        }
        return null;
    }

    @Nullable
    private WidgetData getWidgetData(SlotInfo slotInfo, WidgetTypeV4 widgetType, PageServiceRequest pageServiceRequest)
            throws IOException, PinakaClientException, PinakaException, ParseException {
        CreditScorePageDataSource creditScorePageDataSource = new CreditScorePageDataSource(profileClientConfiguration);
        CreditScorePageDataSourceResponse creditScorePageDataSourceResponse = creditScorePageDataSource.getData(pageServiceRequest.getAccountId(),pageServiceRequest.getSmUserId());
        switch (widgetType) {
            case FORM_V4:
                String formType = Objects.requireNonNull(slotInfo.getFormType());
                return new BureauFormWidgetTransformers(profileClientConfiguration, encryptionData,dynamicBucket).buildWidgetData(formType, pageServiceRequest);
            case ANNOUNCEMENT_V2:
                return getAnnouncementWidgetData(pageServiceRequest);
            case CREDIT_SCORE:
                return creditScoreWidgetTransformer.buildWidgetData(pageServiceRequest, creditScorePageDataSourceResponse.getBureauDataResponse());
            case RICH_MULTI_IMAGE_BANNER:
                return creditScoreWidgetTransformer.buildLoanMultiImageBannerWidgetData(creditScorePageDataSourceResponse.getBureauDataResponse());
            case HIGHLIGHTS_EXTENDED:
                return creditScoreWidgetTransformer.buildCreditScoreHighlightsWidgetData();
            case MARKUP:
                return new MarkUpWidgetTransformer().buildMarkUpWidgetData(slotInfo.getStaticContentId());
            case IMAGE_TEXT_CARDS_LIST:
                if (validateCsInsightsWidgetToShow(creditScorePageDataSourceResponse.getBureauDataResponse())) {
                    return getImageCardListWidgetData(pageServiceRequest, creditScorePageDataSourceResponse, slotInfo);
                } else {
                    return null;
                }
            case CREDIT_SCORE_RANGE_CLASSIFICATION:
                return creditScoreWidgetTransformer.buildCreditScoreClassificationWidget(creditScorePageDataSourceResponse.getBureauDataResponse());
            case CREDIT_SCORE_INSIGHTS:
                return getCreditScoreDropdownInsightsWidgetData(pageServiceRequest, creditScorePageDataSourceResponse);
            case TEXT_V2:
                return getInsightDescriptionWidgetData(pageServiceRequest);
            case TABLE_V4:
                return getTableWidgetData(pageServiceRequest);
        }
        throw new PinakaException("Unknown widgetType : " + widgetType.name());
    }

    private boolean checkIfInactiveLendersExist(BureauDataResponse bureauDataResponse) {
        boolean inactiveFlag = false;
        for (bureauLender lender : bureauDataResponse.getLenders()) {
            if (!lender.isActive()) {
                inactiveFlag = true;
                break;
            }
        }
        return inactiveFlag;
    }

    private WidgetData getAnnouncementWidgetData(PageServiceRequest pageServiceRequest)
            throws PinakaClientException {
        if (Constants.BUREAU_FORM.equals(pageServiceRequest.getContext())) {
            return new CreditScoreDetailsFormTransformer().buildAnnouncementWidgetData();
        } else {
            try {
                CreditScorePageDataSource creditScorePageDataSource = new CreditScorePageDataSource(profileClientConfiguration);
                CreditScorePageDataSourceResponse creditScorePageDataSourceResponse = creditScorePageDataSource.getData(pageServiceRequest.getAccountId(),pageServiceRequest.getSmUserId());
                return creditScoreWidgetTransformer.buildAnnoucementWidgetData(pageServiceRequest, creditScorePageDataSourceResponse.getBureauDataResponse());
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        }
    }

    private WidgetData getTableWidgetData(PageServiceRequest pageServiceRequest) throws PinakaException, JsonProcessingException {
        String context = pageServiceRequest.getContext();
        switch (context) {
            case Constants.CREDIT_SCORE_TABLE:
                return creditScoreWidgetTransformer.buildCsClassificationTableWidgetData();
        }
        throw new PinakaException("Unknown widgetType : " + context);
    }

    private WidgetData getCreditScoreDropdownInsightsWidgetData(PageServiceRequest pageServiceRequest, CreditScorePageDataSourceResponse creditScorePageDataSourceResponse) throws JsonProcessingException, PinakaException {
        String context = pageServiceRequest.getContext();
        switch (context) {
            case Constants.ONTIME_PAYMENT:
                return creditScoreWidgetTransformer.buildOntimePaymentDropdownWidget(creditScorePageDataSourceResponse.getBureauDataResponse());
            case Constants.CREDIT_USAGE:
                return creditScoreWidgetTransformer.buildCreditUsageDropdownWidget(creditScorePageDataSourceResponse.getBureauDataResponse());
            case Constants.CREDIT_AGE:
                return creditScoreWidgetTransformer.buildCreditAgeDropdownWidget(creditScorePageDataSourceResponse.getBureauDataResponse());
            case Constants.CREDIT_MIX:
                return creditScoreWidgetTransformer.buildCreditMixDropdownWidget(creditScorePageDataSourceResponse.getBureauDataResponse());
            case Constants.CREDIT_ENQUIRIES:
                return creditScoreWidgetTransformer.buildCreditEnquiriesDropdownWidget(creditScorePageDataSourceResponse.getBureauDataResponse());
        }
        throw new PinakaException("Unknown widgetType : " + context);
    }

    private WidgetData getInsightDescriptionWidgetData(PageServiceRequest pageServiceRequest) throws JsonProcessingException, PinakaException {
        String context = pageServiceRequest.getContext();
        switch (context) {
            case Constants.ONTIME_PAYMENT:
                return creditScoreWidgetTransformer.buildOntimePaymentDescriptionWidgetData();
            case Constants.CREDIT_USAGE:
                return creditScoreWidgetTransformer.buildCreditUsageDescriptionWidgetData();
            case Constants.CREDIT_AGE:
                return creditScoreWidgetTransformer.buildCreditAgeDescriptionWidgetData();
            case Constants.CREDIT_MIX:
                return creditScoreWidgetTransformer.buildCreditMixDescriptionWidgetData();
            case Constants.CREDIT_ENQUIRIES:
                return creditScoreWidgetTransformer.buildCreditEnquiriesDescriptionWidgetData();
        }
        throw new PinakaException("Unknown widgetType : " + context);
    }

    private WidgetData getImageCardListWidgetData(PageServiceRequest pageServiceRequest, CreditScorePageDataSourceResponse creditScorePageDataSourceResponse, SlotInfo slotInfo) throws JsonProcessingException, PinakaException {
        String context = pageServiceRequest.getContext();
        boolean inactiveFlag = checkIfInactiveLendersExist(creditScorePageDataSourceResponse.getBureauDataResponse());
        switch (context) {
            case Constants.CREDIT_SCORE:
                return creditScoreWidgetTransformer.buildOverallInsightsWidgetData(creditScorePageDataSourceResponse.getBureauDataResponse());
            case Constants.ONTIME_PAYMENT:
                switch (slotInfo.getContentId()) {
                    case Constants.ACTIVE_ACCOUNT:
                        return creditScoreWidgetTransformer.buildActiveLendersForOntimePaymentsWidgetData(creditScorePageDataSourceResponse.getBureauDataResponse());
                    case Constants.INACTIVE_ACCOUNT:
                        if (inactiveFlag) {
                            return creditScoreWidgetTransformer.buildInactiveLendersForOntimePaymentsWidgetData(creditScorePageDataSourceResponse.getBureauDataResponse());
                        }
                    default:
                        return null;
                }
            case Constants.CREDIT_USAGE:
                switch (slotInfo.getContentId()) {
                    case Constants.ACTIVE_ACCOUNT:
                        return creditScoreWidgetTransformer.buildActiveLendersForCreditUsageWidgetData(creditScorePageDataSourceResponse.getBureauDataResponse());
                    case Constants.INACTIVE_ACCOUNT:
                        if (inactiveFlag) {
                            return creditScoreWidgetTransformer.buildInactiveLendersForCreditUsageWidgetData(creditScorePageDataSourceResponse.getBureauDataResponse());
                        }
                    default:
                        return null;
                }
            case Constants.CREDIT_AGE:
                switch (slotInfo.getContentId()) {
                    case Constants.ACTIVE_ACCOUNT:
                        return creditScoreWidgetTransformer.buildActiveLendersForCreditAgeWidgetData(creditScorePageDataSourceResponse.getBureauDataResponse());
                    case Constants.INACTIVE_ACCOUNT:
                        if (inactiveFlag) {
                            return creditScoreWidgetTransformer.buildInactiveLendersForCreditAgeWidgetData(creditScorePageDataSourceResponse.getBureauDataResponse());
                        }
                    default:
                        return null;
                }
            case Constants.CREDIT_MIX:
                switch (slotInfo.getContentId()) {
                    case Constants.ACTIVE_ACCOUNT:
                        return creditScoreWidgetTransformer.buildActiveLendersForCreditMixWidgetData(creditScorePageDataSourceResponse.getBureauDataResponse());
                    case Constants.INACTIVE_ACCOUNT:
                        if (inactiveFlag) {
                            return creditScoreWidgetTransformer.buildInactiveLendersForCreditMixWidgetData(creditScorePageDataSourceResponse.getBureauDataResponse());
                        }
                    default:
                        return null;
                }
        }
        throw new PinakaException("Unknown widgetType : " + context);
    }

    private boolean validateCsInsightsWidgetToShow(BureauDataResponse bureauDataResponse) {
        if (Objects.isNull(bureauDataResponse.getOnTimePaymentPer()) || Objects.isNull(bureauDataResponse.getOnTimePayment())
                || Objects.isNull(bureauDataResponse.getDelayedPayment()) || Objects.isNull(bureauDataResponse.getCreditUsage())
                || Objects.isNull(bureauDataResponse.getCurrentBalance()) || Objects.isNull(bureauDataResponse.getCreditLimit())
                || StringUtils.isBlank(bureauDataResponse.getCreditAge()) || Objects.isNull(bureauDataResponse.getLast180DaysCreditEnquiries())
                || StringUtils.isBlank(bureauDataResponse.getCreditMix())) {
            return false;
        }
        return true;
    }
}
