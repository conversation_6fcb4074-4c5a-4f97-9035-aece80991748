package com.flipkart.fintech.profile.pagehandler.pagedatasource;

import com.flipkart.fintech.profile.pagehandler.pagedatasource.responses.CreditScoreWaitScreenDataSourceResponse;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class CreditScoreWaitScreenDataSource  implements BureauPageDataSource<CreditScoreWaitScreenDataSourceResponse>{
    @Override
    public CreditScoreWaitScreenDataSourceResponse getData(String merchantUserId, String smUserId) {
        Map<String,Object> queryparams = new HashMap<>();
        return new CreditScoreWaitScreenDataSourceResponse(queryparams);

    }
}
