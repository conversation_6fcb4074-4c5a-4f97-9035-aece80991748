package com.flipkart.fintech.profile.pagehandler.web.resource;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.pinaka.api.request.v6.PageServiceRequest;
import com.flipkart.fintech.pinaka.api.response.v6.FetchBulkDataResponseV2;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.profile.pagehandler.FetchPageDataHandlerV2;
import com.google.inject.Inject;
import de.client.shade.javax.validation.Valid;
import io.dropwizard.hibernate.UnitOfWork;
import io.swagger.annotations.ApiOperation;
import lombok.CustomLog;

import javax.validation.constraints.NotNull;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;

@CustomLog
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("6/")
public class FetchPageDataResourceV2 {

    private final FetchPageDataHandlerV2 fetchPageDataHandler;

    @Inject
    public FetchPageDataResourceV2(FetchPageDataHandlerV2 fetchPageDataHandler) {
        this.fetchPageDataHandler = fetchPageDataHandler;
    }

    @POST
    @Timed
    @ExceptionMetered
    @ApiOperation("Credit Score Page Fetch Data")
    @Path("/fetch-bulk-data-v3")
    @UnitOfWork
    public FetchBulkDataResponseV2 fetchBulkDataV2(@Valid PageServiceRequest pageServiceRequest,
                                                   @NotNull @HeaderParam("X-Request-Id") String requestId)
            throws PinakaException, PinakaClientException {
        log.info("Fetch Bulk Data Request received for accountId: {},", pageServiceRequest.getAccountId());
        return fetchPageDataHandler.fetchBulkDataV3(pageServiceRequest);
    }
}
