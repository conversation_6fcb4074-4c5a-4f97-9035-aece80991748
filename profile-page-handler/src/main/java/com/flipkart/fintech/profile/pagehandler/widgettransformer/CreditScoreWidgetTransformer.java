package com.flipkart.fintech.profile.pagehandler.widgettransformer;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.affordability.robinhood.api.model.personalLoan.Cohort;
import com.flipkart.affordability.robinhood.api.model.personalLoan.JourneyStatus;
import com.flipkart.affordability.robinhood.api.model.personalLoan.Lender;
import com.flipkart.affordability.robinhood.api.model.personalLoan.PersonalLoanUserDetailsV2;
import com.flipkart.fintech.pinaka.api.request.v6.PageServiceRequest;
import com.flipkart.fintech.pinaka.client.PinakaCalvinClientConfig;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.common.client.RobinhoodClientImpl;
import com.flipkart.fintech.pinaka.common.metricRegistry.PinakaMetricRegistry;
import com.flipkart.fintech.pinaka.common.varadhi.RobinhoodEventRequest;
import com.flipkart.fintech.profile.config.ProfileServiceConfig;
import com.flipkart.fintech.profile.model.bureauLender;
import com.flipkart.fintech.profile.pagehandler.utils.ExperianUtils;
import com.flipkart.fintech.profile.pagehandler.utils.TransformerUtils;
import com.flipkart.fintech.profile.response.BureauDataResponse;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.common.leaf.value.RichTextValue;
import com.flipkart.rome.datatypes.response.connekt.v1.inapp.ImageTextValue;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import com.flipkart.rome.datatypes.response.fintech.supermoney.RichKeyValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.*;
import com.flipkart.rome.datatypes.response.page.v4.AnnouncementV2WidgetData;
import com.flipkart.rome.datatypes.response.page.v4.RichMessageWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.WidgetData;
import com.flipkart.rome.datatypes.response.page.v4.npsWidgetData.HighlightsExtendedWidgetData;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.CustomLog;

import javax.inject.Named;
import javax.ws.rs.client.Client;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;

@CustomLog
public class CreditScoreWidgetTransformer {
    private final EncryptionData encryptionData;
    private RobinhoodClientImpl robinhoodClient;
    private final ProfileServiceConfig profileServiceConfig;
    private static final String UNDER_THOUSAND_DECIMAL_PATTERN = "###.00";
    public static final String OVER_THOUSAND_DECIMAL_FLOW = "000.00";
    private static final String excellentScoreJson;
    private static final String averageScoreJson;
    private static final String poorScoreJson;
    private static final String noScoreJson;
    private static final String loanMultiImageBannerJson;
    private static final String creditScoreHighlightsJson;
    private static final String refreshButtonJson;
    private static final String creditOverallInsightJson;
    private static final String creditScoreExcellentClassificationJson;
    private static final String creditScoreAverageClassificationJson;
    private static final String creditScorePoorClassificationJson;
    private static final String ontimeDetailedInsightsJson;
    private static final String cUsageDetailedInsightsJson;
    private static final String cMixDetailedInsightsJson;
    private static final String cAgeDetailedInsightsJson;
    private static final String cEnquiriesDetailedInsightsJson;
    private static final String activeAccountJson;
    private static final String inactiveAccountJson;
    private static final String ontimePaymentDescriptionJson;
    private static final String creditUsageDescriptionJson;
    private static final String creditAgeDescriptionJson;
    private static final String creditMixDescriptionJson;
    private static final String creditEnquiriesDescriptionJson;
    private static final String creditScoreClassificationTableJson;
    private final ExecutorService executorService;

    static {
        excellentScoreJson = TransformerUtils.readFileasString("template/bureau/ExcellentCreditScore.json");
        averageScoreJson = TransformerUtils.readFileasString("template/bureau/AverageCreditScore.json");
        poorScoreJson = TransformerUtils.readFileasString("template/bureau/PoorCreditScore.json");
        noScoreJson = TransformerUtils.readFileasString("template/bureau/NoCreditScore.json");
        loanMultiImageBannerJson = TransformerUtils.readFileasString("template/bureau/LoanRichMultiImageBanner.json");
        creditScoreHighlightsJson = TransformerUtils.readFileasString("template/bureau/CreditScoreHighlights.json");
        refreshButtonJson = TransformerUtils.readFileasString("template/bureau/refreshButton.json");
        creditOverallInsightJson = TransformerUtils.readFileasString("template/bureau/CreditOverallInsights.json");
        creditScoreExcellentClassificationJson = TransformerUtils.readFileasString("template/bureau/CreditScoreExcellentClassification.json");
        creditScoreAverageClassificationJson = TransformerUtils.readFileasString("template/bureau/CreditScoreAverageClassification.json");
        creditScorePoorClassificationJson = TransformerUtils.readFileasString("template/bureau/CreditScorePoorClassification.json");
        ontimeDetailedInsightsJson = TransformerUtils.readFileasString("template/bureau/OntimePaymentInsights.json");
        cUsageDetailedInsightsJson = TransformerUtils.readFileasString("template/bureau/CreditUsageInsights.json");
        cAgeDetailedInsightsJson = TransformerUtils.readFileasString("template/bureau/CreditAgeInsights.json");
        cMixDetailedInsightsJson = TransformerUtils.readFileasString("template/bureau/CreditMixInsights.json");
        cEnquiriesDetailedInsightsJson = TransformerUtils.readFileasString("template/bureau/CreditEnquiriesInsights.json");
        activeAccountJson = TransformerUtils.readFileasString("template/bureau/ActiveAccount.json");
        inactiveAccountJson = TransformerUtils.readFileasString("template/bureau/InactiveAccount.json");
        ontimePaymentDescriptionJson = TransformerUtils.readFileasString("template/bureau/OntimePaymentDescription.json");
        creditUsageDescriptionJson = TransformerUtils.readFileasString("template/bureau/CreditUsageDescription.json");
        creditAgeDescriptionJson = TransformerUtils.readFileasString("template/bureau/CreditAgeDescription.json");
        creditMixDescriptionJson = TransformerUtils.readFileasString("template/bureau/CreditMixDescription.json");
        creditEnquiriesDescriptionJson = TransformerUtils.readFileasString("template/bureau/CreditEnquiriesDescription.json");
        creditScoreClassificationTableJson = TransformerUtils.readFileasString("template/bureau/CreditScoreClassificationTable.json");
    }

    public CreditScoreWidgetTransformer(EncryptionData encryptionData, PinakaCalvinClientConfig pinakaCalvinClientConfig, @Named("extOfferServiceExecutor") ExecutorService executorService, Client client, ProfileServiceConfig profileServiceConfig) {
        this.encryptionData = encryptionData;
        this.executorService = executorService;
        this.profileServiceConfig = profileServiceConfig;
        this.robinhoodClient = new RobinhoodClientImpl(pinakaCalvinClientConfig, client);
    }

    public RichMultiImageBannerWidgetData buildLoanMultiImageBannerWidgetData(BureauDataResponse bureauDataResponse) throws JsonProcessingException {
        RichMultiImageBannerWidgetData richMultiImageBannerWidgetData = ObjectMapperUtil.get().readValue(loanMultiImageBannerJson, RichMultiImageBannerWidgetData.class);
        updateCreditScoreButton(richMultiImageBannerWidgetData);
        if (Objects.isNull(bureauDataResponse.getError()) && Integer.parseInt(bureauDataResponse.getCreditScore()) >= 650) {
            return richMultiImageBannerWidgetData;
        }
        return null;
    }

    private void updateCreditScoreButton(RichMultiImageBannerWidgetData richMultiImageBannerWidgetData) {
        richMultiImageBannerWidgetData.getBanner().getValue().getButton().getAction().setEncryption(encryptionData);
    }

    public HighlightsExtendedWidgetData buildCreditScoreHighlightsWidgetData() throws JsonProcessingException {
        return ObjectMapperUtil.get().readValue(creditScoreHighlightsJson, HighlightsExtendedWidgetData.class);
    }


    public AnnouncementV2WidgetData buildAnnoucementWidgetData(PageServiceRequest pageServiceRequest, BureauDataResponse bureauDataResponse) throws JsonProcessingException {
        AnnouncementV2WidgetData announcementV2WidgetData = new AnnouncementV2WidgetData();
        if (Objects.nonNull(bureauDataResponse.getError())) {
            announcementV2WidgetData = ObjectMapperUtil.get().readValue(noScoreJson, AnnouncementV2WidgetData.class);
        }
        return announcementV2WidgetData;
    }

    public CreditScoreWidgetData buildWidgetData(PageServiceRequest pageServiceRequest, BureauDataResponse bureauDataResponse) throws JsonProcessingException {
        CreditScoreWidgetData creditScoreWidgetData = creditScoreWidgetDecider(bureauDataResponse, pageServiceRequest.getAccountId());
        updateCreditScore(creditScoreWidgetData, bureauDataResponse);
        showRefreshButton(creditScoreWidgetData, bureauDataResponse);
        return creditScoreWidgetData;
    }

    public WidgetData buildOntimePaymentDescriptionWidgetData() throws JsonProcessingException {
        return ObjectMapperUtil.get().readValue(ontimePaymentDescriptionJson, RichMessageWidgetData.class);
    }
    public WidgetData buildCreditUsageDescriptionWidgetData() throws JsonProcessingException {
        return ObjectMapperUtil.get().readValue(creditUsageDescriptionJson, RichMessageWidgetData.class);
    }
    public WidgetData buildCreditAgeDescriptionWidgetData() throws JsonProcessingException {
        return ObjectMapperUtil.get().readValue(creditAgeDescriptionJson, RichMessageWidgetData.class);
    }
    public WidgetData buildCreditMixDescriptionWidgetData() throws JsonProcessingException {
        return ObjectMapperUtil.get().readValue(creditMixDescriptionJson, RichMessageWidgetData.class);
    }
    public WidgetData buildCreditEnquiriesDescriptionWidgetData() throws JsonProcessingException {
        return ObjectMapperUtil.get().readValue(creditEnquiriesDescriptionJson, RichMessageWidgetData.class);
    }

    private CreditScoreWidgetData creditScoreWidgetDecider(BureauDataResponse bureauDataResponse, String accountId) throws JsonProcessingException {
        try {
            Future<?> future = executorService.submit(() -> {
                sendRobinhoodEvent(accountId);
            });
        } catch (Exception ex) {
            log.error("Unable to send events to robinhood due to :{}", ex.getMessage());
        }
        if (Objects.nonNull(bureauDataResponse.getCreditScore()) && Integer.parseInt(bureauDataResponse.getCreditScore()) >= 750) {
            return ObjectMapperUtil.get().readValue(excellentScoreJson, CreditScoreWidgetData.class);
        } else if (Objects.nonNull(bureauDataResponse.getCreditScore()) && Integer.parseInt(bureauDataResponse.getCreditScore()) < 750 && Integer.parseInt(bureauDataResponse.getCreditScore()) >= 650) {
            return ObjectMapperUtil.get().readValue(averageScoreJson, CreditScoreWidgetData.class);
        } else {
            return ObjectMapperUtil.get().readValue(poorScoreJson, CreditScoreWidgetData.class);
        }
    }

    private void sendRobinhoodEvent(String accountId) {
        PersonalLoanUserDetailsV2 personalLoanUserDetailsV2 = new PersonalLoanUserDetailsV2();
        personalLoanUserDetailsV2.setAccountId(accountId);
        personalLoanUserDetailsV2.setEligibleLoanAmount((double) 0);
        personalLoanUserDetailsV2.setJourneyStatus(JourneyStatus.IN_PROGRESS);
        personalLoanUserDetailsV2.setLender(Lender.UNALLOCATED);
        personalLoanUserDetailsV2.setCohort(Cohort.NTB);
        personalLoanUserDetailsV2.setUpdatedAt(new Date().getTime());
        processEventForRhUpdate(new RobinhoodEventRequest(personalLoanUserDetailsV2));
    }

    private void processEventForRhUpdate(RobinhoodEventRequest robinhoodEventRequest) {
        try {
            log.info("Pushing credit score event to varadhi");
            PinakaMetricRegistry.getMetricRegistry().meter("CREDIT_SCORE_EVENT").mark();
            this.robinhoodClient.pushEvent(robinhoodEventRequest);
        } catch (PinakaClientException e) {
            log.error("Error while pushing credit score event to varadhi : {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    private void showRefreshButton(CreditScoreWidgetData creditScoreWidgetData, BureauDataResponse bureauDataResponse) {
        String inputDate = bureauDataResponse.getLastUpdatedAt();
        try {

            long differenceInMinutes = ExperianUtils.getRefreshButtonTimeDiff(inputDate);

            if (differenceInMinutes > profileServiceConfig.getExperianConfig().getShowRefreshButton()) {
                SubmitButtonValue refreshButton = ObjectMapperUtil.get().readValue(refreshButtonJson, SubmitButtonValue.class);
                refreshButton.getButton().getAction().setEncryption(encryptionData);
                creditScoreWidgetData.setRefreshScoreAction(refreshButton.getButton());
            }
        } catch (Exception e) {
            throw new RuntimeException();
        }
    }
    private void updateCreditScore(CreditScoreWidgetData creditScoreWidgetData, BureauDataResponse bureauDataResponse) {
        creditScoreWidgetData.getCreditScore().getValue().setScore(Integer.parseInt(bureauDataResponse.getCreditScore()));
        String inputDate = bureauDataResponse.getLastUpdatedAt();
        try {
            SimpleDateFormat inputFormat = new SimpleDateFormat("EEE MMM dd HH:mm:ss zzz yyyy", Locale.ENGLISH);
            Date date = inputFormat.parse(inputDate);

            SimpleDateFormat outputFormat = new SimpleDateFormat("dd MMM ''yy", Locale.ENGLISH);
            String outputDate = outputFormat.format(date);
            creditScoreWidgetData.getCreditScore().getValue().setUpdatedAtString("last updated on " + outputDate);
        } catch (Exception e) {
            throw new RuntimeException();
        }
    }

    public WidgetData buildOverallInsightsWidgetData(BureauDataResponse bureauDataResponse) throws JsonProcessingException {
        ImageTextCardsListWidgetData imageTextCardsListWidgetData = ObjectMapperUtil.get().readValue(creditOverallInsightJson, ImageTextCardsListWidgetData.class);
        updateOntimePayment(imageTextCardsListWidgetData, bureauDataResponse);
        updateCreditUtilisation(imageTextCardsListWidgetData, bureauDataResponse);
        updateCreditMix(imageTextCardsListWidgetData, bureauDataResponse);
        updateCreditAge(imageTextCardsListWidgetData, bureauDataResponse);
        updateCreditEnquiries(imageTextCardsListWidgetData, bureauDataResponse);
        return imageTextCardsListWidgetData;
    }

    private void updateOntimePayment(ImageTextCardsListWidgetData imageTextCardsListWidgetData, BureauDataResponse bureauDataResponse) {
        ImageTextValue imageTextValue = (ImageTextValue) imageTextCardsListWidgetData.getCardsData().getCards().get(0).getValue();
        RichTextValue richTextValue = (RichTextValue) imageTextValue.getDescription();
        long ontimePayment = Math.round(bureauDataResponse.getOnTimePaymentPer());
        String result = ontimePayment + "%";
        richTextValue.setText(result);
        imageTextValue.setDescription(richTextValue);
    }

    private void updateCreditUtilisation(ImageTextCardsListWidgetData imageTextCardsListWidgetData, BureauDataResponse bureauDataResponse) {
        ImageTextValue imageTextValue = (ImageTextValue) imageTextCardsListWidgetData.getCardsData().getCards().get(1).getValue();
        RichTextValue richTextValue = (RichTextValue) imageTextValue.getDescription();
        long creditUsage = Math.round(bureauDataResponse.getCreditUsage());
        String result = creditUsage + "%";
        richTextValue.setText(result);
        imageTextValue.setDescription(richTextValue);
    }

    private void updateCreditAge(ImageTextCardsListWidgetData imageTextCardsListWidgetData, BureauDataResponse bureauDataResponse) {
        ImageTextValue imageTextValue = (ImageTextValue) imageTextCardsListWidgetData.getCardsData().getCards().get(2).getValue();
        RichTextValue richTextValue = (RichTextValue) imageTextValue.getDescription();
        richTextValue.setText(bureauDataResponse.getCreditAge());
        imageTextValue.setDescription(richTextValue);
    }

    private void updateCreditEnquiries(ImageTextCardsListWidgetData imageTextCardsListWidgetData, BureauDataResponse bureauDataResponse) {
        ImageTextValue imageTextValue = (ImageTextValue) imageTextCardsListWidgetData.getCardsData().getCards().get(3).getValue();
        RichTextValue richTextValue = (RichTextValue) imageTextValue.getDescription();
        richTextValue.setText(String.valueOf(bureauDataResponse.getLast180DaysCreditEnquiries()));
        imageTextValue.setDescription(richTextValue);
    }

    private void updateCreditMix(ImageTextCardsListWidgetData imageTextCardsListWidgetData, BureauDataResponse bureauDataResponse) {
        ImageTextValue imageTextValue = (ImageTextValue) imageTextCardsListWidgetData.getCardsData().getCards().get(4).getValue();
        RichTextValue richTextValue = (RichTextValue) imageTextValue.getDescription();
        richTextValue.setText(bureauDataResponse.getCreditMix());
        imageTextValue.setDescription(richTextValue);
    }

    public WidgetData buildCreditScoreClassificationWidget(BureauDataResponse bureauDataResponse) {
        CreditScoreRangeClassificationWidgetData creditScoreRangeClassificationWidgetData;
        try {
            creditScoreRangeClassificationWidgetData = csClassificationDecider(bureauDataResponse);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        return creditScoreRangeClassificationWidgetData;
    }

    private CreditScoreRangeClassificationWidgetData csClassificationDecider(BureauDataResponse bureauDataResponse) throws JsonProcessingException {
        if (Objects.nonNull(bureauDataResponse.getCreditScore()) && Integer.parseInt(bureauDataResponse.getCreditScore()) >= 750) {
            return ObjectMapperUtil.get().readValue(creditScoreExcellentClassificationJson, CreditScoreRangeClassificationWidgetData.class);
        } else if (Objects.nonNull(bureauDataResponse.getCreditScore()) && Integer.parseInt(bureauDataResponse.getCreditScore()) < 750 && Integer.parseInt(bureauDataResponse.getCreditScore()) >= 650) {
            return ObjectMapperUtil.get().readValue(creditScoreAverageClassificationJson, CreditScoreRangeClassificationWidgetData.class);
        } else {
            return ObjectMapperUtil.get().readValue(creditScorePoorClassificationJson, CreditScoreRangeClassificationWidgetData.class);
        }
    }

    public WidgetData buildCsClassificationTableWidgetData() throws JsonProcessingException {
        TableWidgetData csTable = (TableWidgetData) ObjectMapperUtil.get().readValue(creditScoreClassificationTableJson, TableWidgetData.class);
        return csTable;
    }

    public WidgetData buildOntimePaymentDropdownWidget(BureauDataResponse bureauDataResponse) throws JsonProcessingException {
        CreditScoreInsightsWidgetData creditScoreInsightsWidgetData = (CreditScoreInsightsWidgetData) ObjectMapperUtil.get().readValue(ontimeDetailedInsightsJson, CreditScoreInsightsWidgetData.class);
        RichTextValue richTextValue = (RichTextValue) creditScoreInsightsWidgetData.getSummary().getDescription();
        long ontimePayment = Math.round(bureauDataResponse.getOnTimePaymentPer());
        String result = ontimePayment + "%";
        richTextValue.setText(result);

        List<RichKeyValue> richKeyValue = creditScoreInsightsWidgetData.getDetails().getValues();
        RichTextValue richTextValue1 = (RichTextValue) richKeyValue.get(0).getValue();
        richTextValue1.setText(String.valueOf(bureauDataResponse.getOnTimePayment()));

        RichTextValue richTextValue2 = (RichTextValue) richKeyValue.get(1).getValue();
        richTextValue2.setText(String.valueOf(bureauDataResponse.getDelayedPayment()));

        return creditScoreInsightsWidgetData;
    }

    public WidgetData buildCreditUsageDropdownWidget(BureauDataResponse bureauDataResponse) throws JsonProcessingException {
        CreditScoreInsightsWidgetData creditScoreInsightsWidgetData = (CreditScoreInsightsWidgetData) ObjectMapperUtil.get().readValue(cUsageDetailedInsightsJson, CreditScoreInsightsWidgetData.class);
        RichTextValue richTextValue = (RichTextValue) creditScoreInsightsWidgetData.getSummary().getDescription();
        long creditUsage = Math.round(bureauDataResponse.getCreditUsage());
        String result = creditUsage + "%";
        richTextValue.setText(result);

        List<RichKeyValue> richKeyValue = creditScoreInsightsWidgetData.getDetails().getValues();
        RichTextValue richTextValue1 = (RichTextValue) richKeyValue.get(0).getValue();
//        richTextValue1.setText(String.valueOf(bureauDataResponse.getCreditLimit()));
        richTextValue1.setText("₹" +formatNumber(bureauDataResponse.getCreditLimit()));

        RichTextValue richTextValue2 = (RichTextValue) richKeyValue.get(1).getValue();
//        richTextValue2.setText(String.valueOf(bureauDataResponse.getCurrentBalance()));
        richTextValue2.setText("₹" +formatNumber(bureauDataResponse.getCurrentBalance()));

        return creditScoreInsightsWidgetData;
    }

    public WidgetData buildCreditMixDropdownWidget(BureauDataResponse bureauDataResponse) throws JsonProcessingException {
        CreditScoreInsightsWidgetData creditScoreInsightsWidgetData = (CreditScoreInsightsWidgetData) ObjectMapperUtil.get().readValue(cMixDetailedInsightsJson, CreditScoreInsightsWidgetData.class);
        RichTextValue richTextValue = (RichTextValue) creditScoreInsightsWidgetData.getSummary().getDescription();
        richTextValue.setText(bureauDataResponse.getCreditMix());
        return creditScoreInsightsWidgetData;
    }

    public WidgetData buildCreditAgeDropdownWidget(BureauDataResponse bureauDataResponse) throws JsonProcessingException {
        CreditScoreInsightsWidgetData creditScoreInsightsWidgetData = (CreditScoreInsightsWidgetData) ObjectMapperUtil.get().readValue(cAgeDetailedInsightsJson, CreditScoreInsightsWidgetData.class);
        RichTextValue richTextValue = (RichTextValue) creditScoreInsightsWidgetData.getSummary().getDescription();
        richTextValue.setText(bureauDataResponse.getCreditAge());
        return creditScoreInsightsWidgetData;
    }

    public WidgetData buildCreditEnquiriesDropdownWidget(BureauDataResponse bureauDataResponse) throws JsonProcessingException {
        CreditScoreInsightsWidgetData creditScoreInsightsWidgetData = (CreditScoreInsightsWidgetData) ObjectMapperUtil.get().readValue(cEnquiriesDetailedInsightsJson, CreditScoreInsightsWidgetData.class);
        RichTextValue richTextValue = (RichTextValue) creditScoreInsightsWidgetData.getSummary().getDescription();
        richTextValue.setText(String.valueOf(bureauDataResponse.getLast180DaysCreditEnquiries()));
        return creditScoreInsightsWidgetData;
    }

    public WidgetData buildActiveLendersForCreditUsageWidgetData(BureauDataResponse bureauDataResponse) throws JsonProcessingException {
        ImageTextCardsListWidgetData imageTextCardsListWidgetDataActive = ObjectMapperUtil.get().readValue(activeAccountJson, ImageTextCardsListWidgetData.class);

        int[] countOfActiveInactiveLender = countActiveLenders(bureauDataResponse);


        for (bureauLender lender : bureauDataResponse.getLenders()) {
            if (lender.isActive() && String.valueOf(lender.getAccountType()).equals("10") && !(lender.getCreditUsage()==null)) {
                RenderableComponent<ImageTextValue> imageTextValueRenderableComponent = readStaticDataForActiveLender();

                ImageTextValue activeLender = (ImageTextValue) imageTextValueRenderableComponent.getValue();

                RichTextValue name = (RichTextValue) activeLender.getTitle();
                String lenderName = lender.getName();
                name.setText(lenderName);

                RichTextValue type = (RichTextValue) activeLender.getSubTitle();
                String input = String.valueOf(lender.getAccountType());
                if (input == null || input.isEmpty()) {
                    throw new RuntimeException();
                }
                if (input.length() == 1) {
                    input = "0" + input;
                }
                String result = bureauDataResponse.getAccountTypeMap().get(input).getName();
                result = result.substring(0, 1).toUpperCase() + result.substring(1).toLowerCase();
                type.setText(result);

                RichTextValue value = (RichTextValue) activeLender.getDescription();

                long creditUsage = Math.round(lender.getCreditUsage());
                String cUsage = creditUsage + "%";
                value.setText(cUsage);

                activeLender.setTitle(name);
                activeLender.setSubTitle(type);
                activeLender.setDescription(value);

                imageTextValueRenderableComponent.setValue(activeLender);

                imageTextCardsListWidgetDataActive.getCardsData().getCards().add(imageTextValueRenderableComponent);

            }
        }
        imageTextCardsListWidgetDataActive.getCardsData().getCards().remove(0);
        return imageTextCardsListWidgetDataActive;
    }

    public WidgetData buildInactiveLendersForCreditUsageWidgetData(BureauDataResponse bureauDataResponse) throws JsonProcessingException {
        ImageTextCardsListWidgetData imageTextCardsListWidgetDataActive = ObjectMapperUtil.get().readValue(inactiveAccountJson, ImageTextCardsListWidgetData.class);

        int[] countOfActiveInactiveLender = countActiveLenders(bureauDataResponse);


        for (bureauLender lender : bureauDataResponse.getLenders() ) {
            if (!lender.isActive() && String.valueOf(lender.getAccountType()).equals("10") && !(lender.getCreditUsage()==null)) {
                RenderableComponent<ImageTextValue> imageTextValueRenderableComponent = readStaticDataForInactiveLender();

                ImageTextValue activeLender = (ImageTextValue) imageTextValueRenderableComponent.getValue();

                RichTextValue name = (RichTextValue) activeLender.getTitle();
                String lenderName = lender.getName();
                name.setText(lenderName);

                RichTextValue type = (RichTextValue) activeLender.getSubTitle();
                String input = String.valueOf(lender.getAccountType());
                if (input == null || input.isEmpty()) {
                    throw new RuntimeException();
                }
                if (input.length() == 1) {
                    input = "0" + input;
                }
                String result = bureauDataResponse.getAccountTypeMap().get(input).getName();
                result = result.substring(0, 1).toUpperCase() + result.substring(1).toLowerCase();
                type.setText(result);

                RichTextValue value = (RichTextValue) activeLender.getDescription();
                long creditUsage = Math.round(lender.getCreditUsage());
                String cUsage = creditUsage + "%";
                value.setText(cUsage);

                activeLender.setTitle(name);
                activeLender.setSubTitle(type);
                activeLender.setDescription(value);

                imageTextValueRenderableComponent.setValue(activeLender);

                imageTextCardsListWidgetDataActive.getCardsData().getCards().add(imageTextValueRenderableComponent);

            }
        }
        imageTextCardsListWidgetDataActive.getCardsData().getCards().remove(0);
        return imageTextCardsListWidgetDataActive;
    }
    public WidgetData buildActiveLendersForCreditAgeWidgetData(BureauDataResponse bureauDataResponse) throws JsonProcessingException {
        ImageTextCardsListWidgetData imageTextCardsListWidgetDataActive = ObjectMapperUtil.get().readValue(activeAccountJson, ImageTextCardsListWidgetData.class);

        int[] countOfActiveInactiveLender = countActiveLenders(bureauDataResponse);


        for (bureauLender lender : bureauDataResponse.getLenders()) {
            if (lender.isActive()) {
                RenderableComponent<ImageTextValue> imageTextValueRenderableComponent = readStaticDataForActiveLender();

                ImageTextValue activeLender = (ImageTextValue) imageTextValueRenderableComponent.getValue();

                RichTextValue name = (RichTextValue) activeLender.getTitle();
                String lenderName = lender.getName();
                name.setText(lenderName);

                RichTextValue type = (RichTextValue) activeLender.getSubTitle();
                String input = String.valueOf(lender.getAccountType());
                if (input == null || input.isEmpty()) {
                    throw new RuntimeException();
                }
                if (input.length() == 1) {
                    input = "0" + input;
                }
                String result = bureauDataResponse.getAccountTypeMap().get(input).getName();
                result = result.substring(0, 1).toUpperCase() + result.substring(1).toLowerCase();
                type.setText(result);

                RichTextValue value = (RichTextValue) activeLender.getDescription();
                value.setText(lender.getCreditAge());

                activeLender.setTitle(name);
                activeLender.setSubTitle(type);
                activeLender.setDescription(value);

                imageTextValueRenderableComponent.setValue(activeLender);

                imageTextCardsListWidgetDataActive.getCardsData().getCards().add(imageTextValueRenderableComponent);

            }
        }
        imageTextCardsListWidgetDataActive.getCardsData().getCards().remove(0);
        return imageTextCardsListWidgetDataActive;
    }

    public WidgetData buildInactiveLendersForCreditAgeWidgetData(BureauDataResponse bureauDataResponse) throws JsonProcessingException {
        ImageTextCardsListWidgetData imageTextCardsListWidgetDataActive = ObjectMapperUtil.get().readValue(inactiveAccountJson, ImageTextCardsListWidgetData.class);

        int[] countOfActiveInactiveLender = countActiveLenders(bureauDataResponse);


        for (bureauLender lender : bureauDataResponse.getLenders()) {
            if (!lender.isActive()) {
                RenderableComponent<ImageTextValue> imageTextValueRenderableComponent = readStaticDataForInactiveLender();

                ImageTextValue activeLender = (ImageTextValue) imageTextValueRenderableComponent.getValue();

                RichTextValue name = (RichTextValue) activeLender.getTitle();
                String lenderName = lender.getName();
                name.setText(lenderName);

                RichTextValue type = (RichTextValue) activeLender.getSubTitle();
                String input = String.valueOf(lender.getAccountType());
                if (input == null || input.isEmpty()) {
                    throw new RuntimeException();
                }
                if (input.length() == 1) {
                    input = "0" + input;
                }
                String result = bureauDataResponse.getAccountTypeMap().get(input).getName();
                result = result.substring(0, 1).toUpperCase() + result.substring(1).toLowerCase();
                type.setText(result);

                RichTextValue value = (RichTextValue) activeLender.getDescription();
                value.setText(lender.getCreditAge());

                activeLender.setTitle(name);
                activeLender.setSubTitle(type);
                activeLender.setDescription(value);

                imageTextValueRenderableComponent.setValue(activeLender);

                imageTextCardsListWidgetDataActive.getCardsData().getCards().add(imageTextValueRenderableComponent);

            }
        }
        imageTextCardsListWidgetDataActive.getCardsData().getCards().remove(0);
        return imageTextCardsListWidgetDataActive;
    }

    public WidgetData buildActiveLendersForOntimePaymentsWidgetData(BureauDataResponse bureauDataResponse) throws JsonProcessingException {
        ImageTextCardsListWidgetData imageTextCardsListWidgetDataActive = ObjectMapperUtil.get().readValue(activeAccountJson, ImageTextCardsListWidgetData.class);

        int[] countOfActiveInactiveLender = countActiveLenders(bureauDataResponse);


        for (bureauLender lender : bureauDataResponse.getLenders()) {
            if (lender.isActive()) {
                RenderableComponent<ImageTextValue> imageTextValueRenderableComponent = readStaticDataForActiveLender();

                ImageTextValue activeLender = (ImageTextValue) imageTextValueRenderableComponent.getValue();

                RichTextValue name = (RichTextValue) activeLender.getTitle();
                String lenderName = lender.getName();
                name.setText(lenderName);

                RichTextValue type = (RichTextValue) activeLender.getSubTitle();
                String input = String.valueOf(lender.getAccountType());
                if (input == null || input.isEmpty()) {
                    throw new RuntimeException();
                }
                if (input.length() == 1) {
                    input = "0" + input;
                }
                String result = bureauDataResponse.getAccountTypeMap().get(input).getName();
                result = result.substring(0, 1).toUpperCase() + result.substring(1).toLowerCase();
                type.setText(result);

                RichTextValue value = (RichTextValue) activeLender.getDescription();
                long ontimePayment = Math.round(lender.getOnTimePaymentPer());
                String ontimeP = ontimePayment + "%";
                value.setText(ontimeP);

                activeLender.setTitle(name);
                activeLender.setSubTitle(type);
                activeLender.setDescription(value);

                imageTextValueRenderableComponent.setValue(activeLender);

                imageTextCardsListWidgetDataActive.getCardsData().getCards().add(imageTextValueRenderableComponent);

            }
        }
        imageTextCardsListWidgetDataActive.getCardsData().getCards().remove(0);
        return imageTextCardsListWidgetDataActive;
    }

    public WidgetData buildInactiveLendersForOntimePaymentsWidgetData(BureauDataResponse bureauDataResponse) throws JsonProcessingException {
        ImageTextCardsListWidgetData imageTextCardsListWidgetDataActive = ObjectMapperUtil.get().readValue(inactiveAccountJson, ImageTextCardsListWidgetData.class);

        int[] countOfActiveInactiveLender = countActiveLenders(bureauDataResponse);


        for (bureauLender lender : bureauDataResponse.getLenders()) {
            if (!lender.isActive()) {
                RenderableComponent<ImageTextValue> imageTextValueRenderableComponent = readStaticDataForInactiveLender();

                ImageTextValue activeLender = (ImageTextValue) imageTextValueRenderableComponent.getValue();

                RichTextValue name = (RichTextValue) activeLender.getTitle();
                String lenderName = lender.getName();
                name.setText(lenderName);

                RichTextValue type = (RichTextValue) activeLender.getSubTitle();
                String input = String.valueOf(lender.getAccountType());
                if (input == null || input.isEmpty()) {
                    throw new RuntimeException();
                }
                if (input.length() == 1) {
                    input = "0" + input;
                }
                String result = bureauDataResponse.getAccountTypeMap().get(input).getName();
                result = result.substring(0, 1).toUpperCase() + result.substring(1).toLowerCase();
                type.setText(result);

                RichTextValue value = (RichTextValue) activeLender.getDescription();
                long ontimePayment = Math.round(lender.getOnTimePaymentPer());
                String ontimeP = ontimePayment + "%";
                value.setText(ontimeP);

                activeLender.setTitle(name);
                activeLender.setSubTitle(type);
                activeLender.setDescription(value);

                imageTextValueRenderableComponent.setValue(activeLender);

                imageTextCardsListWidgetDataActive.getCardsData().getCards().add(imageTextValueRenderableComponent);

            }
        }
        imageTextCardsListWidgetDataActive.getCardsData().getCards().remove(0);
        return imageTextCardsListWidgetDataActive;
    }
    public WidgetData buildActiveLendersForCreditMixWidgetData(BureauDataResponse bureauDataResponse) throws JsonProcessingException {
        ImageTextCardsListWidgetData imageTextCardsListWidgetDataActive = ObjectMapperUtil.get().readValue(activeAccountJson, ImageTextCardsListWidgetData.class);

        int[] countOfActiveInactiveLender = countActiveLenders(bureauDataResponse);


        for (bureauLender lender : bureauDataResponse.getLenders()) {
            if (lender.isActive()) {
                RenderableComponent<ImageTextValue> imageTextValueRenderableComponent = readStaticDataForActiveLender();

                ImageTextValue activeLender = (ImageTextValue) imageTextValueRenderableComponent.getValue();

                RichTextValue name = (RichTextValue) activeLender.getTitle();
                String lenderName = lender.getName();
                name.setText(lenderName);

                RichTextValue type = (RichTextValue) activeLender.getSubTitle();
                String input = String.valueOf(lender.getAccountType());
                if (input == null || input.isEmpty()) {
                    throw new RuntimeException();
                }
                if (input.length() == 1) {
                    input = "0" + input;
                }
                String result = bureauDataResponse.getAccountTypeMap().get(input).getName();
                result = result.substring(0, 1).toUpperCase() + result.substring(1).toLowerCase();
                type.setText(result);

                RichTextValue value = (RichTextValue) activeLender.getDescription();
                value.setText(lender.getCreditMix());

                activeLender.setTitle(name);
                activeLender.setSubTitle(type);
                activeLender.setDescription(value);

                imageTextValueRenderableComponent.setValue(activeLender);

                imageTextCardsListWidgetDataActive.getCardsData().getCards().add(imageTextValueRenderableComponent);

            }
        }
        imageTextCardsListWidgetDataActive.getCardsData().getCards().remove(0);
        return imageTextCardsListWidgetDataActive;
    }

    public WidgetData buildInactiveLendersForCreditMixWidgetData(BureauDataResponse bureauDataResponse) throws JsonProcessingException {
        ImageTextCardsListWidgetData imageTextCardsListWidgetDataActive = ObjectMapperUtil.get().readValue(inactiveAccountJson, ImageTextCardsListWidgetData.class);

        int[] countOfActiveInactiveLender = countActiveLenders(bureauDataResponse);


        for (bureauLender lender : bureauDataResponse.getLenders()) {
            if (!lender.isActive()) {
                RenderableComponent<ImageTextValue> imageTextValueRenderableComponent = readStaticDataForInactiveLender();

                ImageTextValue activeLender = (ImageTextValue) imageTextValueRenderableComponent.getValue();

                RichTextValue name = (RichTextValue) activeLender.getTitle();
                String lenderName = lender.getName();
                name.setText(lenderName);

                RichTextValue type = (RichTextValue) activeLender.getSubTitle();
                String input = String.valueOf(lender.getAccountType());
                if (input == null || input.isEmpty()) {
                    throw new RuntimeException();
                }
                if (input.length() == 1) {
                    input = "0" + input;
                }
                String result = bureauDataResponse.getAccountTypeMap().get(input).getName();
                result = result.substring(0, 1).toUpperCase() + result.substring(1).toLowerCase();
                type.setText(result);

                RichTextValue value = (RichTextValue) activeLender.getDescription();
                value.setText(lender.getCreditMix());

                activeLender.setTitle(name);
                activeLender.setSubTitle(type);
                activeLender.setDescription(value);

                imageTextValueRenderableComponent.setValue(activeLender);

                imageTextCardsListWidgetDataActive.getCardsData().getCards().add(imageTextValueRenderableComponent);

            }
        }
        imageTextCardsListWidgetDataActive.getCardsData().getCards().remove(0);
        return imageTextCardsListWidgetDataActive;
    }




    private RenderableComponent<ImageTextValue> readStaticDataForActiveLender() throws JsonProcessingException {
        ImageTextCardsListWidgetData imageTextCardsListWidgetDataActive = ObjectMapperUtil.get().readValue(activeAccountJson, ImageTextCardsListWidgetData.class);
        return imageTextCardsListWidgetDataActive.getCardsData().getCards().get(0);
    }
    private RenderableComponent<ImageTextValue> readStaticDataForInactiveLender() throws JsonProcessingException {
        ImageTextCardsListWidgetData imageTextCardsListWidgetDataInactive = ObjectMapperUtil.get().readValue(inactiveAccountJson, ImageTextCardsListWidgetData.class);
        return imageTextCardsListWidgetDataInactive.getCardsData().getCards().get(0);
    }


    private int[] countActiveLenders(BureauDataResponse bureauDataResponse) {
        int[] countOfActiveInactiveLender = new int[2];
        int active = 0;
        int inactive = 0;
        for (bureauLender lender : bureauDataResponse.getLenders()) {
            if (lender.isActive()) {
                active++;
            } else {
                inactive++;
            }
        }
        countOfActiveInactiveLender[0] = active;
        countOfActiveInactiveLender[1] = inactive;
        return countOfActiveInactiveLender;
    }

    public String formatNumber(double value) {
        boolean isNegative = false;
        if (value < 0){
            value = value*-1;
            isNegative = true;
        }
        String result = "";
        if (value < 1000) {
            result = format(UNDER_THOUSAND_DECIMAL_PATTERN, value);
        } else {
            double hundreds = value % 1000;
            int other = (int) (value / 1000);
            result = format(",##", other) + ',' + format(OVER_THOUSAND_DECIMAL_FLOW, hundreds);
        }
        if (isNegative){
            result = "-" + result;
        }
        return  result;
    }
    private static String format(String pattern, Object value) {
        return new DecimalFormat(pattern).format(value);
    }
}
