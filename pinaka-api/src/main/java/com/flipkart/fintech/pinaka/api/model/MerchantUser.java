package com.flipkart.fintech.pinaka.api.model;

import com.google.common.base.Preconditions;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
@Builder
public class MerchantUser {
    private final String merchantKey;
    private final String merchantUserId;
    private final String smUserId;

    private MerchantUser(String merchantKey, String merchantUserId, String smUserId) {
        this.merchantKey = merchantKey;
        this.merchantUserId = merchantUserId;
        this.smUserId = smUserId;
    }

    public static MerchantUser getMerchantUser(String merchantKey, String merchantUserId, String smUserId) {
        return new MerchantUser(merchantKey, merchantUserId, smUserId);
    }

    public static MerchantUser getDefaultMerchantUserGivenMerchantAccountId(String merchantUserId) {
        Preconditions.checkNotNull(merchantUserId);
        return new MerchantUser(MerchantKeys.DEFAULT_MERCHANT_KEY, merchantUserId, null);
    }

    public static MerchantUser getEmptyUser() {
        return new MerchantUser(null, null, null);
    }

    public static class MerchantKeys {
        public static final String FLIPKART_MERCHANT_KEY = "FLIPKART";
        public static final String SHOPSY_MERCHANT_KEY = "SHOPSY";
        public static final String MYNTRA_MERCHANT_KEY = "MYNTRA";
        public static final String DEFAULT_MERCHANT_KEY = "CALM";
    }

}
