package com.flipkart.fintech.pinaka.api.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.enums.Cohort;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class EnhancedDueDiligenceConfig {

    private boolean enabled;
    private boolean underwritingMandatory;
    private BigDecimal minApprovalLimit;
    private List<Cohort> cohortList;

}
