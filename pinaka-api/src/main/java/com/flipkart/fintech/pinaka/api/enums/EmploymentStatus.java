package com.flipkart.fintech.pinaka.api.enums;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 05/07/18.
 */
public enum EmploymentStatus {
    SELF_EMPLOYED("Self Employed"),
    SALARIED_EMPLOYEE("Salaried"),
    SALARIED("Salaried"),
    OTHER_PROFESSIONAL("Others"),
    INDEPENDENT_WORKER("Self Employed"),
    PROFESSIONAL("Self Employed"),
    BUSINESS_OWNER("Self Employed"),
    STUDENT("Others"),
    RETIRED("Others"),
    HOMEMAKER("Others"),
    NONE_OF_THE_ABOVE("Others");

    private String title;

    private static Map<String, EmploymentStatus> map = Maps.newHashMap();

    static {
        map.put("Self Employed", SELF_EMPLOYED);
        map.put("Salaried", SALARIED_EMPLOYEE);
        map.put("Others", OTHER_PROFESSIONAL);
        map.put("INDEPENDENT_WORKER", INDEPENDENT_WORKER);
        map.put("PROFESSIONAL", PROFESSIONAL);
        map.put("BUSINESS_OWNER", BUSINESS_OWNER);
        map.put("SELF EMPLOYED", SELF_EMPLOYED);
        map.put("SALARIED", SALARIED);
        map.put("SELF_EMPLOYED", SELF_EMPLOYED);
        map.put("CBC_BUSINESS_OWNER", SELF_EMPLOYED);
        map.put("STUDENT", STUDENT);
        map.put("RETIRED", RETIRED);
        map.put("HOMEMAKER", HOMEMAKER);
        map.put("NONE_OF_THE_ABOVE", NONE_OF_THE_ABOVE);
    }

    EmploymentStatus(String title){
        this.title = title;
    }

    public static EmploymentStatus fromString(String value){
        return map.get(value);
    }

    public static String getValue(EmploymentStatus employmentStatus){
        return employmentStatus.title.toUpperCase();
    }

    public String getTitle(){
        return this.title;
    }
}
