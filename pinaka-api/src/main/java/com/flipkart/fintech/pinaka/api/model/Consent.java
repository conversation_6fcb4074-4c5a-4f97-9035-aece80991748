package com.flipkart.fintech.pinaka.api.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 05/07/18.
 */
public class Consent {

    private ConsentType consentType;

    @JsonProperty("isConsentGiven")
    private boolean isConsentGiven;

    public ConsentType getConsentType() {
        return consentType;
    }

    public void setConsentType(ConsentType consentType) {
        this.consentType = consentType;
    }

    @JsonIgnore
    public boolean isConsentGiven() {
        return isConsentGiven;
    }

    public void setConsentGiven(boolean isConsentGiven) {
        this.isConsentGiven = isConsentGiven;
    }

    public enum ConsentType{
        EKYC_CONSENT,
        CIBIL_CONSENT,
        CREDIT_LIMIT,
        TNC_CONSENT,
        DATA_DECLARATION,
        STORE_CREDIT_CARD_INFO,
        ADDRESS_CONSENT,
        APPLY_CONSENT,
        CUSTOMER_DETAILS_CONSENT,
        CONSOLE_CONSENT,
        AUTOMATED_QUERY,
        ACCOUNT_SUMMARY,
        SEND_EMAIL,
        BLOCK_CARD,
        CREDIT_SETTINGS,
        CARD_LINKING_CONSENT,
        CBC_AXIS_ETCC_FETCH_DEMOG_CONSENT,
        SAVE_CARD,
        EMI_CONVERSION
    }

}
