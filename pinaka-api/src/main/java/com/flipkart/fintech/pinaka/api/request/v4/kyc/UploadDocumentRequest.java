package com.flipkart.fintech.pinaka.api.request.v4.kyc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.stratum.api.models.common.EncryptionKeyData;
import com.flipkart.fintech.stratum.api.models.kyc.xmlKyc.KycDocumentDetails;
import com.google.common.collect.Maps;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 01/10/20.
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@AllArgsConstructor
public class UploadDocumentRequest extends KycUpdateRequest {

    private KycDocumentDetails kycDocumentDetails;
    private EncryptionKeyData encryptionKeyData;

    public Map<String, String> createD42MetaData() {
        Map<String, String> metaData = Maps.newHashMap();
        metaData.put("password", kycDocumentDetails.getPassword());
        metaData.put("encKeyRef", encryptionKeyData.getEncKeyRef());
        metaData.put("encKey", encryptionKeyData.getEncKey());
        return metaData;
    }

}
