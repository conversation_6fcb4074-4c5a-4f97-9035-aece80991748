package com.flipkart.fintech.pinaka.api.model;

import com.flipkart.fintech.pinaka.api.enums.FormFieldType;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.<PERSON> on 12/08/18.
 */
public class NumberFieldDetails extends FormFieldDetails{

    private String value;

    public NumberFieldDetails(){
        super(FormFieldType.NUMBER);
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
