package com.flipkart.fintech.pinaka.api.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotNull;
import java.util.Map;

@JsonIgnoreProperties(ignoreUnknown = true)
public class AddressDetailsForPopulate {

    @JsonProperty("addressData")
    @NotNull
    private Map<String, Object> addressData;

    public Map<String, Object> getAddressData() {
        return addressData;
    }

    public void setAddressData(Map<String, Object>  addressData) {
        this.addressData = addressData;
    }

}