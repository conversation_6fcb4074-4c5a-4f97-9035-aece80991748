package com.flipkart.fintech.pinaka.api.model;

import com.flipkart.fintech.pinaka.api.enums.TriggerStatus;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TriggerData {
    Long version;
    int executionsLeft;
    TriggerStatus triggerStatus;
    List<Long> executedTriggerVersionList = Lists.newArrayList();
}
