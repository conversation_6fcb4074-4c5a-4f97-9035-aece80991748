package com.flipkart.fintech.pinaka.api.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.profile.model.EmploymentType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Builder
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class UserProfileObject {
  @JsonProperty("userEnteredPincode")
  private Integer userEnteredPincode;
  @JsonProperty("merchantUserId")
  private String merchantUserId;
  @JsonProperty("smUserId")
  private String smUserId;
  @JsonProperty("profileId")
  private Long profileId;
  @JsonProperty("firstName")
  private String firstName;
  @JsonProperty("lastName")
  private String lastName;
  @JsonProperty("gender")
  private String gender;
  @JsonProperty("dob")
  private String dob;
  @JsonProperty("employmentType")
  private EmploymentType employmentType;
  @JsonProperty("phoneNo")
  private String phoneNo;
  @JsonProperty("pan")
  private String pan;
  @JsonProperty("email")
  private String email;
  @JsonProperty("shippingPincode")
  private Integer shippingPincode;
}
