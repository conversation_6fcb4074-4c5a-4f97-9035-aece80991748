package com.flipkart.fintech.pinaka.api.model.bigfoot;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.common.enums.Tenant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@NoArgsConstructor
@Data
@Builder
@AllArgsConstructor
public class PlApplication {
    @JsonProperty("lender")
    private String lender;

    @JsonProperty("created_at")
    private String createdAt;

    @JsonProperty("updated_at")
    private String updatedAt;

    @JsonProperty("account_id")
    private String accountId;

    @JsonProperty("sm_user_id")
    private String smUserId;

    @JsonProperty("step_status")
    private String stepStatus;

    @JsonProperty("tenant")
    private Tenant tenant;

    @JsonProperty("product_type")
    private String productType;

    @JsonProperty("lsp_application_id")
    private String lspApplicationId;

    @JsonProperty("lender_application_id")
    private String lenderApplicationId;

    @JsonProperty("state_transition_time")
    private long stateTransitionTime;

    @JsonProperty("step")
    private String step;

    @JsonProperty("step_status_description")
    private String stepStatusDescription;

    @JsonProperty("application_state")
    private String applicationState;

    @JsonProperty("reject_reason")
    private String rejectReason;

    @JsonProperty("merchant_id")
    private String merchantId;
}
