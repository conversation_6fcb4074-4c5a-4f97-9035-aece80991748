package com.flipkart.fintech.pinaka.api.model;

import com.flipkart.fintech.pinaka.api.enums.Channel;
import com.flipkart.fintech.pinaka.api.enums.RuleContext;
import lombok.Data;

import java.util.List;

@Data
public class UnderwritingRuleConfig {
    // these are pivots on which we will have to decide rule id
    private Boolean fullKycCompleted;
    private List<Channel> channels;
    private RuleContext ruleContext;
    private String ruleName;
}
