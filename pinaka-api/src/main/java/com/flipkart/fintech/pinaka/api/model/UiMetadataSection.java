package com.flipkart.fintech.pinaka.api.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class UiMetadataSection {

    @JsonProperty("name")
    private String name;

    @JsonProperty("order")
    private int order;

    @JsonProperty("image_urls")
    private List<String> imageUrls;

    @JsonProperty("video_urls")
    private List<String> videoUrls;

    public UiMetadataSection() {
    }

    public UiMetadataSection(String name, int order, List<String> imageUrls, List<String> videoUrls) {
        this.name = name;
        this.order = order;
        this.imageUrls = imageUrls;
        this.videoUrls = videoUrls;
    }
}
