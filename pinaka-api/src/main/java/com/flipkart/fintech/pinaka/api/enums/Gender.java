package com.flipkart.fintech.pinaka.api.enums;

import com.google.common.collect.Maps;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.<PERSON> on 06/09/17.
 */
public enum Gender {

    M("male"),
    F("female"),
    T("transgender");

    private String value;

    private static Map<String, Gender> map = Maps.newHashMap();

    static {
        map.put("male", M);
        map.put("female", F);
        map.put("transgender", T);
    }

    private Gender(String value){
        this.value = value;
    }

    public static Gender fromString(String value){
        if(value == null) return null;
        return map.get(value);
    }

    public static String getValue(Gender gender){
        return gender.value.toUpperCase();
    }
}
