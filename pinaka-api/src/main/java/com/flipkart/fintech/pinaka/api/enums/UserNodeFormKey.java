package com.flipkart.fintech.pinaka.api.enums;


/**
 * AMS user task action form key
 */
public enum UserNodeForm<PERSON>ey {
    EKYC_PAN_AADHAAR_SUBMIT,
    REVIEW_SCREEN,
    <PERSON>ETCH_AADHAAR_OTP,
    GET_EXTERNAL_USER_CONSENT,
    WAITING_BUREAU_INSIGHTS,
    COLLECT_PERMISSIONS,
    WAIT_FOR_SMS_UPLOAD,
    KYC_REJECTED_RETRIABLE,
    AAD<PERSON>AR_XML_KYC_PAN_SUBMIT,
    INITIATE_AADHAAR_VERIFICATION,
    WAITING_FOR_AADHAAR_VERIFICATION,
    COLLECT_BANK_ACCOUNT_DATA,
    WAITING_FOR_APPLICATION_COMPLETION,
    EKYC_PAN_SUBMIT,
    EKYC_GENERATE_OTP,

    CKYC_PAN_SUBMIT,
    CKYC_GENERATE_OTP,
    CKYC_VERIFY_OTP,
    INITIATE_PENNY_DROP,
    WAITING_FOR_ONBOARDING_COMPLETION,
    PENDING_IDFC_LOAN_CREATION;
}
