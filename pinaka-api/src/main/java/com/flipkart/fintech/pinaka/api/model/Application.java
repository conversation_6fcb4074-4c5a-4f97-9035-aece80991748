package com.flipkart.fintech.pinaka.api.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.enums.AddressLocationType;
import com.flipkart.fintech.pinaka.api.enums.CardUsageType;
import com.flipkart.fintech.pinaka.api.enums.EmploymentStatus;
import com.flipkart.fintech.pinaka.api.enums.Gender;

import com.flipkart.fintech.pinaka.api.enums.MaritalStatus;
import com.flipkart.fintech.pinaka.api.enums.YesNoType;
import com.flipkart.sensitive.annotation.SensitiveField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.Date;
import java.util.List;

/**
 * Created by su<PERSON><PERSON><PERSON>.r on 16/08/17.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Application{

    @JsonProperty("first_name")
    private String firstName;

    @JsonProperty("last_name")
    private String lastName;

    @JsonProperty("middle_name")
    private String middleName;

    @JsonProperty("card_name")
    private String cardName;

    @JsonProperty("mothers_name")
    private String mothersName;

    @JsonProperty("fathers_first_name")
    private String fathersFirstName;

    @JsonProperty("fathers_last_name")
    private String fathersLastName;

    @JsonProperty("caste")
    private String caste;

    @JsonProperty("DOB")
    private Date dob;

    @JsonProperty("gender")
    private Gender gender;

    @JsonProperty("marital_status")
    private MaritalStatus maritalStatus;

    @JsonProperty("email")
    @Pattern(regexp = "^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,6}$", flags = Pattern.Flag.CASE_INSENSITIVE,
            message = "Invalid email")
    @SensitiveField
    private String email;

    @JsonProperty("phone_number")
    @SensitiveField
    private String phoneNumber;

    @NotNull
    @JsonProperty("pan_number")
    @SensitiveField
    private String panNumber;

    @JsonProperty("pan_verified_name")
    private String panVerifiedName;

    @JsonProperty("addresses")
    private List<Address> addressList;

    @JsonProperty("employment_status")
    private EmploymentStatus employmentStatus;

    @JsonProperty("monthly_salary")
    private Double monthlySalary;

    private List<FormFieldDetails> additionalDetails;

    @JsonProperty("education")
    private String education;

    @JsonProperty("annual_income")
    private String annualIncome;

    @JsonProperty("current_organization_name")
    private String currentOrganizationName;

    @JsonProperty("tenure_in_current_organization")
    private String tenureInCurrentOrganization;

    @JsonProperty("industry_type")
    private  String industryType;

    @JsonProperty("name_of_business")
    private  String nameOfBusiness;

    @JsonProperty("ownership_type")
    private  String ownershipType;

    @JsonProperty("nature_of_business")
    private  String natureOfBusiness;

    @JsonProperty("delivery_address")
    private YesNoType deliveryAddress;

    @JsonProperty("permanent_address")
    private YesNoType permanentAddress;

    @JsonProperty("address_type")
    private AddressLocationType addressLocationType;

    @JsonProperty("delivery_address_type")
    private AddressLocationType deliveryAddressLocationType;

    @JsonProperty("card_usage_type")
    private CardUsageType cardUsageType;
    @JsonProperty("increase_credit_limit")

    private boolean increaseCreditLimit;

    @JsonProperty("card_protection_plan")
    private boolean cardProtectionPlan;

    @JsonProperty("employment_sector")
    private String employmentSector;

    @JsonProperty("years_in_business")
    private String yearsInBusiness;

    @JsonProperty("existing_employer")
    private boolean existingEmployer;

    @JsonProperty("residence_type")
    private String residenceType;

    @JsonProperty("is_official")
    private String isOfficial;

    @JsonProperty("name_of_official")
    private String nameOfOffical;

    @JsonProperty("name_of_bank")
    private String nameOfBank;

    @JsonProperty("position_in_bank")
    private String positionInBank;

    @JsonProperty("relationship_with_official")
    private String relationshipWithOfficial;
}
