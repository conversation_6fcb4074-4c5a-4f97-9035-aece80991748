package com.flipkart.fintech.pinaka.api.enums;

import com.fasterxml.jackson.annotation.JsonCreator;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 16/08/17.
 */
public enum ApplicationStatus {
    CREATED("created"),
    PROCESSING("processing"),
    APPROVED("approved"),
    UNDERWRITING_APPROVED("underwriting_approved"),
    CONDITIONALLY_APPROVED("conditionally_approved"),
    SOFT_APPROVED("soft_approved"),
    REJECTED("rejected"),
    ELIGIBILITY_REJECTED("eligibility_rejected"),
    DEFERRED("deferred"),
    KYC_REJECTED("kyc_rejected"),
    AUTOPAY_SETUP_INITIATED("autopay_setup_initiated"),
    AUTOPAY_SETUP_PROCESSING("autopay_setup_processing"),
    AUTOPAY_SETUP_COMPLETED("autopay_setup_completed"),
    AUTOPAY_SETUP_FAILED_RETRY("autopay_setup_completed"),
    COMPLETED("completed"),
    CHECK_ELIGIBILITY("check_eligibility"),
    CHECK_ELIGIBILITY_WITH_ALT_DATA("check_eligibility_with_alt_data"),
    CHECK_ELIGIBILITY_WITH_LENDER("check_eligibility"),
    CHECK_ELIGIBILITY_WITH_AVAILABLE_DATA("check_eligibility_with_alt_data"),
    CHECK_ELIGIBILITY_WITH_BUREAU("check_eligibility_with_bureau"),
    CHECK_ELIGIBILITY_WITH_SMS("check_eligibility_with_sms"),
    CHECK_ELIGIBILITY_WITH_TS("check_eligibility_with_ts"),
    PRE_CHECK_ELIGIBILITY_VALIDATIONS("pre_check_eligibility_validations"),
    CHECK_ELIGIBILITY_WITH_UNDERWRITING("check_eligibility_with_underwriting"),
    USER_CONFIRMATION("user_confirmation"),
    KYC_PENDING("kyc_pending"),
    KYC_REUPLOAD("kyc_reupload"),
    KYC_CONFIRMATION("kyc_confirmation"),
    DISCARDED("user_discarded"),
    DECLINED("user_declined"),
    FAILED("failed"),
    FAILED_RETRY("failed_retry"),
    WAITING_FOR_BUREAU_CONSENT("waiting_for_bureau_consent"),
    INITIATE_BUREAU_COLLECTION("initiate_bureau_collection"),
    WAITING_FOR_BUREAU_INSIGHTS("waiting_for_bureau_insights"),
    WAITING_FOR_INSIGHTS("waiting_for_insights"),
    WAITING_FOR_SMS_INSIGHTS("waiting_for_sms_insights"),
    WAITING_FOR_SMS_PERMISSIONS("waiting_for_sms_permissions"),
    WAITING_FOR_DEVICE_PERMISSIONS("waiting_for_device_permissions"),
    WAITING_FOR_PERMISSIONS("waiting_for_permissions"),
    WAITING_FOR_PENNY_DROP("waiting_for_penny_drop"),
    WAITING_FOR_ENHANCED_DUE_DILIGENCE("waiting_for_enhanced_due_diligence"),
    WAITING_FOR_TS_CONSENT("waiting_for_ts_consent"),
    WAITING_FOR_TS_CONSENT_VERIFICATION("waiting_for_ts_consent_verification"),
    WAITING_FOR_TS_INSIGHTS("waiting_for_ts_insights"),
    ACCESS_CODE_GENERATION("account_access_code_generation"),
    USER_CONSENT("user_consent"),
    LENDER_APPROVED("final_lender_approved"),
    PAN_VALIDATION_FAILED("invalid_pan"),
    CHECK_ELIGIBILITY_DONE("check_eligibility_done"),
    FETCH_DETAILS_CONSENT("fetch_details_consent"),
    USER_INPUT("user_input"),
    PENDING_IDFC_LIMIT_UPDATE ("pending_idfc_limit_update"),
    PENDING_CONSENT_CAPTURE ("pending_consent_capture"),
    USER_PERSONAL_DETAILS_INPUT("user_personal_details_input"),
    USER_PROFESSIONAL_DETAILS_INPUT("user_professional_details_input"),
    USER_CONTACT_DETAILS_INPUT("user_contact_details_input"),
    USER_ADDITIONAL_DETAILS_INPUT("user_additional_details_input"),
    APPLY_CONSENT("apply_consent"),
    OTP_TRIGGERED("otp_triggered"),
    TELE_VERIFICATION_PENDING("tele_verification_pending"),
    REFER_PENDING("refer_pending"),
    KYC_PICKUP_SCHEDULED("kyc_scheduled"),
    EKYC_PASSED("ekyc_passed"),
    VIDEO_KYC_ELIGIBLE("video_kyc_eligible"),
    AADHAAR_OTP_GENERATED("aadhaar_otp_generated"),
    CKYC_PENDING("ckyc_pending"),
    XML_KYC_PENDING("xml_kyc_pending"),
    PENNY_DROP_PENDING("penny_drop_pending"),
    WAITING_FOR_LENDER_CALLBACK("waiting_for_lender_callback"),
    REJECTED_RETRIABLE("rejected_retriable"),


    //Interim statuses
    KYC_CONFIRMATION_IN_PROGRESS("kyc_confirmation_in_progress"),
    DISCARD_IN_PROGRESS("discard_in_progress"),
    USER_CONFIRMATION_IN_PROGRESS("user_confirmation_in_progress"),
    CHECK_ELIGIBILITY_IN_PROGRESS("check_eligibility_in_progress"),
    USER_CONSENT_IN_PROGRESS("user_consent_in_progress"),
    ACCESS_CODE_GENERATION_IN_PROGRESS("account_access_code_generation_in_progress"),
    LOAN_CREATION_IN_PROGRESS("loan_creation_in_progress"),
    POST_PROCESSING_PENDING("post_processing_pending"),
    DEVICE_DATA_COLLECTION_IN_PROGRESS("device_data_collection_in_progress"),
    BUREAU_COLLECTION_IN_PROGRESS("bureau_collection_in_progress"),
    KYC_REJECTED_RETRIABLE("kyc_rejected_retriable"),
    KYC_DETAILS_RECEIVED("kyc_details_received"),
    PAN_DETAILS_RECEIVED("pan_details_received"),
    INVALID_PAN("invalid_pan"),
    KYC_DOCUMENT_RECEIVED("kyc_document_received"),
    AADHAAR_OTP_VERIFIED("aadhaar_otp_verified"),
    AADHAAR_DETAILS_RECEIVED("aadhaar_details_received"),
    CKYC_SEARCH_COMPLETED("ckyc_search_completed"),
    WAITING_FOR_DEVICE_DATA_INSIGHTS("waiting_for_device_data_insights"),
    SAVE_CONSENT("save_consent"),

    //DUMMY state
    NOT_CREATED("not_created"),

    COHORT_REJECTION("cohort_rejection"),

    ENTER_PAN("enter_pan"),
    PAN_NUMBER_RECEIVED("pan_number_received"),
    XML_COLLECT_PAN("xml_collect_pan"),
    XML_PAN_DETAILS_RECEIVED("xml_pan_details_received"),
    EKYC_PAN_DETAILS_RECEIVED("ekyc_pan_details_received"),
    ACCOUNT_DETAILS_RECEIVED("account_details_received"),
    INITIATED("initiated"),
    PENNY_DROP_DONE("penny_drop_done"),

    //Note : this sate is not currently being set for application but added keeping in mind future needs of CBC as it is
    // required for NPS survey for CBC (as we don't want to send post approval survey to those users whose card is in blocked state)
    BLOCKED ("blocked"),
    //todo abhishek :
    // 1)segregate common, EFA and CBC specific states when refactoring sprint and below comment by para is outdated.
    // 2)Also remove "value field" (we should keep it only if it gives some descriptive information or if this value is being used)
    
    EXCLUSION_CRITERIA_FAILED("exclusion_criteria_failed"),

    CARD_LINKING_IN_PROGRESS("card_linking_in_progress"),
    CARD_LINKING_CONSENT("card_linking_consent"),
    OTP_ATTEMPTS_EXCEEDED("otp_attempts_exceeded"),
    CHECK_DUE_DILIGENCE_STATUS("check_due_diligence_status"),
    COMPLETED_OFFLINE("completed_offline"),

    //LCM State
    SUCCESS("SUCCESS");

    /*
    CBC APPLICATION STATES:
    CREATED
    ELIGIBILITY_REJECTED - user not eligible based on Axis response
    FETCH_DETAILS_CONSENT - first otp triggered
    ELIGIBILITY_REJECTED - card no not eligible
    USER_INPUT - user is on application form page
    APPLY_CONSENT - second otp triggered
    CHECK_ELIGIBILITY - application submitted, got caseRefId
    KYC_PENDING / REFER_PENDING / TELE_VERIFICATION_PENDING / KYC_CONFIRMATION_IN_PROGRESS / KYC_PICKUP_SCHEDULED
    APPROVED - application approved, got appSerNo
    COMPLETED - payment instrument fetched & added to saved cards
    REJECTED - lender reject
    DISCARDED - ttl timeout
     */

    private String value;

    private ApplicationStatus(String value){
        this.value = value;
    }

    @JsonCreator
    public ApplicationStatus fromString(String value){
        return ApplicationStatus.valueOf(value);
    }


}
