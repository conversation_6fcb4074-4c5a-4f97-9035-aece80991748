package com.flipkart.fintech.pinaka.api.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.ToString;

@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class KycSchedulerData {
    @JsonProperty("kyc_scheduler_slot")
    private String kycSchedulerSlot;
    @JsonProperty("kyc_scheduler_address")
    @ToString.Exclude
    private Address kycSchedulerAddress;
    @ToString.Exclude
    @JsonProperty("kyc_scheduler_alternate_contact")
    private String kycSchedulerAlternateContact;
    @ToString.Exclude
    @JsonProperty("kyc_scheduler_contact")
    private String kycSchedulerContact;

}
