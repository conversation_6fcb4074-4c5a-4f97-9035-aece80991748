package com.flipkart.fintech.pinaka.api.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.pinaka.api.enums.ApplicationStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
@AllArgsConstructor
@NoArgsConstructor
public class LcmData {
    private Date approvedDate;
    private String cardSerno;
    private String validationToken;
    private String deviceId;
    private String applicationId;
    private ApplicationStatus status;
}
