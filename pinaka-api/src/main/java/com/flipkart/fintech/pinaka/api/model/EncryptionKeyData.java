package com.flipkart.fintech.pinaka.api.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotNull;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 29/01/18.
 */
public class EncryptionKeyData {

    @NotNull
    @JsonProperty("enc_key_ref")
    private String encKeyRef;

    @NotNull
    @JsonProperty("enc_key")
    private String encKey;

    public String getEncKeyRef() {
        return encKeyRef;
    }

    public void setEncKeyRef(String encKeyRef) {
        this.encKeyRef = encKeyRef;
    }

    public String getEncKey() {
        return encKey;
    }

    public void setEncKey(String encKey) {
        this.encKey = encKey;
    }
}
