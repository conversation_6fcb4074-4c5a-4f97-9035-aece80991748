package com.flipkart.fintech.pinaka.api.enums;

/**
 * Created by kunal.keshwani on 06/05/19.
 */
public enum AddressLocationType {
    CURRENT("current"),
    WORK("work");

    private String value;

    AddressLocationType(String value) {
        this.value = value;
    }

    public static AddressLocationType fromString(String value) {
        for(AddressLocationType addressLocationType : AddressLocationType.values()) {
            if(addressLocationType.value.equals(value)) {
                return addressLocationType;
            }
        }
        return null;
    }
}
