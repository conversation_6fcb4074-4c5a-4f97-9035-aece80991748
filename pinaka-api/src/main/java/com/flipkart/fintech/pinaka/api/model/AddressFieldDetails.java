package com.flipkart.fintech.pinaka.api.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.enums.FormFieldType;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.<PERSON> on 12/08/18.
 */
public class AddressFieldDetails extends FormFieldDetails {
    public AddressFieldDetails() {
        super(FormFieldType.ADDRESS);
    }

    @JsonProperty("values")
    private List<Address> addresses;

    public List<Address> getAddresses() {
        return addresses;
    }

    public void setAddresses(List<Address> addresses) {
        this.addresses = addresses;
    }
}
