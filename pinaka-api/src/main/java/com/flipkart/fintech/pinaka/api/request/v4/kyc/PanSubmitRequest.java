package com.flipkart.fintech.pinaka.api.request.v4.kyc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.flipkart.fintech.stratum.api.models.common.EncryptionKeyData;
import lombok.*;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PanSubmitRequest extends KycUpdateRequest {

    @NonNull
    private String encryptedPanNumber;
    private EncryptionKeyData encryptionKeyData;

}
