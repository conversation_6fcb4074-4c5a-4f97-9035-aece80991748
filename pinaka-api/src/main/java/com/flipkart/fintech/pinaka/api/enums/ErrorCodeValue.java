package com.flipkart.fintech.pinaka.api.enums;

import java.util.HashMap;
import java.util.Map;

public enum ErrorCodeValue {
    B018027("ETB customer not eligible");

    private final String errorValue;
    private static Map<String, ErrorCodeValue> map = new HashMap<>();

    static {
        map.put("ETB customer not eligible", B018027);
    }

    private ErrorCodeValue(String errorValue) {
        this.errorValue = errorValue;
    }

    public String getErrorValue() {
        return errorValue;
    }

    public ErrorCodeValue getFromErrorValue(String errorValue) {
        return map.get(errorValue);
    }
}
