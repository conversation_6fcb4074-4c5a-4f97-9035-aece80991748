package com.flipkart.fintech.pinaka.api.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import lombok.Data;

import java.sql.Timestamp;

/**
 * <AUTHOR> - 28/08/19
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
public class Whitelist {

    @JsonProperty("product")
    private ProductType productType;

    @JsonProperty("lender")
    private String lender;

    @JsonProperty("whitelist_name")
    private String whitelistName;

    @JsonProperty("whitelist_desc")
    private String whitelistDesc;

    @JsonProperty("created_at")
    private Timestamp createdAt;

    @JsonProperty("updated_at")
    private Timestamp updatedAt;

    @JsonProperty("merchant_key")
    private String merchantKey;

    private boolean enabled;
}
