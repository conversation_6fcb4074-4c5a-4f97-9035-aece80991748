package com.flipkart.fintech.pinaka.api.enums;

import com.fasterxml.jackson.annotation.JsonCreator;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.<PERSON> on 22/08/17.
 */
public enum AddressType {
    PA("permanent_address"),
    CA("current_address"),
    BOTH("both"),
    RESIDENTIAL("HOME"),
    OFFICE("WORK");

    private static Map<String, AddressType> map = new HashMap<>();

    static {
        map.put("permanent_address", PA);
        map.put("current_address", CA);
        map.put("both", BOTH);
        map.put("HOME", RESIDENTIAL);
        map.put("WORK", OFFICE);
    }

    private String value;

    private AddressType(String value){
        this.value = value;
    }

    public String getValue() {
        return this.value;
    }

    @JsonCreator
    public AddressType fromString(String value){
        return AddressType.valueOf(value);
    }

    public static AddressType fromValue(String value) {
        return map.get(value);
    }

}
