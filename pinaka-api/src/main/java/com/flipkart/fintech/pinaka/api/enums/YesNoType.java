package com.flipkart.fintech.pinaka.api.enums;

/**
 * Created by kunal.keshwani on 06/05/19.
 */
public enum YesNoType {
    YES("yes"),
    NO("no");

    private String value;

    YesNoType(String value) {
        this.value = value;
    }

    public static YesNoType fromString(String value) {
        for(YesNoType yesNoType : YesNoType.values()) {
            if(yesNoType.value.equals(value)) {
                return yesNoType;
            }
        }
        return null;
    }
}
