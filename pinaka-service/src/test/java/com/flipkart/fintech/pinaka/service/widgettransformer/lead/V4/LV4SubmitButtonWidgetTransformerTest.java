package com.flipkart.fintech.pinaka.service.widgettransformer.lead.V4;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.common.bqIngestor.BqIngestionHelper;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.pagedatasource.InitialUserReviewDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.LeadPageDataSource;
import com.flipkart.fintech.pinaka.service.response.LeadPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3Util;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4SubmitButtonWidgetTransformer;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.GroupedFormWidgetData;
import com.supermoney.schema.PinakaService.LeadV3Events;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.whenNew;

@RunWith(PowerMockRunner.class)
@PrepareForTest({
        LV4SubmitButtonWidgetTransformer.class,
        ObjectMapperUtil.class,
        LV3Util.class,
        InitialUserReviewDataSource.class,
        LeadPageDataSource.class
})
public class LV4SubmitButtonWidgetTransformerTest {

    @Mock
    private ApplicationDataResponse applicationDataResponse;
    @Mock
    private LeadPageDataSourceResponse leadPageDataSourceResponse;
    @Mock
    private ReviewUserDataSourceResponse reviewUserDataSourceResponse;
    @Mock
    private ProfileDetailedResponse profile;
    @Mock
    private Decrypter decrypter;
    @Mock
    private BqIngestionHelper bqIngestionHelper;
    @Mock
    private ObjectMapper objectMapper;
    @Mock
    private GroupedFormWidgetData mockWidgetData;
    @Mock
    private InitialUserReviewDataSource initialUserReviewDataSource;
    @Mock
    private LeadPageDataSource leadPageDataSource;

    private LV4SubmitButtonWidgetTransformer transformer;

    @Before
    public void setup() throws Exception {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(LV3Util.class);
        PowerMockito.mockStatic(ObjectMapperUtil.class);

        // Mock LV3Util.getLeadEvents to return a valid LeadV3Events
        PowerMockito.when(LV3Util.class, "getLeadEvents",
                any(ApplicationDataResponse.class), anyString(), anyString(), anyString())
                .thenReturn(LeadV3Events.newBuilder().build());

        // Mock ObjectMapperUtil
        when(ObjectMapperUtil.get()).thenReturn(objectMapper);

        // Mock data sources
        whenNew(InitialUserReviewDataSource.class).withNoArguments().thenReturn(initialUserReviewDataSource);
        whenNew(LeadPageDataSource.class).withNoArguments().thenReturn(leadPageDataSource);
        when(initialUserReviewDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(reviewUserDataSourceResponse);
        when(leadPageDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(leadPageDataSourceResponse);

        transformer = new LV4SubmitButtonWidgetTransformer(decrypter, bqIngestionHelper);
    }

    @Test
    public void testBuildWidgetGroupData_WithValidData() throws Exception {
        // Setup test data
        setupValidApplicationDataResponse();
        setupValidReviewUserDataSourceResponse();
        setupValidLeadPageDataSourceResponse();
        
        // Mock JSON parsing
        when(objectMapper.readValue(anyString(), eq(GroupedFormWidgetData.class)))
                .thenReturn(mockWidgetData);
        when(mockWidgetData.getSubmitButton()).thenReturn(null);
        
        // Execute
        GroupedFormWidgetData result = transformer.buildWidgetGroupData(applicationDataResponse);
        
        // Verify
        assertNotNull(result);
        verify(bqIngestionHelper).insertLeadEvents(any(LeadV3Events.class));
    }

    @Test
    public void testBuildWidgetGroupData_WithUserProfile() throws Exception {
        // Setup test data with user profile
        setupValidApplicationDataResponse();
        setupReviewUserDataSourceResponseWithProfile();
        setupLeadPageDataSourceResponseWithProfile();
        
        // Mock JSON parsing
        when(objectMapper.readValue(anyString(), eq(GroupedFormWidgetData.class)))
                .thenReturn(mockWidgetData);
        when(mockWidgetData.getSubmitButton()).thenReturn(null);
        
        // Execute
        GroupedFormWidgetData result = transformer.buildWidgetGroupData(applicationDataResponse);
        
        // Verify
        assertNotNull(result);
        verify(bqIngestionHelper).insertLeadEvents(any(LeadV3Events.class));
    }

    @Test
    public void testBuildWidgetGroupData_WithQueryParams() throws Exception {
        // Setup test data with query params
        setupValidApplicationDataResponse();
        setupReviewUserDataSourceResponseWithQueryParams();
        setupValidLeadPageDataSourceResponse();
        
        // Mock JSON parsing
        when(objectMapper.readValue(anyString(), eq(GroupedFormWidgetData.class)))
                .thenReturn(mockWidgetData);
        when(mockWidgetData.getSubmitButton()).thenReturn(null);
        
        // Execute
        GroupedFormWidgetData result = transformer.buildWidgetGroupData(applicationDataResponse);
        
        // Verify
        assertNotNull(result);
        verify(bqIngestionHelper).insertLeadEvents(any(LeadV3Events.class));
    }

    @Test
    public void testBuildWidgetGroupData_WithApplicationData() throws Exception {
        // Setup test data with application data
        setupApplicationDataResponseWithData();
        setupValidReviewUserDataSourceResponse();
        setupValidLeadPageDataSourceResponse();
        
        // Mock JSON parsing
        when(objectMapper.readValue(anyString(), eq(GroupedFormWidgetData.class)))
                .thenReturn(mockWidgetData);
        when(mockWidgetData.getSubmitButton()).thenReturn(null);
        
        // Execute
        GroupedFormWidgetData result = transformer.buildWidgetGroupData(applicationDataResponse);
        
        // Verify
        assertNotNull(result);
        verify(bqIngestionHelper).insertLeadEvents(any(LeadV3Events.class));
    }

    @Test(expected = PinakaException.class)
    public void testBuildWidgetGroupData_JsonProcessingException() throws Exception {
        // Setup test data
        setupValidApplicationDataResponse();
        setupValidReviewUserDataSourceResponse();
        setupValidLeadPageDataSourceResponse();
        
        // Mock JSON parsing to throw exception
        when(objectMapper.readValue(anyString(), eq(GroupedFormWidgetData.class)))
                .thenThrow(new JsonProcessingException("JSON parsing failed") {});
        
        // Execute - should throw PinakaException
        transformer.buildWidgetGroupData(applicationDataResponse);
    }

    @Test(expected = PinakaException.class)
    public void testBuildWidgetGroupData_RuntimeException() throws Exception {
        // Setup test data
        setupValidApplicationDataResponse();
        setupValidReviewUserDataSourceResponse();
        setupValidLeadPageDataSourceResponse();
        
        // Mock to throw runtime exception
        when(objectMapper.readValue(anyString(), eq(GroupedFormWidgetData.class)))
                .thenThrow(new RuntimeException("Unexpected error"));
        
        // Execute - should throw PinakaException
        transformer.buildWidgetGroupData(applicationDataResponse);
    }

    @Test
    public void testBuildWidgetGroupData_ExceptionHandling() throws Exception {
        // Setup test data
        setupValidApplicationDataResponse();
        setupValidReviewUserDataSourceResponse();
        setupValidLeadPageDataSourceResponse();
        
        // Mock JSON parsing to throw exception
        when(objectMapper.readValue(anyString(), eq(GroupedFormWidgetData.class)))
                .thenThrow(new RuntimeException("Test exception"));
        
        try {
            transformer.buildWidgetGroupData(applicationDataResponse);
            fail("Expected PinakaException to be thrown");
        } catch (PinakaException e) {
            // Verify error event is logged
            verify(bqIngestionHelper).insertLeadEvents(any(LeadV3Events.class));
            assertTrue(e.getMessage().contains("Error while building widget Group Data for LV4 Submit Button"));
        }
    }

    @Test
    public void testBuildWidgetGroupData_WithNullResponses() throws Exception {
        // Setup test data with null responses
        setupValidApplicationDataResponse();
        
        // Mock JSON parsing
        when(objectMapper.readValue(anyString(), eq(GroupedFormWidgetData.class)))
                .thenReturn(mockWidgetData);
        when(mockWidgetData.getSubmitButton()).thenReturn(null);
        
        // Execute
        GroupedFormWidgetData result = transformer.buildWidgetGroupData(applicationDataResponse);
        
        // Verify
        assertNotNull(result);
        verify(bqIngestionHelper).insertLeadEvents(any(LeadV3Events.class));
    }

    // Helper methods for setting up test data
    private void setupValidApplicationDataResponse() {
        when(applicationDataResponse.getSmUserId()).thenReturn("testUser123");
        when(applicationDataResponse.getApplicationId()).thenReturn("app123");
        when(applicationDataResponse.getApplicationState()).thenReturn("LEAD_V4_SUBMIT_BUTTON");
        // Note: ApplicationDataResponse doesn't have getAccountId method
        
        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put("leadV4_content_scenario", "PERSONALIZED_WITH_PA_OFFER");
        when(applicationDataResponse.getApplicationData()).thenReturn(applicationData);
    }

    private void setupApplicationDataResponseWithData() {
        setupValidApplicationDataResponse();
        
        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put("leadV4_content_scenario", "PERSONALIZED_WITH_PA_OFFER");
        applicationData.put("firstName", "John");
        applicationData.put("lastName", "Doe");
        applicationData.put("email", "<EMAIL>");
        applicationData.put("phoneNumber", "**********");
        
        when(applicationDataResponse.getApplicationData()).thenReturn(applicationData);
    }

    private void setupValidReviewUserDataSourceResponse() {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("accountId", "account123");
        queryParams.put("applicationId", "app123");
        
        when(reviewUserDataSourceResponse.getQueryParams()).thenReturn(queryParams);
    }

    private void setupReviewUserDataSourceResponseWithProfile() {
        setupValidReviewUserDataSourceResponse();
        when(reviewUserDataSourceResponse.getProfile()).thenReturn(profile);
        when(profile.getFirstName()).thenReturn("John");
        when(profile.getLastName()).thenReturn("Doe");
        when(profile.getEmail()).thenReturn("<EMAIL>");
        when(profile.getPhoneNo()).thenReturn("**********");
    }

    private void setupReviewUserDataSourceResponseWithQueryParams() {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("accountId", "account123");
        queryParams.put("applicationId", "app123");
        queryParams.put("firstName", "John");
        queryParams.put("lastName", "Doe");
        queryParams.put("email", "<EMAIL>");
        queryParams.put("phoneNumber", "**********");
        
        when(reviewUserDataSourceResponse.getQueryParams()).thenReturn(queryParams);
    }

    private void setupValidLeadPageDataSourceResponse() {
        when(leadPageDataSourceResponse.getProfile()).thenReturn(profile);
        when(profile.getFirstName()).thenReturn("John");
        when(profile.getLastName()).thenReturn("Doe");
    }

    private void setupLeadPageDataSourceResponseWithProfile() {
        when(leadPageDataSourceResponse.getProfile()).thenReturn(profile);
        when(profile.getFirstName()).thenReturn("John");
        when(profile.getLastName()).thenReturn("Doe");
        when(profile.getEmail()).thenReturn("<EMAIL>");
        when(profile.getPhoneNo()).thenReturn("**********");
    }
}
