package com.flipkart.fintech.pinaka.service.widgettransformer.lead.V4;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.common.bqIngestor.BqIngestionHelper;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.service.core.v6.LocationRequestHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.pagedatasource.InitialUserReviewDataSource;
import com.flipkart.fintech.pinaka.service.pagedatasource.LeadPageDataSource;
import com.flipkart.fintech.pinaka.service.response.LeadPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.V3.LV3Util;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4LandingPageTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils.FormWidgetDataFetcher;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardSummaryListWidgetData;
import com.supermoney.schema.PinakaService.LeadV3Events;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.whenNew;

@RunWith(PowerMockRunner.class)
@PrepareForTest({
        LV4LandingPageTransformer.class,
        ObjectMapperUtil.class,
        LV3Util.class,
        InitialUserReviewDataSource.class,
        LeadPageDataSource.class
})
public class LV4LandingPageTransformerTest {

    @Mock
    private ApplicationDataResponse applicationDataResponse;
    @Mock
    private LeadPageDataSourceResponse leadPageDataSourceResponse;
    @Mock
    private ReviewUserDataSourceResponse reviewUserDataSourceResponse;
    @Mock
    private ProfileDetailedResponse profile;
    @Mock
    private Decrypter decrypter;
    @Mock
    private DynamicBucket dynamicBucket;
    @Mock
    private FormWidgetDataFetcher formWidgetDataFetcher;
    @Mock
    private LocationRequestHandler locationRequestHandler;
    @Mock
    private BqIngestionHelper bqIngestionHelper;
    @Mock
    private ObjectMapper objectMapper;
    @Mock
    private CardSummaryListWidgetData mockWidgetData;
    @Mock
    private InitialUserReviewDataSource initialUserReviewDataSource;
    @Mock
    private LeadPageDataSource leadPageDataSource;

    private LV4LandingPageTransformer transformer;

    @Before
    public void setup() throws Exception {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(LV3Util.class);
        PowerMockito.mockStatic(ObjectMapperUtil.class);

        // Mock LV3Util.getLeadEvents to return a valid LeadV3Events
        PowerMockito.when(LV3Util.class, "getLeadEvents",
                any(ApplicationDataResponse.class), anyString(), anyString(), anyString())
                .thenReturn(LeadV3Events.newBuilder().build());

        // Mock ObjectMapperUtil
        when(ObjectMapperUtil.get()).thenReturn(objectMapper);

        // Mock data sources
        whenNew(InitialUserReviewDataSource.class).withNoArguments().thenReturn(initialUserReviewDataSource);
        whenNew(LeadPageDataSource.class).withNoArguments().thenReturn(leadPageDataSource);
        when(initialUserReviewDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(reviewUserDataSourceResponse);
        when(leadPageDataSource.getData(any(ApplicationDataResponse.class))).thenReturn(leadPageDataSourceResponse);

        // Mock form widget data fetcher
        when(formWidgetDataFetcher.getDataForFields(any(Set.class), any(LeadPageDataSourceResponse.class),
                any(ReviewUserDataSourceResponse.class), eq(decrypter), eq(locationRequestHandler)))
                .thenReturn(new HashMap<>());

        transformer = new LV4LandingPageTransformer(decrypter, dynamicBucket, formWidgetDataFetcher,
                locationRequestHandler, bqIngestionHelper);
    }

    @Test
    public void testBuildWidgetGroupData_WithValidData() throws Exception {
        // Setup test data
        setupValidApplicationDataResponse();
        setupValidLeadPageDataSourceResponse();
        
        // Mock JSON parsing
        when(objectMapper.readValue(anyString(), eq(CardSummaryListWidgetData.class)))
                .thenReturn(mockWidgetData);
        when(mockWidgetData.getRenderableComponents()).thenReturn(null);
        
        // Execute
        CardSummaryListWidgetData result = transformer.buildWidgetGroupData(applicationDataResponse);
        
        // Verify
        assertNotNull(result);
        verify(bqIngestionHelper).insertLeadEvents(any(LeadV3Events.class));
    }

    @Test
    public void testBuildWidgetGroupData_WithUserNameFromProfile() throws Exception {
        // Setup test data with user name
        setupValidApplicationDataResponse();
        setupLeadPageDataSourceResponseWithUserName("John");
        
        // Mock JSON parsing
        when(objectMapper.readValue(anyString(), eq(CardSummaryListWidgetData.class)))
                .thenReturn(mockWidgetData);
        when(mockWidgetData.getRenderableComponents()).thenReturn(null);
        
        // Execute
        CardSummaryListWidgetData result = transformer.buildWidgetGroupData(applicationDataResponse);
        
        // Verify
        assertNotNull(result);
        verify(bqIngestionHelper).insertLeadEvents(any(LeadV3Events.class));
    }

    @Test
    public void testBuildWidgetGroupData_WithBlankUserName() throws Exception {
        // Setup test data with blank user name
        setupValidApplicationDataResponse();
        setupLeadPageDataSourceResponseWithUserName("");
        
        // Mock JSON parsing
        when(objectMapper.readValue(anyString(), eq(CardSummaryListWidgetData.class)))
                .thenReturn(mockWidgetData);
        when(mockWidgetData.getRenderableComponents()).thenReturn(null);
        
        // Execute
        CardSummaryListWidgetData result = transformer.buildWidgetGroupData(applicationDataResponse);
        
        // Verify
        assertNotNull(result);
        verify(bqIngestionHelper).insertLeadEvents(any(LeadV3Events.class));
    }

    @Test
    public void testBuildWidgetGroupData_WithNullProfile() throws Exception {
        // Setup test data with null profile
        setupValidApplicationDataResponse();
        setupLeadPageDataSourceResponseWithNullProfile();
        
        // Mock JSON parsing
        when(objectMapper.readValue(anyString(), eq(CardSummaryListWidgetData.class)))
                .thenReturn(mockWidgetData);
        when(mockWidgetData.getRenderableComponents()).thenReturn(null);
        
        // Execute
        CardSummaryListWidgetData result = transformer.buildWidgetGroupData(applicationDataResponse);
        
        // Verify
        assertNotNull(result);
        verify(bqIngestionHelper).insertLeadEvents(any(LeadV3Events.class));
    }

    @Test
    public void testBuildWidgetGroupData_WithOfferAmount() throws Exception {
        // Setup test data with PA offer
        setupApplicationDataResponseWithOffer();
        setupValidLeadPageDataSourceResponse();
        
        // Mock JSON parsing
        when(objectMapper.readValue(anyString(), eq(CardSummaryListWidgetData.class)))
                .thenReturn(mockWidgetData);
        when(mockWidgetData.getRenderableComponents()).thenReturn(null);
        
        // Execute
        CardSummaryListWidgetData result = transformer.buildWidgetGroupData(applicationDataResponse);
        
        // Verify
        assertNotNull(result);
        verify(bqIngestionHelper).insertLeadEvents(any(LeadV3Events.class));
    }

    @Test(expected = PinakaException.class)
    public void testBuildWidgetGroupData_JsonProcessingException() throws Exception {
        // Setup test data
        setupValidApplicationDataResponse();
        setupValidLeadPageDataSourceResponse();
        
        // Mock JSON parsing to throw exception
        when(objectMapper.readValue(anyString(), eq(CardSummaryListWidgetData.class)))
                .thenThrow(new JsonProcessingException("JSON parsing failed") {});
        
        // Execute - should throw PinakaException
        transformer.buildWidgetGroupData(applicationDataResponse);
    }

    @Test(expected = PinakaException.class)
    public void testBuildWidgetGroupData_RuntimeException() throws Exception {
        // Setup test data
        setupValidApplicationDataResponse();
        setupValidLeadPageDataSourceResponse();
        
        // Mock to throw runtime exception
        when(objectMapper.readValue(anyString(), eq(CardSummaryListWidgetData.class)))
                .thenThrow(new RuntimeException("Unexpected error"));
        
        // Execute - should throw PinakaException
        transformer.buildWidgetGroupData(applicationDataResponse);
    }

    @Test
    public void testBuildWidgetGroupData_ExceptionHandling() throws Exception {
        // Setup test data
        setupValidApplicationDataResponse();
        setupValidLeadPageDataSourceResponse();
        
        // Mock JSON parsing to throw exception
        when(objectMapper.readValue(anyString(), eq(CardSummaryListWidgetData.class)))
                .thenThrow(new RuntimeException("Test exception"));
        
        try {
            transformer.buildWidgetGroupData(applicationDataResponse);
            fail("Expected PinakaException to be thrown");
        } catch (PinakaException e) {
            // Verify error event is logged
            verify(bqIngestionHelper).insertLeadEvents(any(LeadV3Events.class));
            assertTrue(e.getMessage().contains("Error while building widget Group Data for LV4 Landing Page"));
        }
    }

    // Helper methods for setting up test data
    private void setupValidApplicationDataResponse() {
        when(applicationDataResponse.getSmUserId()).thenReturn("testUser123");
        when(applicationDataResponse.getApplicationId()).thenReturn("app123");
        when(applicationDataResponse.getApplicationState()).thenReturn("LEAD_V4_LANDING_PAGE");
        
        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put("leadV4_content_scenario", "PERSONALIZED_WITH_PA_OFFER");
        when(applicationDataResponse.getApplicationData()).thenReturn(applicationData);
    }

    private void setupApplicationDataResponseWithOffer() {
        setupValidApplicationDataResponse();
        
        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put("leadV4_content_scenario", "PERSONALIZED_WITH_PA_OFFER");
        
        // Add PA offer data
        Map<String, Object> paOffer = new HashMap<>();
        paOffer.put("amount", 500000L); // 5 lakh
        applicationData.put("paOffer", paOffer);
        
        when(applicationDataResponse.getApplicationData()).thenReturn(applicationData);
    }

    private void setupValidLeadPageDataSourceResponse() {
        when(leadPageDataSourceResponse.getProfile()).thenReturn(profile);
        when(profile.getFirstName()).thenReturn(null);
    }

    private void setupLeadPageDataSourceResponseWithUserName(String firstName) {
        when(leadPageDataSourceResponse.getProfile()).thenReturn(profile);
        when(profile.getFirstName()).thenReturn(firstName);
    }

    private void setupLeadPageDataSourceResponseWithNullProfile() {
        when(leadPageDataSourceResponse.getProfile()).thenReturn(null);
    }
}
