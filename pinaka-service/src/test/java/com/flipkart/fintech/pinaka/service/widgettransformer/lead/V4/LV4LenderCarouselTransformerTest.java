package com.flipkart.fintech.pinaka.service.widgettransformer.lead.V4;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4.LV4LenderCarouselTransformer;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardCarouselWidgetDataV0;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardSummaryListWidgetData;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({
        LV4LenderCarouselTransformer.class,
        ObjectMapperUtil.class
})
public class LV4LenderCarouselTransformerTest {

    @Mock
    private ApplicationDataResponse applicationDataResponse;
    @Mock
    private ObjectMapper objectMapper;
    @Mock
    private CardSummaryListWidgetData mockListWidgetData;
    @Mock
    private CardCarouselWidgetDataV0 mockCarouselWidgetData;

    private LV4LenderCarouselTransformer transformer;

    @Before
    public void setup() throws Exception {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(ObjectMapperUtil.class);
        
        // Mock ObjectMapperUtil
        when(ObjectMapperUtil.get()).thenReturn(objectMapper);
        
        transformer = new LV4LenderCarouselTransformer();
    }

    @Test
    public void testBuildWidgetGroupData_WithValidData() throws Exception {
        // Setup test data
        setupValidApplicationDataResponse();
        
        // Mock JSON parsing for landing page carousel
        when(objectMapper.readValue(anyString(), eq(CardSummaryListWidgetData.class)))
                .thenReturn(mockListWidgetData);
        
        // Execute
        CardSummaryListWidgetData result = transformer.buildWidgetGroupData(applicationDataResponse);
        
        // Verify
        assertNotNull(result);
        assertEquals(mockListWidgetData, result);
        verify(objectMapper).readValue(anyString(), eq(CardSummaryListWidgetData.class));
    }

    @Test(expected = PinakaException.class)
    public void testBuildWidgetGroupData_JsonProcessingException() throws Exception {
        // Setup test data
        setupValidApplicationDataResponse();
        
        // Mock JSON parsing to throw exception
        when(objectMapper.readValue(anyString(), eq(CardSummaryListWidgetData.class)))
                .thenThrow(new JsonProcessingException("JSON parsing failed") {});
        
        // Execute - should throw PinakaException
        transformer.buildWidgetGroupData(applicationDataResponse);
    }

    @Test(expected = PinakaException.class)
    public void testBuildWidgetGroupData_RuntimeException() throws Exception {
        // Setup test data
        setupValidApplicationDataResponse();
        
        // Mock to throw runtime exception
        when(objectMapper.readValue(anyString(), eq(CardSummaryListWidgetData.class)))
                .thenThrow(new RuntimeException("Unexpected error"));
        
        // Execute - should throw PinakaException
        transformer.buildWidgetGroupData(applicationDataResponse);
    }

    @Test
    public void testBuildWidgetGroupData_ExceptionHandling() throws Exception {
        // Setup test data
        setupValidApplicationDataResponse();
        
        // Mock JSON parsing to throw exception
        when(objectMapper.readValue(anyString(), eq(CardSummaryListWidgetData.class)))
                .thenThrow(new RuntimeException("Test exception"));
        
        try {
            transformer.buildWidgetGroupData(applicationDataResponse);
            fail("Expected PinakaException to be thrown");
        } catch (PinakaException e) {
            assertTrue(e.getMessage().contains("Error while building widget Group Data for LV4 Landing Page"));
            assertTrue(e.getMessage().contains("testUser123"));
        }
    }

    @Test
    public void testBuildWidgetData_WithValidData() throws Exception {
        // Setup test data
        setupValidApplicationDataResponse();
        
        // Mock JSON parsing for lender carousel
        when(objectMapper.readValue(anyString(), eq(CardCarouselWidgetDataV0.class)))
                .thenReturn(mockCarouselWidgetData);
        
        // Execute
        CardCarouselWidgetDataV0 result = transformer.buildWidgetData(applicationDataResponse);
        
        // Verify
        assertNotNull(result);
        assertEquals(mockCarouselWidgetData, result);
        verify(objectMapper).readValue(anyString(), eq(CardCarouselWidgetDataV0.class));
    }

    @Test(expected = PinakaException.class)
    public void testBuildWidgetData_JsonProcessingException() throws Exception {
        // Setup test data
        setupValidApplicationDataResponse();
        
        // Mock JSON parsing to throw exception
        when(objectMapper.readValue(anyString(), eq(CardCarouselWidgetDataV0.class)))
                .thenThrow(new JsonProcessingException("JSON parsing failed") {});
        
        // Execute - should throw PinakaException
        transformer.buildWidgetData(applicationDataResponse);
    }

    @Test(expected = PinakaException.class)
    public void testBuildWidgetData_RuntimeException() throws Exception {
        // Setup test data
        setupValidApplicationDataResponse();
        
        // Mock to throw runtime exception
        when(objectMapper.readValue(anyString(), eq(CardCarouselWidgetDataV0.class)))
                .thenThrow(new RuntimeException("Unexpected error"));
        
        // Execute - should throw PinakaException
        transformer.buildWidgetData(applicationDataResponse);
    }

    @Test
    public void testBuildWidgetData_ExceptionHandling() throws Exception {
        // Setup test data
        setupValidApplicationDataResponse();
        
        // Mock JSON parsing to throw exception
        when(objectMapper.readValue(anyString(), eq(CardCarouselWidgetDataV0.class)))
                .thenThrow(new RuntimeException("Test exception"));
        
        try {
            transformer.buildWidgetData(applicationDataResponse);
            fail("Expected PinakaException to be thrown");
        } catch (PinakaException e) {
            assertTrue(e.getMessage().contains("Error while building widget data for LV4 Lender Carousel"));
            assertTrue(e.getMessage().contains("testUser123"));
        }
    }

    @Test
    public void testBuildWidgetGroupData_WithNullUserId() throws Exception {
        // Setup test data with null user ID
        when(applicationDataResponse.getSmUserId()).thenReturn(null);
        when(applicationDataResponse.getApplicationId()).thenReturn("app123");
        when(applicationDataResponse.getApplicationState()).thenReturn("LEAD_V4_LENDER_CAROUSEL");
        
        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put("leadV4_content_scenario", "PERSONALIZED_WITH_PA_OFFER");
        when(applicationDataResponse.getApplicationData()).thenReturn(applicationData);
        
        // Mock JSON parsing
        when(objectMapper.readValue(anyString(), eq(CardSummaryListWidgetData.class)))
                .thenReturn(mockListWidgetData);
        
        // Execute
        CardSummaryListWidgetData result = transformer.buildWidgetGroupData(applicationDataResponse);
        
        // Verify
        assertNotNull(result);
        assertEquals(mockListWidgetData, result);
    }

    @Test
    public void testBuildWidgetData_WithNullUserId() throws Exception {
        // Setup test data with null user ID
        when(applicationDataResponse.getSmUserId()).thenReturn(null);
        when(applicationDataResponse.getApplicationId()).thenReturn("app123");
        when(applicationDataResponse.getApplicationState()).thenReturn("LEAD_V4_LENDER_CAROUSEL");
        
        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put("leadV4_content_scenario", "PERSONALIZED_WITH_PA_OFFER");
        when(applicationDataResponse.getApplicationData()).thenReturn(applicationData);
        
        // Mock JSON parsing
        when(objectMapper.readValue(anyString(), eq(CardCarouselWidgetDataV0.class)))
                .thenReturn(mockCarouselWidgetData);
        
        // Execute
        CardCarouselWidgetDataV0 result = transformer.buildWidgetData(applicationDataResponse);
        
        // Verify
        assertNotNull(result);
        assertEquals(mockCarouselWidgetData, result);
    }

    // Helper methods for setting up test data
    private void setupValidApplicationDataResponse() {
        when(applicationDataResponse.getSmUserId()).thenReturn("testUser123");
        when(applicationDataResponse.getApplicationId()).thenReturn("app123");
        when(applicationDataResponse.getApplicationState()).thenReturn("LEAD_V4_LENDER_CAROUSEL");
        
        Map<String, Object> applicationData = new HashMap<>();
        applicationData.put("leadV4_content_scenario", "PERSONALIZED_WITH_PA_OFFER");
        when(applicationDataResponse.getApplicationData()).thenReturn(applicationData);
    }
}
