package com.flipkart.fintech.lead.model;

import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class LeadV4DataGatheringResponseTest {

    @Test
    public void testGetContentScenario_WithUserNameAndPaOffer_ShouldReturnPersonalizedWithPaOffer() {
        // Arrange
        LenderOfferEntity paOffer = createTestPaOffer("OFFER_001", 750000L);
        LeadV4DataGatheringResponse response = LeadV4DataGatheringResponse.builder()
            .userName("John Doe")
            .paOffer(paOffer)
            .build();

        // Act
        LeadV4DataGatheringResponse.ContentScenario scenario = response.getContentScenario();

        // Assert
        assertEquals(LeadV4DataGatheringResponse.ContentScenario.PERSONALIZED_WITH_PA_OFFER, scenario);
    }

    @Test
    public void testGetContentScenario_WithUserNameOnly_ShouldReturnPersonalizedGenericOffer() {
        // Arrange
        LeadV4DataGatheringResponse response = LeadV4DataGatheringResponse.builder()
            .userName("Jane Smith")
            .paOffer(null)
            .build();

        // Act
        LeadV4DataGatheringResponse.ContentScenario scenario = response.getContentScenario();

        // Assert
        assertEquals(LeadV4DataGatheringResponse.ContentScenario.PERSONALIZED_GENERIC_OFFER, scenario);
    }

    @Test
    public void testGetContentScenario_WithPaOfferOnly_ShouldReturnGenericUserWithPaOffer() {
        // Arrange
        LenderOfferEntity paOffer = createTestPaOffer("OFFER_002", 600000L);
        LeadV4DataGatheringResponse response = LeadV4DataGatheringResponse.builder()
            .userName(null)
            .paOffer(paOffer)
            .build();

        // Act
        LeadV4DataGatheringResponse.ContentScenario scenario = response.getContentScenario();

        // Assert
        assertEquals(LeadV4DataGatheringResponse.ContentScenario.GENERIC_USER_WITH_PA_OFFER, scenario);
    }

    @Test
    public void testGetContentScenario_WithNoUserNameAndNoPaOffer_ShouldReturnGenericAll() {
        // Arrange
        LeadV4DataGatheringResponse response = LeadV4DataGatheringResponse.builder()
            .userName(null)
            .paOffer(null)
            .build();

        // Act
        LeadV4DataGatheringResponse.ContentScenario scenario = response.getContentScenario();

        // Assert
        assertEquals(LeadV4DataGatheringResponse.ContentScenario.GENERIC_ALL, scenario);
    }

    @Test
    public void testGetContentScenario_WithEmptyUserName_ShouldReturnGenericAll() {
        // Arrange
        LeadV4DataGatheringResponse response = LeadV4DataGatheringResponse.builder()
            .userName("")
            .paOffer(null)
            .build();

        // Act
        LeadV4DataGatheringResponse.ContentScenario scenario = response.getContentScenario();

        // Assert
        assertEquals(LeadV4DataGatheringResponse.ContentScenario.GENERIC_ALL, scenario);
    }

    @Test
    public void testGetContentScenario_WithBlankUserName_ShouldReturnGenericAll() {
        // Arrange
        LeadV4DataGatheringResponse response = LeadV4DataGatheringResponse.builder()
            .userName("   ")
            .paOffer(null)
            .build();

        // Act
        LeadV4DataGatheringResponse.ContentScenario scenario = response.getContentScenario();

        // Assert
        assertEquals(LeadV4DataGatheringResponse.ContentScenario.GENERIC_ALL, scenario);
    }

    @Test
    public void testGetContentScenario_WithBlankUserNameAndPaOffer_ShouldReturnGenericUserWithPaOffer() {
        // Arrange
        LenderOfferEntity paOffer = createTestPaOffer("OFFER_003", 450000L);
        LeadV4DataGatheringResponse response = LeadV4DataGatheringResponse.builder()
            .userName("   ")
            .paOffer(paOffer)
            .build();

        // Act
        LeadV4DataGatheringResponse.ContentScenario scenario = response.getContentScenario();

        // Assert
        assertEquals(LeadV4DataGatheringResponse.ContentScenario.GENERIC_USER_WITH_PA_OFFER, scenario);
    }

    @Test
    public void testGetContentScenario_WithSingleCharacterUserName_ShouldReturnPersonalizedGenericOffer() {
        // Arrange
        LeadV4DataGatheringResponse response = LeadV4DataGatheringResponse.builder()
            .userName("A")
            .paOffer(null)
            .build();

        // Act
        LeadV4DataGatheringResponse.ContentScenario scenario = response.getContentScenario();

        // Assert
        assertEquals(LeadV4DataGatheringResponse.ContentScenario.PERSONALIZED_GENERIC_OFFER, scenario);
    }

    @Test
    public void testGetContentScenario_WithSingleCharacterUserNameAndPaOffer_ShouldReturnPersonalizedWithPaOffer() {
        // Arrange
        LenderOfferEntity paOffer = createTestPaOffer("OFFER_004", 800000L);
        LeadV4DataGatheringResponse response = LeadV4DataGatheringResponse.builder()
            .userName("B")
            .paOffer(paOffer)
            .build();

        // Act
        LeadV4DataGatheringResponse.ContentScenario scenario = response.getContentScenario();

        // Assert
        assertEquals(LeadV4DataGatheringResponse.ContentScenario.PERSONALIZED_WITH_PA_OFFER, scenario);
    }

    @Test
    public void testBuilder_ShouldCreateValidResponse() {
        // Arrange
        LenderOfferEntity paOffer = createTestPaOffer("OFFER_005", 1000000L);
        String userName = "Test User";

        // Act
        LeadV4DataGatheringResponse response = LeadV4DataGatheringResponse.builder()
            .userName(userName)
            .paOffer(paOffer)
            .build();

        // Assert
        assertNotNull(response);
        assertEquals(userName, response.getUserName());
        assertEquals(paOffer, response.getPaOffer());
        assertEquals("OFFER_005", response.getPaOffer().getId());
        assertEquals(Long.valueOf(1000000L), response.getPaOffer().getAmount());
    }

    @Test
    public void testBuilder_WithNullValues_ShouldCreateValidResponse() {
        // Act
        LeadV4DataGatheringResponse response = LeadV4DataGatheringResponse.builder()
            .userName(null)
            .paOffer(null)
            .build();

        // Assert
        assertNotNull(response);
        assertNull(response.getUserName());
        assertNull(response.getPaOffer());
    }

    @Test
    public void testContentScenarioEnum_ShouldHaveAllExpectedValues() {
        // Act & Assert
        LeadV4DataGatheringResponse.ContentScenario[] scenarios = LeadV4DataGatheringResponse.ContentScenario.values();
        
        assertEquals(4, scenarios.length);
        assertTrue(containsScenario(scenarios, LeadV4DataGatheringResponse.ContentScenario.PERSONALIZED_WITH_PA_OFFER));
        assertTrue(containsScenario(scenarios, LeadV4DataGatheringResponse.ContentScenario.PERSONALIZED_GENERIC_OFFER));
        assertTrue(containsScenario(scenarios, LeadV4DataGatheringResponse.ContentScenario.GENERIC_USER_WITH_PA_OFFER));
        assertTrue(containsScenario(scenarios, LeadV4DataGatheringResponse.ContentScenario.GENERIC_ALL));
    }

    private boolean containsScenario(LeadV4DataGatheringResponse.ContentScenario[] scenarios,
                                   LeadV4DataGatheringResponse.ContentScenario target) {
        for (LeadV4DataGatheringResponse.ContentScenario scenario : scenarios) {
            if (scenario == target) {
                return true;
            }
        }
        return false;
    }

    private LenderOfferEntity createTestPaOffer(String offerId, Long amount) {
        LenderOfferEntity offer = new LenderOfferEntity();
        offer.setId(offerId);
        offer.setUserId("test-user-123");
        offer.setUserProfileId("profile-456");
        offer.setAmount(amount);
        offer.setRoi(14.5);
        offer.setOfferDetails("{\"tenure\": 24, \"processingFee\": 3.0}");
        offer.setMetadata("{\"source\": \"test\", \"type\": \"pre_approved\"}");
        offer.setCreatedAtMS(System.currentTimeMillis() - 3600000L); // 1 hour ago
        offer.setUpdatedAtMS(System.currentTimeMillis());
        return offer;
    }
}
