package com.flipkart.fintech.lead.service;

import com.flipkart.fintech.lead.model.LeadV4DataGatheringResponse;
import com.flipkart.fintech.lending.orchestrator.client.OfferServiceClient;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferState;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferType;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.profile.client.ProfileClient;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Optional;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class LeadV4DataGatheringServiceTest {

    @Mock
    private ProfileClient profileClient;

    @Mock
    private OfferServiceClient offerServiceClient;

    private LeadV4DataGatheringService leadV4DataGatheringService;

    private MerchantUser merchantUser;
    private String requestId;

    @Before
    public void setUp() {
        leadV4DataGatheringService = new LeadV4DataGatheringService(profileClient, offerServiceClient);
        
        merchantUser = MerchantUser.getMerchantUser("test-merchant", "merchant-user-456", "test-user-123");
        
        requestId = "test-request-id";
    }

    @Test
    public void testGatherData_WithUserNameAndPaOffer_ShouldReturnCompleteResponse() throws Exception {
        // Arrange
        ProfileDetailedResponse profile = createProfileWithName("John", "Doe");
        LenderOfferEntity paOffer = createMockPaOffer();
        
        when(profileClient.getProfile(anyString(), anyString(), anyBoolean())).thenReturn(profile);
        when(offerServiceClient.getPreApprovedOffer(any(MerchantUser.class))).thenReturn(Optional.of(paOffer));

        // Act
        LeadV4DataGatheringResponse response = leadV4DataGatheringService.gatherData(merchantUser, requestId);

        // Assert
        assertNotNull(response);
        assertEquals("John Doe", response.getUserName());
        assertEquals(paOffer, response.getPaOffer());
        assertEquals(LeadV4DataGatheringResponse.ContentScenario.PERSONALIZED_WITH_PA_OFFER, response.getContentScenario());
        
        verify(profileClient).getProfile("merchant-user-456", "test-user-123", true);
        verify(offerServiceClient).getPreApprovedOffer(merchantUser);
    }

    @Test
    public void testGatherData_WithUserNameOnly_ShouldReturnPersonalizedGenericResponse() throws Exception {
        // Arrange
        ProfileDetailedResponse profile = createProfileWithName("Jane", "Smith");
        
        when(profileClient.getProfile(anyString(), anyString(), anyBoolean())).thenReturn(profile);
        when(offerServiceClient.getPreApprovedOffer(any(MerchantUser.class))).thenReturn(Optional.empty());

        // Act
        LeadV4DataGatheringResponse response = leadV4DataGatheringService.gatherData(merchantUser, requestId);

        // Assert
        assertNotNull(response);
        assertEquals("Jane Smith", response.getUserName());
        assertNull(response.getPaOffer());
        assertEquals(LeadV4DataGatheringResponse.ContentScenario.PERSONALIZED_GENERIC_OFFER, response.getContentScenario());
    }

    @Test
    public void testGatherData_WithPaOfferOnly_ShouldReturnGenericUserWithPaOfferResponse() throws Exception {
        // Arrange
        ProfileDetailedResponse profile = createProfileWithName(null, null);
        LenderOfferEntity paOffer = createMockPaOffer();
        
        when(profileClient.getProfile(anyString(), anyString(), anyBoolean())).thenReturn(profile);
        when(offerServiceClient.getPreApprovedOffer(any(MerchantUser.class))).thenReturn(Optional.of(paOffer));

        // Act
        LeadV4DataGatheringResponse response = leadV4DataGatheringService.gatherData(merchantUser, requestId);

        // Assert
        assertNotNull(response);
        assertNull(response.getUserName());
        assertEquals(paOffer, response.getPaOffer());
        assertEquals(LeadV4DataGatheringResponse.ContentScenario.GENERIC_USER_WITH_PA_OFFER, response.getContentScenario());
    }

    @Test
    public void testGatherData_WithNoUserNameAndNoPaOffer_ShouldReturnGenericAllResponse() throws Exception {
        // Arrange
        ProfileDetailedResponse profile = createProfileWithName(null, null);
        
        when(profileClient.getProfile(anyString(), anyString(), anyBoolean())).thenReturn(profile);
        when(offerServiceClient.getPreApprovedOffer(any(MerchantUser.class))).thenReturn(Optional.empty());

        // Act
        LeadV4DataGatheringResponse response = leadV4DataGatheringService.gatherData(merchantUser, requestId);

        // Assert
        assertNotNull(response);
        assertNull(response.getUserName());
        assertNull(response.getPaOffer());
        assertEquals(LeadV4DataGatheringResponse.ContentScenario.GENERIC_ALL, response.getContentScenario());
    }

    @Test
    public void testGatherData_WithFirstNameOnly_ShouldReturnFirstNameAsUserName() throws Exception {
        // Arrange
        ProfileDetailedResponse profile = createProfileWithName("Alice", null);
        
        when(profileClient.getProfile(anyString(), anyString(), anyBoolean())).thenReturn(profile);
        when(offerServiceClient.getPreApprovedOffer(any(MerchantUser.class))).thenReturn(Optional.empty());

        // Act
        LeadV4DataGatheringResponse response = leadV4DataGatheringService.gatherData(merchantUser, requestId);

        // Assert
        assertNotNull(response);
        assertEquals("Alice", response.getUserName());
        assertEquals(LeadV4DataGatheringResponse.ContentScenario.PERSONALIZED_GENERIC_OFFER, response.getContentScenario());
    }

    @Test
    public void testGatherData_WithBlankFirstName_ShouldReturnNullUserName() throws Exception {
        // Arrange
        ProfileDetailedResponse profile = createProfileWithName("", "Doe");
        
        when(profileClient.getProfile(anyString(), anyString(), anyBoolean())).thenReturn(profile);
        when(offerServiceClient.getPreApprovedOffer(any(MerchantUser.class))).thenReturn(Optional.empty());

        // Act
        LeadV4DataGatheringResponse response = leadV4DataGatheringService.gatherData(merchantUser, requestId);

        // Assert
        assertNotNull(response);
        assertNull(response.getUserName());
        assertEquals(LeadV4DataGatheringResponse.ContentScenario.GENERIC_ALL, response.getContentScenario());
    }

    @Test
    public void testGatherData_WithNullProfile_ShouldReturnNullUserName() throws Exception {
        // Arrange
        when(profileClient.getProfile(anyString(), anyString(), anyBoolean())).thenReturn(null);
        when(offerServiceClient.getPreApprovedOffer(any(MerchantUser.class))).thenReturn(Optional.empty());

        // Act
        LeadV4DataGatheringResponse response = leadV4DataGatheringService.gatherData(merchantUser, requestId);

        // Assert
        assertNotNull(response);
        assertNull(response.getUserName());
        assertEquals(LeadV4DataGatheringResponse.ContentScenario.GENERIC_ALL, response.getContentScenario());
    }

    @Test
    public void testGatherData_WithProfileClientException_ShouldContinueWithoutUserName() throws Exception {
        // Arrange
        LenderOfferEntity paOffer = createMockPaOffer();
        
        when(profileClient.getProfile(anyString(), anyString(), anyBoolean()))
            .thenThrow(new RuntimeException("Profile service error"));
        when(offerServiceClient.getPreApprovedOffer(any(MerchantUser.class))).thenReturn(Optional.of(paOffer));

        // Act
        LeadV4DataGatheringResponse response = leadV4DataGatheringService.gatherData(merchantUser, requestId);

        // Assert
        assertNotNull(response);
        assertNull(response.getUserName());
        assertEquals(paOffer, response.getPaOffer());
        assertEquals(LeadV4DataGatheringResponse.ContentScenario.GENERIC_USER_WITH_PA_OFFER, response.getContentScenario());
    }

    @Test
    public void testGatherData_WithOfferServiceException_ShouldContinueWithoutPaOffer() throws Exception {
        // Arrange
        ProfileDetailedResponse profile = createProfileWithName("John", "Doe");
        
        when(profileClient.getProfile(anyString(), anyString(), anyBoolean())).thenReturn(profile);
        when(offerServiceClient.getPreApprovedOffer(any(MerchantUser.class)))
            .thenThrow(new RuntimeException("Offer service error"));

        // Act
        LeadV4DataGatheringResponse response = leadV4DataGatheringService.gatherData(merchantUser, requestId);

        // Assert
        assertNotNull(response);
        assertEquals("John Doe", response.getUserName());
        assertNull(response.getPaOffer());
        assertEquals(LeadV4DataGatheringResponse.ContentScenario.PERSONALIZED_GENERIC_OFFER, response.getContentScenario());
    }

    @Test
    public void testGatherData_WithBothServicesException_ShouldReturnGenericAllResponse() throws Exception {
        // Arrange
        when(profileClient.getProfile(anyString(), anyString(), anyBoolean()))
            .thenThrow(new RuntimeException("Profile service error"));
        when(offerServiceClient.getPreApprovedOffer(any(MerchantUser.class)))
            .thenThrow(new RuntimeException("Offer service error"));

        // Act
        LeadV4DataGatheringResponse response = leadV4DataGatheringService.gatherData(merchantUser, requestId);

        // Assert
        assertNotNull(response);
        assertNull(response.getUserName());
        assertNull(response.getPaOffer());
        assertEquals(LeadV4DataGatheringResponse.ContentScenario.GENERIC_ALL, response.getContentScenario());
    }

    private ProfileDetailedResponse createProfileWithName(String firstName, String lastName) {
        ProfileDetailedResponse profile = new ProfileDetailedResponse();
        profile.setFirstName(firstName);
        profile.setLastName(lastName);
        profile.setPhoneNo("9876543210");
        profile.setEmail("<EMAIL>");
        profile.setGender("MALE");
        profile.setDateOfBirth("1990-01-15");
        profile.setPincode("560001");
        profile.setCity("Bangalore");
        profile.setState("Karnataka");
        profile.setEmploymentType("SALARIED");
        profile.setMonthlyIncome(75000.0);
        profile.setPan("**********");
        profile.setAadhaar("123456789012");
        return profile;
    }

    private LenderOfferEntity createMockPaOffer() {
        LenderOfferEntity offer = new LenderOfferEntity();
        offer.setId("PA_OFFER_123");
        offer.setUserId("test-user-123");
        offer.setUserProfileId("profile-456");
        offer.setLender(com.flipkart.fintech.pinaka.api.enums.Lender.MONEYVIEW);
        offer.setStatus(LenderOfferState.ACTIVE);
        offer.setOfferType(LenderOfferType.PRE_APPROVED);
        offer.setAmount(500000L);
        offer.setRoi(12.5);
        offer.setOfferDetails("{\"tenure\": 12, \"processingFee\": 2.5}");
        offer.setMetadata("{\"source\": \"test\", \"campaign\": \"v4_landing\"}");
        offer.setCreatedAtMS(System.currentTimeMillis() - 86400000L); // 1 day ago
        offer.setUpdatedAtMS(System.currentTimeMillis());
        return offer;
    }
}
