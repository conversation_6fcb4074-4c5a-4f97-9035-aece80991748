{"CACHE_INVALIDATION_TTL": "0", "META_INFO": {"abContext": {"abWrapper": []}, "appConfigHash": null, "clientConfigHashMap": null, "dcInfo": null, "omnitureInfo": null}, "REQUEST": null, "REQUEST-ID": "f8e04034-2cb8-4822-a07e-2ebb4105409d", "RESPONSE": {"actionSuccess": false, "marketPlaceTrackingDataMap": {}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": 2147483647, "hasMorePages": false, "infinitePage": false, "layoutParams": {"background": "linear-gradient(135deg, #f8e6fa 0%, #f9fafb 60%, #e6f0fa 100%)", "orientation": "", "theme": "light"}, "pageHash": "-*********", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "pageTitle": "Offer Details", "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": "ACC1AA9317789384AB8A54DCC2F4029031A9", "pageType": "DYNAMIC", "pageId": "offer-dynam-dd62d", "applicationId": "APP2502241712254137621721185267668452213", "loginStatus": "login:<PERSON><PERSON><PERSON>"}}}, "pageMeta": {"baseImpressionId": "f8e04034-2cb8-4822-a07e-2ebb4105409d", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "-*********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "CARD_SUMMARY_LIST", "data": {"subWidget": null, "clientPowered": false, "renderableComponents": [{"tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/0b69cf71-634c-4584-a916-097bd0f981b0.png?q={@quality}", "height": 132, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/0b69cf71-634c-4584-a916-097bd0f981b0.png?q={@quality}", "width": 105}, "backgroundImage": null, "avatar": null, "renderableAvatar": null, "superTitle": null, "title": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "Wohoo!", "specialTextsMapper": null, "showClipboard": false, "textColor": "#98A2B3", "style": {"color": "#1D2939", "fontSize": 28, "lineHeight": 32, "fontFamily": "ANEK_LATIN", "fontWeight": "bold"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "body": null, "description": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "An instant cash of upto", "showClipboard": false, "textColor": "#98A2B3", "style": {"color": "#1D2939", "fontSize": 28, "lineHeight": 32, "fontFamily": "ANEK_LATIN", "fontWeight": "bold"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "textContainerStyle": {"display": "flex", "flexDirection": "column", "alignItems": "flex-start"}, "tagValue": null, "overlayTagValue": null, "style": {"borderRadius": 6, "padding": 8, "paddingBottom": 0, "alignItems": "flex-start"}, "cardPressEnabled": null, "viewType": "PRIMITIVE_CARD", "iconBackgroundColor": null, "iconSize": null, "orientation": "PORTRAIT", "requestContext": null, "cardType": null, "next": null, "statusText": null, "subStatusText": null, "actionButtons": null}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "icon": null, "backgroundImage": null, "avatar": null, "renderableAvatar": null, "superTitle": null, "title": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "₹1,00,000", "showClipboard": false, "textColor": "#4D43FE", "style": {"fontSize": 56, "lineHeight": 60, "fontFamily": "ANEK_LATIN", "fontWeight": "bold"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "body": null, "description": {"value": {"type": "RichTextValue", "text": "is reserved for you!", "showClipboard": false, "textColor": "#98A2B3", "style": {"paddingTop": 12, "color": "#1D2939", "fontSize": 28, "lineHeight": 32, "fontFamily": "ANEK_LATIN", "fontWeight": "bold"}}}, "textContainerStyle": {}, "tagValue": null, "overlayTagValue": null, "style": {"borderRadius": 6, "padding": 8, "paddingTop": 0, "alignItems": "baseline"}, "cardPressEnabled": null, "viewType": "PRIMITIVE_CARD", "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "requestContext": null, "cardType": null, "next": null, "statusText": null, "subStatusText": null, "actionButtons": null}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}], "orientation": "PORTRAIT", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": null}}}, {"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"margin": "0,0,0,0", "orientation": "", "padding": "24,0,24,0", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "-*********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "CARD_SUMMARY_LIST", "data": {"orientation": "VERTICAL_CARD_ANIMATION", "renderableComponents": [{"action": null, "value": {"type": "PrimitiveCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/ad0137e2-703b-4b1f-9f45-9d294e9c4c0e.png?q={@quality}", "height": 40, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/ad0137e2-703b-4b1f-9f45-9d294e9c4c0e.png?q={@quality}", "width": 40}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"alignItems": "center", "display": "flex", "flexDirection": "row", "gap": "4px", "borderRadius": "12px", "backgroundImage": "linear-gradient(to right, #E5E8FD, #F9FAFB)", "padding": "8px"}, "subStatusText": null, "tagValue": null, "title": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "ANEK_LATIN", "fontSize": 16, "fontWeight": "normal", "lineHeight": 20, "marginTop": 8, "whiteSpace": "nowrap"}, "text": "Quick cash in 2 minutes", "textColor": "#344054"}}, "description": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "ANEK_LATIN", "fontSize": 12, "fontWeight": "normal", "lineHeight": 16, "marginTop": 8, "whiteSpace": "nowrap"}, "text": "Fast and instant disbursal", "textColor": "#667085"}}, "viewType": "PRIMITIVE_CARD"}}, {"action": null, "value": {"type": "PrimitiveCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/e934feb1-9201-4301-b02b-7479ed984074.png?q={@quality}", "height": 40, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/e934feb1-9201-4301-b02b-7479ed984074.png?q={@quality}", "width": 40}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"alignItems": "center", "display": "flex", "flexDirection": "row", "gap": "4px", "borderRadius": "12px", "background": "linear-gradient(to right, #F3DFFF, #F9FAFB)", "padding": "8px"}, "subStatusText": null, "tagValue": null, "title": {"value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "ANEK_LATIN", "fontSize": 16, "fontWeight": "normal", "lineHeight": 20, "marginTop": 8, "whiteSpace": "nowrap"}, "text": "3-10 months of easy EMI's", "textColor": "#344054"}}, "description": {"value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "ANEK_LATIN", "fontSize": 12, "fontWeight": "normal", "lineHeight": 16, "marginTop": 8, "whiteSpace": "nowrap"}, "text": "repay as per your comfort", "textColor": "#667085"}}, "viewType": "PRIMITIVE_CARD"}}, {"action": null, "value": {"type": "PrimitiveCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/438662f9-024a-43f1-9072-361c08cf756b.png?q={@quality}", "height": 40, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/438662f9-024a-43f1-9072-361c08cf756b.png?q={@quality}", "width": 40}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"alignItems": "center", "display": "flex", "flexDirection": "row", "gap": "4px", "borderRadius": "12px", "backgroundImage": "linear-gradient(to right, #DDFAE8, #F9FAFB)", "padding": "8px"}, "subStatusText": null, "tagValue": null, "title": {"value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "ANEK_LATIN", "fontSize": 16, "fontWeight": "normal", "lineHeight": 20, "marginTop": 8, "whiteSpace": "nowrap"}, "text": "No paperwork", "textColor": "#344054"}}, "description": {"value": {"type": "RichTextValue", "showClipboard": false, "style": {"color": "#1D2939", "fontFamily": "ANEK_LATIN", "fontSize": 12, "fontWeight": "normal", "lineHeight": 16, "marginTop": 8, "whiteSpace": "nowrap"}, "text": "100% hassle-free process", "textColor": "#667085"}}, "viewType": "PRIMITIVE_CARD"}}]}}}, {"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"margin": "0,0,0,0", "orientation": "", "padding": "24,0,24,24", "widgetWidth": 12}, "widget": {"type": "CARD_CAROUSEL", "data": {"renderableComponents": [{"action": null, "value": {"type": "PrimitiveCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "Lender logo", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/08/2024/7229ac68-95d2-4e3b-a1ad-158949b08f65.png?q={@quality}", "height": 28, "width": 31}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"paddingRight": 0}, "subStatusText": null, "tagValue": null, "title": null, "description": null, "viewType": "PRIMITIVE_CARD"}}, {"action": null, "value": {"type": "PrimitiveCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "Lender logo", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/08/2024/e8512fe0-801f-4d0e-af63-32ac537f70ea.png?q={@quality}", "height": 25, "width": 28}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"paddingRight": 0}, "subStatusText": null, "tagValue": null, "title": null, "description": null, "viewType": "PRIMITIVE_CARD"}}, {"action": null, "value": {"type": "PrimitiveCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "Lender logo", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/08/2024/7885680c-7968-4bbd-be59-284974778df7.png?q={@quality}", "height": 28, "width": 34}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"paddingRight": 0}, "subStatusText": null, "tagValue": null, "title": null, "description": null, "viewType": "PRIMITIVE_CARD"}}, {"action": null, "value": {"type": "PrimitiveCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "Lender logo", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/08/2024/bea7652b-a853-479a-b782-62f3f2b2af10.png?q={@quality}", "height": 28, "width": 37}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"paddingRight": 0}, "subStatusText": null, "tagValue": null, "title": null, "description": null, "viewType": "PRIMITIVE_CARD"}}, {"action": null, "value": {"type": "PrimitiveCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "Lender logo", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/08/2024/16f3f972-8a83-4b11-a414-2d069491dc7f.png?q={@quality}", "height": 28, "width": 43}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"paddingRight": 0}, "subStatusText": null, "tagValue": null, "title": null, "description": null, "viewType": "PRIMITIVE_CARD"}}, {"action": null, "value": {"type": "PrimitiveCard", "actionButtons": null, "next": null, "avatar": null, "backgroundImage": null, "body": null, "cardPressEnabled": null, "icon": {"type": "ImageValue", "alternateText": "Lender logo", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/30/08/2024/e14cc817-ee87-42b9-9b35-999dc587dc07.png?q={@quality}", "height": 24, "width": 37}, "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "renderableAvatar": null, "requestContext": null, "statusText": null, "style": {"paddingRight": 0}, "subStatusText": null, "tagValue": null, "title": null, "description": null, "viewType": "PRIMITIVE_CARD"}}], "autoPlay": true, "speed": 2}}}, {"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"margin": "0,0,0,0", "orientation": "", "padding": "24,0,24,24", "widgetHeight": 150, "widgetWidth": 12}, "dataId": "-*********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "SUBMIT_BUTTON_WIDGET", "data": {"subWidget": null, "clientPowered": false, "submitButton": {"type": "SubmitButtonValue", "consent": null, "consentList": null, "button": {"tracking": null, "trackingData": null, "value": {"borderColor": "#B5EF85", "buttonColor": "#B5EF85", "buttonTextColor": "#000", "type": "RichTextButtonValue", "title": "Unlock offer now"}, "action": {"type": "CLIENT__INLINE_NAVIGATION", "omnitureData": null, "originalUrl": null, "url": "6/pl/apply-now", "params": {"screenDetails": {"isBottomSheet": true, "fullWidth": true, "hideOnClickOutside": true}, "pageResponse": {"pageData": {"backTTL": 30000, "elementId": "page", "hardTTL": 2147483647, "hasMorePages": false, "infinitePage": false, "layoutParams": {"backgroundColor": "#F9FAFB", "orientation": "", "theme": "light"}, "pageHash": "-********", "pageLevelSlots": {}, "pageTTL": 30000, "pageTags": {"tags": []}, "pageTitle": "", "pageSubtitle": "", "paginationContextMap": {}, "sharedData": {}, "trackingContext": {"meta": null, "navigationalPageName": "dynamic", "navigationalPageType": "dynamic", "orderLevelTrackingMap": null, "pageId": null, "pageName": null, "pageType": "dynamic", "tracking": {"accountId": "ACCCA5AA2C0584244C99E0D109A807D4073F", "pageType": "DYNAMIC", "pageId": "perso-dynam-26ad7", "applicationId": "APP2502041932335592866489436724399367995", "loginStatus": "login:<PERSON><PERSON><PERSON>"}}}, "pageMeta": {"baseImpressionId": "25b04894-2a9b-4b55-b6aa-6343b6654005", "pageNotChanged": false, "prefetchPage": {"enablePrefetch": false}}, "slots": [{"slotType": "WIDGET", "id": 1, "parentId": 0, "layoutParams": {"backgroundColor": "#F9FAFB", "margin": "0,0,0,0", "orientation": "", "padding": "24,24,24,24", "widgetHeight": 0, "widgetWidth": 12}, "dataId": "-********", "elementId": "1-FORM_V4", "hasWidgetDataChanged": true, "ttl": 3000, "widget": {"type": "FORM_V4", "data": {"clientPowered": false, "colGap": null, "containerStyle": null, "failCard": null, "formId": "REVIEW_PAGE_1", "gap": 0, "numGridColumns": 0, "orientation": "PORTRAIT", "paginationRequestContext": null, "persistFormData": false, "title": {"text": " ", "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "url": "https://rukminim1.flixcart.com/www/288/{@height}/promos/17/06/2025/1419ed91-7577-4a76-af8b-160b93fc96d7.png?q=100", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/288/{@height}/promos/17/06/2025/1419ed91-7577-4a76-af8b-160b93fc96d7.png?q=100", "height": 144, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/17/06/2025/1419ed91-7577-4a76-af8b-160b93fc96d7.png?q=100", "width": 288}}, "subTitle": {"text": "Make sure your name is as per PAN", "style": {"fontFamily": "<PERSON><PERSON>", "color": "#4D43FE", "fontSize": 20, "fontWeight": "bold", "width": 100}}, "renderableComponents": [{"action": null, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "style": {}, "value": {"type": "TextBoxFormFieldValueV0", "autoCapitalize": "characters", "disabled": false, "formFieldType": "TEXT_BOX_V0", "label": "Name", "mandatory": true, "name": "Name", "noOfErrors": 1, "placeholder": "Enter your first name", "validationRuleList": [{"errorMessage": "Complete this field to proceed", "interactionType": null, "regex": "\\S", "ruleType": "REGEX", "validateOnSubmit": false}], "textboxStyles": {"inputStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontStyle": "normal", "fontWeight": 400, "border-radius": 8, "border": "none", "box-shadow": "none"}, "labelStyle": {"color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "fontWeight": 400, "marginTop": 20}, "captionStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal", "fontWeight": 400}}, "value": "<PERSON>"}}, {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "TextBoxFormFieldValue", "disabled": true, "formFieldType": "TEXT_BOX", "label": "Phone no.", "mandatory": true, "name": "phoneNumber", "placeholder": "Enter mobile number", "inputType": "PHONE_PAD", "value": "9413439226", "validationRuleList": [{"ruleType": "REGEX", "errorMessage": "Please enter valid mobile number", "validateOnSubmit": false, "regex": "^[6-9]{1}[0-9]{9}$"}], "textboxStyles": {"inputStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 16, "fontStyle": "normal", "fontWeight": 400, "border-radius": 8, "border": "none", "box-shadow": "none"}, "labelStyle": {"color": "#344054", "fontFamily": "<PERSON><PERSON>", "fontSize": 14, "fontStyle": "normal", "fontWeight": 400}, "captionStyle": {"color": "#1D2939", "fontFamily": "<PERSON><PERSON>", "fontSize": 12, "fontStyle": "normal", "fontWeight": 400}}}}], "resultStoreKey": null, "subWidget": null, "submitButton": {"type": "SubmitButtonValue", "alwaysEnabled": true, "autoSubmit": false, "button": {"action": {"constraints": null, "customTrackingEvents": null, "encryption": null, "fallback": null, "loaderContent": null, "loginType": "LOGIN_NOT_REQUIRED", "nonWidgetizeRedirection": null, "omnitureData": null, "originalUrl": null, "params": {"processInstanceId": "-RsPa5_sMrCj0E6Q", "taskKey": "reviewPage1", "applicationId": "APP2502041932335592866489436724399367995", "taskId": "2sJ11aBS3pipSBM-", "token": "hDGX/Et8g+DxfJiYZPRI42OojuWD+KRqVK9CnG79OmCCyfyZEwSXJ45Pc2h9AZ52JmAI9isC9V/qJcIGHXfrJLqsWQPS7T5sKIyfxxIR2NJqBicz3dPc+RQnPQMZ9DAX"}, "requiredPermissionType": null, "requiredPermissionTypes": null, "screenType": null, "tracking": {}, "triggerExtraStandardEvents": null, "type": "NAVIGATION", "url": "/api/sm/1/application/form?page=review_details_2", "validationMeta": null, "widgetTracking": null}, "lenderLogo": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextButtonValue", "title": "Confirm", "borderColor": "#B5EF85", "buttonColor": "#B5EF85", "borderRadius": 8, "richText": {"type": "RichTextValue", "style": {"color": "#1D2939", "fontSize": 18, "fontWeight": "bold", "fontFamily": "<PERSON><PERSON>"}}, "text": "Continue"}}, "buttonOrientation": null, "consent": {"checkboxField": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "CheckBoxFormFieldValue", "actionText": {"action": null, "metaData": null, "rcType": null, "tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "showClipboard": false, "text": "I hereby agree and apply for L&T and further accept that."}}, "defaultValue": false, "disabled": false, "formFieldType": "CHECKBOX", "label": "Consent", "mandatory": true, "name": "consentPan"}}, "consentFor": "BUREAU_PULL", "consentId": "001", "consentType": "CHECKBOX"}, "consentList": null, "containerStyle": null, "footerValue": null, "headerValue": null, "hidden": false, "lenderLogo": null, "secondaryButtons": null, "sticky": null, "viewType": "NO_SHADOW"}}, "tracking": {}}}]}}, "validationMeta": null, "tracking": {}, "fallback": null, "loginType": "LOGIN_NOT_REQUIRED", "constraints": null, "requiredPermissionType": null, "requiredPermissionTypes": null, "widgetTracking": null, "nonWidgetizeRedirection": null, "customTrackingEvents": null, "triggerExtraStandardEvents": null, "encryption": {"publicKey": null, "keyId": null}, "loaderContent": null}, "rcType": null, "metaData": null, "lenderLogo": null}, "secondaryButtons": null, "buttonOrientation": null, "autoSubmit": false, "alwaysEnabled": null, "hidden": false, "sticky": null, "viewType": null, "headerValue": null, "footerValue": null, "lenderLogo": null, "containerStyle": null}}}}]}}, "SESSION": {"accountId": "ACC1AA9317789384AB8A54DCC2F4029031A9", "asn": null, "at": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjNhNzdlZTgxLTRjNWYtNGU5Ni04ZmRlLWM3YWMyYjVlOTA1NSJ9.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.TIBCwh3NNsnKeMWWHP1LC_1gWu7ltD3-mlVlMHFWRs4", "email": "<EMAIL>", "firstName": "<PERSON><PERSON>", "flipkartFirstUser": false, "isLoggedIn": true, "isUPISessionActive": false, "kaction": null, "lastName": "<PERSON><PERSON><PERSON>", "mobileNumber": null, "nsid": "3.VIDD76D52AD5394327B85381B5FC3D24F7.*************.VIDD76D52AD5394327B85381B5FC3D24F7", "rt": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6IjhlM2ZhMGE3LTJmZDMtNGNiMi05MWRjLTZlNTMxOGU1YTkxZiJ9.****************************************************************************************************************************************************************************************************************************************************************************************.T5Nv1ax-S-GuRtxQxVam-bLUYNCGVGVOGABuc_cQczE", "secureToken": "VIDD76D52AD5394327B85381B5FC3D24F7:VIDD76D52AD5394327B85381B5FC3D24F7", "sn": "VIDD76D52AD5394327B85381B5FC3D24F7.TOK70307EB53DB34C9E9F62E66233D0C265.1740399775.LI", "tracking": null, "ts": 0, "twoFa": false, "upiSessionPolicy": null, "vid": "VIDD76D52AD5394327B85381B5FC3D24F7"}, "STATUS_CODE": 200}