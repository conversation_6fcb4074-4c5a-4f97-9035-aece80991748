{"subWidget": null, "clientPowered": false, "renderableComponents": [{"tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "icon": {"type": "ImageValue", "alternateText": "No pending requests found", "dynamicImageUrl": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/0b69cf71-634c-4584-a916-097bd0f981b0.png?q={@quality}", "height": 132, "source": "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/06/2025/0b69cf71-634c-4584-a916-097bd0f981b0.png?q={@quality}", "width": 105}, "backgroundImage": null, "avatar": null, "renderableAvatar": null, "superTitle": null, "title": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "Congrats <PERSON>,", "specialTextsMapper": {"Prakash": {"color": "#1D2939"}}, "showClipboard": false, "textColor": "#98A2B3", "style": {"color": "#1D2939", "fontSize": 28, "lineHeight": 32, "fontFamily": "ANEK_LATIN", "fontWeight": "bold"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "body": null, "description": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "An instant cash of upto", "showClipboard": false, "textColor": "#98A2B3", "style": {"color": "#1D2939", "fontSize": 28, "lineHeight": 32, "fontFamily": "ANEK_LATIN", "fontWeight": "bold"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "textContainerStyle": {"display": "flex", "flexDirection": "column", "alignItems": "flex-start"}, "tagValue": null, "overlayTagValue": null, "style": {"borderRadius": 6, "padding": 8, "paddingBottom": 0, "alignItems": "flex-start"}, "cardPressEnabled": null, "viewType": "PRIMITIVE_CARD", "iconBackgroundColor": null, "iconSize": null, "orientation": "PORTRAIT", "requestContext": null, "cardType": null, "next": null, "statusText": null, "subStatusText": null, "actionButtons": null}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, {"tracking": null, "trackingData": null, "value": {"type": "PrimitiveCard", "icon": null, "backgroundImage": null, "avatar": null, "renderableAvatar": null, "superTitle": null, "title": {"tracking": null, "trackingData": null, "value": {"type": "RichTextValue", "text": "₹1,00,000", "showClipboard": false, "textColor": "#4D43FE", "style": {"fontSize": 56, "lineHeight": 60, "fontFamily": "ANEK_LATIN", "fontWeight": "bold"}}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}, "body": null, "description": {"value": {"type": "RichTextValue", "text": "is reserved for you!", "showClipboard": false, "textColor": "#98A2B3", "style": {"paddingTop": 12, "color": "#1D2939", "fontSize": 28, "lineHeight": 32, "fontFamily": "ANEK_LATIN", "fontWeight": "bold"}}}, "textContainerStyle": {}, "tagValue": null, "overlayTagValue": null, "style": {"borderRadius": 6, "padding": 8, "paddingTop": 0, "alignItems": "baseline"}, "cardPressEnabled": null, "viewType": "PRIMITIVE_CARD", "iconBackgroundColor": null, "iconSize": null, "orientation": "LANDSCAPE", "requestContext": null, "cardType": null, "next": null, "statusText": null, "subStatusText": null, "actionButtons": null}, "action": null, "rcType": null, "metaData": null, "lenderLogo": null}], "orientation": "PORTRAIT", "failCard": null, "paginationRequestContext": null, "numGridColumns": 0, "gap": 0, "colGap": null, "containerStyle": null}