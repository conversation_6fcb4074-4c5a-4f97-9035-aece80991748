package com.flipkart.fintech.lead.service;

import com.flipkart.fintech.lead.model.LeadResponse;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.google.inject.ImplementedBy;
import java.util.Optional;

@ImplementedBy(LeadServiceImpl.class)
public interface LeadService {

    LeadResponse getCurrentLeadStatus(MerchantUser merchantUser, String requestId) throws PinakaException;

    Optional<ApplicationDataResponse> findActiveApplication(MerchantUser merchantUser)
        throws PinakaException;
}