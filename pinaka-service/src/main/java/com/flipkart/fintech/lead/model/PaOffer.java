package com.flipkart.fintech.lead.model;

import com.flipkart.fintech.pinaka.api.enums.Lender;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PaOffer {

    @NotBlank
    private Lender lender;
    @NotBlank
    private String id;
    @NotNull
    private Long amount;
}
