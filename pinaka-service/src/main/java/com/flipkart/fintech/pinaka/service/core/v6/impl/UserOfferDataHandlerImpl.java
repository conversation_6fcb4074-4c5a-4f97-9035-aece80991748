package com.flipkart.fintech.pinaka.service.core.v6.impl;

import com.flipkart.fintech.pinaka.api.response.v6.UserOfferDataResponse;
import com.flipkart.fintech.pinaka.service.core.v6.UserOfferDataHandler;
import com.flipkart.fintech.pinaka.service.core.v6.mapper.LenderOfferMapper;
import com.flipkart.fintech.pinaka.service.core.v6.mapper.LenderOfferMapperFactory;
import com.flipkart.fintech.pinaka.service.exception.InvalidOfferDataException;
import com.flipkart.fintech.winterfell.client.WinterfellClient;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import lombok.CustomLog;

import javax.inject.Inject;

@CustomLog
public class UserOfferDataHandlerImpl implements UserOfferDataHandler {

    private final WinterfellClient winterfellClient;
    private final LenderOfferMapperFactory lenderOfferMapperFactory;

    @Inject
    public UserOfferDataHandlerImpl(WinterfellClient winterfellClient, LenderOfferMapperFactory lenderOfferMapperFactory) {
        this.winterfellClient = winterfellClient;
        this.lenderOfferMapperFactory = lenderOfferMapperFactory;
    }

    @Override
    public UserOfferDataResponse getUserOfferData(String applicationId, String smUserId, String lender) throws InvalidOfferDataException {
        try {
            LenderOfferMapper lenderOfferMapper = lenderOfferMapperFactory.getMapper(lender);
            if (lenderOfferMapper == null) {
                log.warn("No mapper found for lender: {}", lender);
                return null;
            }

            ApplicationDataResponse applicationDataResponse = winterfellClient.getApplication("CALM", applicationId, smUserId, smUserId);
            if (applicationDataResponse == null) {
                log.error("Null response received from Winterfell for applicationId: {}", applicationId);
                throw new InvalidOfferDataException("No Application Data Response for application Id "+ applicationId);
            }

            return lenderOfferMapper.mapToUserOfferData(applicationDataResponse);

        } catch (InvalidOfferDataException e) {
            log.error("Pinaka Exception at getUserOfferData {}", applicationId);
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error in getUserOfferData for applicationId: {} ", applicationId, e);
            throw new InvalidOfferDataException("Unexpected error processing user offer data");
        }
    }
}