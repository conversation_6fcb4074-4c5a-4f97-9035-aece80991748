package com.flipkart.fintech.pinaka.service.constants;

import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.api.model.request.mandate.MandateStatus;
import com.flipkart.fintech.pinaka.api.enums.*;
import com.flipkart.fintech.pinaka.api.response.dataprovider.Option;
import com.google.common.collect.Lists;

import java.util.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 29/08/17.
 */
public class PinakaConstants {


    public static final String TENANT_FLIPKART = "flipkart";


    public static final List<String> IGNORE_RH_EVENT_PL_STATE_LIST = new ArrayList<>(Arrays.asList("CUSTOMER_IDENTIFICATION_START", "CUSTOMER_IDENTIFICATION_END", "ELIGIBLE_OFFER_START", "ELIGIBLE_OFFER_END", "SUBMIT_OFFER_START", "SUBMIT_OFFER_END", "CREATE_PROFILE_END", "CREATE_PROFILE_START", "REJECTED"));


    public static final Map<UserNodeFormKey, List<ApplicationUpdateContext>> workflowFormKeyContextMap = new HashMap<>();

    static {
        workflowFormKeyContextMap.put(UserNodeFormKey.EKYC_PAN_AADHAAR_SUBMIT, Lists.newArrayList(ApplicationUpdateContext.EKYC_PAN_AADHAAR_SUBMIT));
        workflowFormKeyContextMap.put(UserNodeFormKey.FETCH_AADHAAR_OTP, Lists.newArrayList(ApplicationUpdateContext.EKYC_PAN_AADHAAR_SUBMIT, ApplicationUpdateContext.EKYC_SUBMIT_OTP, ApplicationUpdateContext.EKYC_RESEND_OTP));
        workflowFormKeyContextMap.put(UserNodeFormKey.REVIEW_SCREEN, Lists.newArrayList(ApplicationUpdateContext.KYC_DISCARD, ApplicationUpdateContext.KYC_REVIEW_AND_SUBMIT));
        workflowFormKeyContextMap.put(UserNodeFormKey.COLLECT_PERMISSIONS, Lists.newArrayList(ApplicationUpdateContext.UPDATE_DEVICE_PERMISSIONS));
        workflowFormKeyContextMap.put(UserNodeFormKey.KYC_REJECTED_RETRIABLE, Lists.newArrayList(ApplicationUpdateContext.KYC_DISCARD, ApplicationUpdateContext.KYC_RETRY));
        workflowFormKeyContextMap.put(UserNodeFormKey.WAITING_BUREAU_INSIGHTS, Lists.newArrayList());
        workflowFormKeyContextMap.put(UserNodeFormKey.WAIT_FOR_SMS_UPLOAD, Lists.newArrayList());
        workflowFormKeyContextMap.put(UserNodeFormKey.GET_EXTERNAL_USER_CONSENT, Lists.newArrayList());
        workflowFormKeyContextMap.put(UserNodeFormKey.INITIATE_PENNY_DROP, Lists.newArrayList(ApplicationUpdateContext.CONDITIONAL_APPROVAL_PAGE));
    }

    public static final Map<UserNodeFormKey, List<KycUpdateContext>> workflowkycUpgradeFormKeyContextMap = new HashMap<>();

    static {
        workflowkycUpgradeFormKeyContextMap.put(UserNodeFormKey.AADHAAR_XML_KYC_PAN_SUBMIT, Lists.newArrayList(KycUpdateContext.AADHAAR_XML_PAN_SUBMIT));
        workflowkycUpgradeFormKeyContextMap.put(UserNodeFormKey.INITIATE_AADHAAR_VERIFICATION, Lists.newArrayList(KycUpdateContext.INITIATE_AADHAAR_VERIFICATION, KycUpdateContext.AADHAAR_XML_PAN_SUBMIT));
        workflowkycUpgradeFormKeyContextMap.put(UserNodeFormKey.REVIEW_SCREEN, Lists.newArrayList(KycUpdateContext.KYC_REVIEW_AND_SUBMIT, KycUpdateContext.KYC_DISCARD));
        workflowkycUpgradeFormKeyContextMap.put(UserNodeFormKey.KYC_REJECTED_RETRIABLE, Lists.newArrayList(KycUpdateContext.KYC_DISCARD, KycUpdateContext.KYC_RETRY));
        workflowkycUpgradeFormKeyContextMap.put(UserNodeFormKey.WAITING_FOR_AADHAAR_VERIFICATION, Lists.newArrayList(KycUpdateContext.XML_KYC_DOCUMENT_UPLOAD, KycUpdateContext.INITIATE_AADHAAR_VERIFICATION, KycUpdateContext.AADHAAR_XML_PAN_SUBMIT));
        workflowkycUpgradeFormKeyContextMap.put(UserNodeFormKey.CKYC_PAN_SUBMIT, Lists.newArrayList(KycUpdateContext.CKYC_PAN_SUBMIT));
        workflowkycUpgradeFormKeyContextMap.put(UserNodeFormKey.EKYC_PAN_SUBMIT, Lists.newArrayList(KycUpdateContext.EKYC_PAN_SUBMIT));
        workflowkycUpgradeFormKeyContextMap.put(UserNodeFormKey.CKYC_GENERATE_OTP, Lists.newArrayList(KycUpdateContext.CKYC_GENERATE_OTP, KycUpdateContext.CKYC_PAN_SUBMIT));
        workflowkycUpgradeFormKeyContextMap.put(UserNodeFormKey.EKYC_GENERATE_OTP, Lists.newArrayList(KycUpdateContext.CKYC_GENERATE_OTP, KycUpdateContext.EKYC_PAN_SUBMIT));
        workflowkycUpgradeFormKeyContextMap.put(UserNodeFormKey.FETCH_AADHAAR_OTP, Lists.newArrayList(KycUpdateContext.CKYC_VERIFY_OTP, KycUpdateContext.CKYC_GENERATE_OTP, KycUpdateContext.EKYC_GENERATE_OTP, KycUpdateContext.EKYC_PAN_SUBMIT, KycUpdateContext.CKYC_PAN_SUBMIT));

    }

    private static final Map<String, String> CBC_USAGE_OPTIONS = new LinkedHashMap<String, String>();

    static {
        CBC_USAGE_OPTIONS.put("international", "International & Domestic");
        CBC_USAGE_OPTIONS.put("domestic", "Domestic");
    }

    public static List<Option> CBC_USAGE_TYPE;

    static {
        List<Option> options = new ArrayList<>();
        for (Map.Entry<String, String> optionEntry : PinakaConstants.CBC_USAGE_OPTIONS.entrySet()) {
            Option option = new Option();
            option.setId(optionEntry.getKey());
            option.setTitle(optionEntry.getValue());
            options.add(option);
        }
        CBC_USAGE_TYPE = options;
    }

    public static final Map<String, String> CBC_ADDRESS_OPTIONS = new LinkedHashMap<>();

    static {
        CBC_ADDRESS_OPTIONS.put("current", "Current Residence");
        CBC_ADDRESS_OPTIONS.put("work", "Work");
    }

    public static List<Option> CBC_ADDRESS_TYPE;

    static {
        List<Option> options = new ArrayList<>();
        for (Map.Entry<String, String> optionEntry : PinakaConstants.CBC_ADDRESS_OPTIONS.entrySet()) {
            Option option = new Option();
            option.setId(optionEntry.getKey());
            option.setTitle(optionEntry.getValue());
            options.add(option);
        }
        CBC_ADDRESS_TYPE = options;
    }

    private static final Map<String, String> CBC_YES_NO_OPTIONS = new LinkedHashMap<>();

    static {
        CBC_YES_NO_OPTIONS.put("yes", "Yes");
        CBC_YES_NO_OPTIONS.put("no", "No");
    }

    public static List<Option> CBC_YES_NO_TYPE;

    static {
        List<Option> options = new ArrayList<>();
        for (Map.Entry<String, String> optionEntry : PinakaConstants.CBC_YES_NO_OPTIONS.entrySet()) {
            Option option = new Option();
            option.setId(optionEntry.getKey());
            option.setTitle(optionEntry.getValue());
            options.add(option);
        }
        CBC_YES_NO_TYPE = options;
    }

    private static final Map<String, String> STATE_OPTIONS = new LinkedHashMap<>();

    static {
        STATE_OPTIONS.put("andaman_and_nicobar_islands", "Andaman and Nicobar Islands");
        STATE_OPTIONS.put("andhra_pradesh", "Andhra Pradesh");
        STATE_OPTIONS.put("arunachal_pradesh", "Arunachal Pradesh");
        STATE_OPTIONS.put("assam", "Assam");
        STATE_OPTIONS.put("bihar", "Bihar");
        STATE_OPTIONS.put("chandigarh", "Chandigarh");
        STATE_OPTIONS.put("chhasttisgarh", "Chhattisgarh");
        STATE_OPTIONS.put("dadra_and_nagar_haveli", "Dadra and Nagar Haveli");
        STATE_OPTIONS.put("daman_and_diu", "Daman and Diu");
        STATE_OPTIONS.put("delhi", "Delhi");
        STATE_OPTIONS.put("goa", "Goa");
        STATE_OPTIONS.put("gujarat", "Gujarat");
        STATE_OPTIONS.put("haryana", "Haryana");
        STATE_OPTIONS.put("himachal_pradesh", "Himachal Pradesh");
        STATE_OPTIONS.put("jammu_&_kashmir", "Jammu & Kashmir");
        STATE_OPTIONS.put("jharkhand", "Jharkhand");
        STATE_OPTIONS.put("karnataka", "Karnataka");
        STATE_OPTIONS.put("kerala", "Kerala");
        STATE_OPTIONS.put("lakshadweep", "Lakshadweep");
        STATE_OPTIONS.put("madhya_pradesh", "Madhya Pradesh");
        STATE_OPTIONS.put("maharashtra", "Maharashtra");
        STATE_OPTIONS.put("manipur", "Manipur");
        STATE_OPTIONS.put("meghalaya", "Meghalaya");
        STATE_OPTIONS.put("mizoram", "Mizoram");
        STATE_OPTIONS.put("nagaland", "Nagaland");
        STATE_OPTIONS.put("odisha", "Odisha");
        STATE_OPTIONS.put("pondicherry", "Pondicherry");
        STATE_OPTIONS.put("punjab", "Punjab");
        STATE_OPTIONS.put("rajasthan", "Rajasthan");
        STATE_OPTIONS.put("sikkim", "Sikkim");
        STATE_OPTIONS.put("tamilnadu", "Tamilnadu");
        STATE_OPTIONS.put("telangana", "Telangana");
        STATE_OPTIONS.put("tripura", "Tripura");
        STATE_OPTIONS.put("uttarakhand", "Uttarakhand");
        STATE_OPTIONS.put("uttar_padesh", "Uttar Pradesh");
        STATE_OPTIONS.put("west_bengal", "West Bengal");
    }

    public static List<Option> STATES;

    static {
        List<Option> options = new ArrayList<>();
        for (Map.Entry<String, String> optionEntry : PinakaConstants.STATE_OPTIONS.entrySet()) {
            Option option = new Option();
            option.setId(optionEntry.getKey());
            option.setTitle(optionEntry.getValue());
            options.add(option);
        }
        STATES = options;
    }


    private static final Map<String, String> GENDER_OPTIONS = new LinkedHashMap<>();

    static {
        GENDER_OPTIONS.put("male", "Male");
        GENDER_OPTIONS.put("female", "Female");
        GENDER_OPTIONS.put("transgender", "Transgender");
    }

    public static final List<Option> GENDERS;

    static {
        List<Option> options = new ArrayList<>();
        for (Map.Entry<String, String> optionEntry : PinakaConstants.GENDER_OPTIONS.entrySet()) {
            Option option = new Option();
            option.setId(optionEntry.getKey());
            option.setTitle(optionEntry.getValue());
            options.add(option);
        }
        GENDERS = options;
    }

    public static final Map<String, String> CBC_GENDER_OPTIONS = new LinkedHashMap<>();

    static {
        CBC_GENDER_OPTIONS.put("male", "Male");
        CBC_GENDER_OPTIONS.put("female", "Female");
        CBC_GENDER_OPTIONS.put("transgender", "Transgender");
    }

    public static final Map<String, String> EDUCATION_OPTIONS = new LinkedHashMap<>();

    static {
        EDUCATION_OPTIONS.put("undergraduate", "Undergraduate");
        EDUCATION_OPTIONS.put("graduate", "Graduate");
        EDUCATION_OPTIONS.put("post graduate", "Post Graduate");
    }


    public class ConfigFeature {
        public static final String FEATURE_NAME_PREFIX = ".cug.feature";

    }

    //AB EXPERIMENTS
    public static final String OFFER_ORCHESTRATOR_MODE = "offerOrchestratorMode";
    public static final String OFFER_ORCHESTRATOR_MODE_SHADOW = "SHADOW";
    public static final String OFFER_ORCHESTRATOR_MODE_LIVE = "LIVE";


    public class CacheContext {
        public static final String PAN_RETRY_CLC_IB = "PAN_RETRY_CLC_IB";
    }


    public static final Map<MandateState, com.flipkart.fintech.pandora.api.model.request.mandate.MandateStatus> pinakaToPandoraMandateStateMapper = new HashMap<>();

    static {
        pinakaToPandoraMandateStateMapper.put(MandateState.SUCCESS, MandateStatus.SUCCESS);
        pinakaToPandoraMandateStateMapper.put(MandateState.FAILED, MandateStatus.FAILURE);
    }

    public static final Map<com.flipkart.affordability.collections.model.enums.mandate.MandateStatus, MandateState> collectionToPinakaMandateStateMapper = new HashMap<>();

    static {
        collectionToPinakaMandateStateMapper.put(com.flipkart.affordability.collections.model.enums.mandate.MandateStatus.CONFIRMED, MandateState.SUCCESS);
        collectionToPinakaMandateStateMapper.put(com.flipkart.affordability.collections.model.enums.mandate.MandateStatus.INITIATED, MandateState.FAILED);
        collectionToPinakaMandateStateMapper.put(com.flipkart.affordability.collections.model.enums.mandate.MandateStatus.FAILED, MandateState.FAILED);
    }

    public static final Map<STATUS, MandateState> pandoraToPinakaMandateStateMapper = new HashMap<>();

    static {
        pandoraToPinakaMandateStateMapper.put(STATUS.SUCCESS, MandateState.SUCCESS);
        pandoraToPinakaMandateStateMapper.put(STATUS.FAILURE, MandateState.FAILED);
    }

    public static final Map<MandateState, com.flipkart.affordability.collections.model.enums.mandate.MandateStatus> pinakaToCollectionsMandateStateMapper = new HashMap<>();

    static {
        pinakaToCollectionsMandateStateMapper.put(MandateState.SUCCESS, com.flipkart.affordability.collections.model.enums.mandate.MandateStatus.CONFIRMED);
        pinakaToCollectionsMandateStateMapper.put(MandateState.FAILED, com.flipkart.affordability.collections.model.enums.mandate.MandateStatus.FAILED);
    }


    public static class KYC {
        public static final String KYC_REJECT_PAGE_CONTENT_KEY = "%s.%s.%s.kyc_reject.page.content";
        public static final String JOURNEY_SPECIFIC_KYC_REJECT_PAGE_CONTENT_KEY = "%s.%s.%s.%s.%s.kyc_reject.page.content";
    }


    public static final Map<String, Integer> CBC_TRANSACTION_VALID_TO_SHOW_MAP = new HashMap<>();

    static {
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("CRADJ", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("05", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("CRDSP", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("15", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("35", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("PAYMT", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("TEXT ", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("TFEE ", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("1240", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("PFEE ", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("10", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("20", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("25", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("07", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("27", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("FEE", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("DBADJ", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("CASH", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("06", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("TX", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("SFEE", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("17", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("REDEM", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("DBINT", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("DBDSP", 1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("26", 1);
    }

    public static final Map<String, Integer> CBC_MCC_TO_ICON_MAP = new HashMap<>();

    static {
        CBC_MCC_TO_ICON_MAP.put("FOOD", 0);
        CBC_MCC_TO_ICON_MAP.put("TRAVEL", 1);
        CBC_MCC_TO_ICON_MAP.put("HEALTH", 2);
        CBC_MCC_TO_ICON_MAP.put("BILL", 3);
        CBC_MCC_TO_ICON_MAP.put("GROCERY", 4);
        CBC_MCC_TO_ICON_MAP.put("SHOPPING", 5);
        CBC_MCC_TO_ICON_MAP.put("FUEL", 6);
        CBC_MCC_TO_ICON_MAP.put("COMMUTE", 7);
        CBC_MCC_TO_ICON_MAP.put("CREDIT BILL", 8);
        CBC_MCC_TO_ICON_MAP.put("MISC", 9);
        CBC_MCC_TO_ICON_MAP.put("CASH", 10);
        CBC_MCC_TO_ICON_MAP.put("ENTERTAINMENT", 11);
    }

    public static class CompanyMasterData {
        public static final String EMPLOYER_INDEX_PATTERN = "cbc.employer.index.pattern";
        public static final String EMPLOYER_SECTOR = "employerSector";
        public static final String INDUSTRY_TYPE = "industryType";
        public static final String SECTOR_DROPDOWN_VALUE = "Sector_DropDown_Value";
        public static final String INDUSTRY_DROPDOWN_VALUE = "Industry_DropDown_Value";
        public static final String EMPLOYER_NAME = "Employer_Name";
        public static final String EMPLOYER_AUTO_SUGGEST_SIZE = "cbcEmployerAutoSuggestSize";
        public static final Integer EMPLOYER_AUTO_SUGGEST_DEFAULT_SIZE = 10;
        public static final String OTHERS = "OTHERS";
        public static final String EMPLOYER_INDEX_PATTERN_DEFAULT = "fintech_cbc_employer_details*";
        public static final String PL_EMP_AUTO_SUGGEST_SIZE = "plEmployerAutoSuggestSize";
        public static final Integer PL_EMP_AUTO_SUGGEST_DEFAULT_SIZE = 10;
        public static final String PL_EMPLOYER_INDEX_PATTERN = "plEmployerIndexPattern";
        public static final String PL_EMPLOYER_INDEX_NAME_KEY = "plEmployerIndexNameKey";
        public static final String PL_PINCODE_INDEX_NAME_KEY = "plPincodeIndexNameKey";
        public static final String PL_EMPLOYER_NAME = "employer_name";
        public static final String PL_EMP_ID = "employer_id";
    }

    public static final String PIPE_SEPARATOR = "|";
    public static final String EMPTY_STRING = "";
    public static final String DATE_FORMAT_PATTERN = "yyyy-MM-dd HH:mm:ss";

    public static class PLConstants {
        public static final String APPLICATION_ID = "applicationId";
        public static final String PL_STATUS_URL = "https://www.shopsy.in/sm-3p/pl/pages/status";
        public static final String TASK_ID = "taskId";
        public static final String TASK_KEY = "taskKey";
        public static final String PROCESS_INSTANCE_ID = "processInstanceId";
        public static final String USER_COHORT = "userCohort";
        public static final String TOKEN = "token";
        public static final String RETRY_WITH_EDIT_MESSAGE = "Please check the details and Retry";
        public static final String RETRY_WITHOUT_EDIT_MESSAGE = "Please retry after sometime";
        public static final String OFFER = "offer";
        public static final String CONSENT_DETAILS = "consentDetails";
        public static final String LENDER_PLATFORM = "LENDER_PLATFORM";
        public static final String SEPARATOR = " \\|\\| ";
        public static final String PL_TNC_RELATIVE_PATH = "scapic-axis-tnc";
        public static final List<String> PL_TERMINAL_STATES = Arrays.asList("APPLICATION_COMPLETED", "SUCCESS", "REJECTED", "DISBURSAL_IN_PROGRESS");
        public static final String PL_ENCRYPTION_KEY = "CCv7SRrJuatGmf3C";
        public static final String PL_DATA_ENCRYPTION_KEY = "BBv7SRrJuatGmf2G";
        public static final String LEAD_V2_WL_USERS = "LEAD_V2_WL_USERS";
        public static final String LEAD_V2_TRAFFIC = "LEAD_V2_TRAFFIC";
        public static final String ES_CLIENT_TIMEOUT_MS_DEFAULT = "3000";
        public static final String LEAD_V3_WL_USERS = "LEAD_V3_WL_USERS";
        public static final String LEAD_V3_TRAFFIC = "LEAD_V3_TRAFFIC";
        public static final String ENABLE_LEAD_V4_FLOW = "ENABLE_LEAD_V4_FLOW";
        public static final String LEAD_V4_WHITELISTED_USERS = "LEAD_V4_WHITELISTED_USERS";
        public static final String LEAD_V4_TRAFFIC_PERCENTAGE = "LEAD_V4_TRAFFIC_PERCENTAGE";
        public static final String SMONEY_PL_DATA_ENCRYPTION_KEY = "cGlpRW5jcnlwdGlvbktleQ==";
        public static final int ROW_COUNT = 5;
        public static final String ENCRYPTION_DATA = "encryptionData";
        public static final String ENC_KEY = "encKey";
        public static final String ENC_KEY_REF = "encKeyRef";
        public static final String CREATE_SOURCE = "CREATE_SOURCE";
        public static final String CUSTOMER_STATUS = "customerStatus";
        public static final String ENABLE_PL_MAINTENANCE = "enableMaintenancePage";
        public static final String ENABLE_OD_LANDING_PAGE = "enableOdLandingPage";
        public static final String ENABLE_PL_FDP_INGESTION = "enablePlFdpIngestion";
        public static final String ENABLE_PL_BQ_INGESTION = "enablePlBqIngestion";
        public static final String ENABLE_PL_BQ_INGESTION_EVENTS = "enablePlBqIngestionEvents";
        public static final String MERCHANT = "merchant";
        public static final String ENABLE_PL_UI_EVENT_INGESTION = "enableUIEventIngestion";
        public static final boolean ENABLE_PL_UI_EVENT_INGESTION_DEFAULT_FLAG = true;
        public static final String PA_OFFER = "paOffer";
        public static final String SPACE = " ";
    }

    public static class PLAlfredConstants {
        public static final String ALFRED_CLIENT_VERSION = "20230301";
        public static final String CBC_JRM_V1 = "CBC-JRM-V1";
        public static final String CBC_JRM_V3 = "CBC-JRM-V3";
        public static final String ADDRESS_CONFIDENCE_V1 = "Address-Confidence-V1";
        public static final String MOST_USED_ADDRESS_ID = "most_used_address_id";
        public static final String SCORE = "SCORE";
        public static final String BIN = "BIN";
        public static final String CREMO_BAND = "cremo_band";
        public static final String BNPL_UNDERWRITING = "BNPL_UNDERWRITING";
    }

    public static class PLFormDataConstants {
        public static final String INCOME = "income";
        public static final String BONUS_INCOME = "bonusIncome";
        public static final String REVIEW_PAGE_2_TASK_KEY = "reviewPage2";
    }

    public static class PLWorkflowVariable {
        public static final String LOAN_AMOUNT = "loan_amount";
        public static final String ROI = "roi";
        public static final String EMI = "emi";
        public static final String NET_DISBURSAL_AMOUNT = "net_disbursal_amount";
        public static final String CALLBACK_URL = "callback_url";
        public static final String OFFER_CONSENT_PROVIDED = "offer_consent_provided";
        public static final String LENDER_APPLICATION_ID = "lender_application_id";
        public static final String TENURE = "tenure";
        public static final String CHARGES = "charges";
        public static final String LENDER_DETAILS = "lender_details";
        public static final String USER_DETAILS = "user_details";
        public static final String FORM_DETAILS = "form_details";
        public static final String CI_STATUS = "ci_status";
        public static final String SO_STATUS = "so_status";
        public static final String EO_STATUS = "eo_status";
        public static final String LENDER_STATUS = "lenderStatus";
        public static final String ERROR_MESSAGE = "error_message";
        public static final String APP_STATUS = "appStatusResponse";
        public static final String CODE = "code";
    }

    public static class ConsentConstants {
        public static final String CURRENT_TIMESTAMP = "currentTimeStamp";
        public static final String USER_IP = "userIP";
        public static final String LENDING = "LENDING";
        public static final String DEVICE_ID = "deviceId";
        public static final String DEVICE_INFO = "deviceInfo";
    }

    public static class BigtableConstants {
        public static final String PL_INVALID_AT = "pl-invalid-at";
        public static final String UPI_INVALID_AT = "upi-invalid-at";
        public static final String PL_VISITORS = "pl-visitors";
        public static final String UPI_VISITORS = "upi-visitors";
    }

    public static class MetricRegistryConstants {
        public static final String PAN_PREFILL_FROM_PROFILE = "PAN_PREFILL_FROM_PROFILE";
        public static final String DOB_PREFILL_FROM_PROFILE = "DOB_PREFILL_FROM_PROFILE";
        public static final String EMAIL_PREFILL_FROM_PROFILE = "EMAIL_PREFILL_FROM_PROFILE";
        public static final String ADDRESS_LINE_1_PREFILL_FROM_PROFILE = "ADDRESS_LINE_1_PREFILL_FROM_PROFILE";
        public static final String ADDRESS_LINE_2_PREFILL_FROM_PROFILE = "ADDRESS_LINE_2_PREFILL_FROM_PROFILE";
        public static final String PINCODE_PREFILL_FROM_PROFILE = "PINCODE_PREFILL_FROM_PROFILE";
        public static final String GENDER_PREFILL_FROM_PROFILE = "GENDER_PREFILL_FROM_PROFILE";
        public static final String EMPLOYMENT_TYPE_PREFILL_FROM_PROFILE = "EMPLOYMENT_TYPE_PREFILL_FROM_PROFILE";
        public static final String MONTHLY_INCOME_PREFILL_FROM_PROFILE = "MONTHLY_INCOME_PREFILL_FROM_PROFILE";
        public static final String BONUS_INCOME_PREFILL_FROM_PROFILE = "BONUS_INCOME_PREFILL_FROM_PROFILE";
        public static final String ANNUAL_TURNOVER_PREFILL_FROM_PROFILE = "ANNUAL_TURNOVER_PREFILL_FROM_PROFILE";
        public static final String INCOME_SOURCE_PREFILL_FROM_PROFILE = "INCOME_SOURCE_PREFILL_FROM_PROFILE";
        public static final String APPLY_NOW = "apply-now.";
        public static final String FETCH_BULK_DATA = "fetch-bulk-data.";
        public static final String SUBMIT = "submit.";
        public static final String HOMEPAGE = "homepage";
        public static final String LEAD_V4_LANDING_PAGE = "LEAD_V4_LANDING_PAGE";
    }
}
