package com.flipkart.fintech.pinaka.service.ruleengine;

import java.util.Map;
import java.util.concurrent.Callable;
import lombok.CustomLog;

@CustomLog
public abstract class RuleEngine<R extends RuleEngineInput, T extends RuleEngineStates, S> {

  protected abstract String getRuleEngineName();

  public S executeRuleEngineState(Map<T, Callable<S>> executionMap, R input) throws Exception {
    T state = getStateByRuleEngine(input);
    log.info("State by {}: {}", getRuleEngineName(), state.toString());
    return executionMap.get(state).call();
  }

  protected abstract T getStateByRuleEngine(R input);
}
