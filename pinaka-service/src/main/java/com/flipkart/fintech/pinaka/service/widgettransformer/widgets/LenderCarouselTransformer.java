package com.flipkart.fintech.pinaka.service.widgettransformer.widgets;

import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardCarouselWidgetDataV0;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardWidgetData;

public interface LenderCarouselTransformer {
    CardCarouselWidgetDataV0 buildWidgetData(ApplicationDataResponse applicationDataResponse) throws PinakaException;
}
