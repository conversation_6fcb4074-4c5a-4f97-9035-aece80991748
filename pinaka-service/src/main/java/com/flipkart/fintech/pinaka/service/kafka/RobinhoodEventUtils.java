package com.flipkart.fintech.pinaka.service.kafka;

import com.flipkart.fintech.exception.ServiceErrorResponse;
import com.flipkart.fintech.exception.ServiceException;
import com.flipkart.fintech.pandora.api.model.response.plOnboarding.LoanAmountDetail;
import com.flipkart.fintech.pinaka.api.request.v6.LenderDetails;
import com.flipkart.fintech.pinaka.api.request.v6.Offer;
import lombok.CustomLog;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.IGNORE_RH_EVENT_PL_STATE_LIST;

@CustomLog
public class RobinhoodEventUtils {

    private static List<String> idfcStates = new ArrayList<>();

    static {
        idfcStates.add("BASIC_DETAILS");
        idfcStates.add("VERIFY_PAN");
        idfcStates.add("GENERATE_OFFER");
        idfcStates.add("POLL_OFFER_TIMER");
        idfcStates.add("POLL_OFFER");
        idfcStates.add("OFFER_SCREEN");
        idfcStates.add("SEARCH_DOWNLOAD_CKYC");
        idfcStates.add("CKYC_DETAILS");
        idfcStates.add("GENERATE_SELFIE_TOKEN");
        idfcStates.add("SELFIE_SCREEN");
        idfcStates.add("MATCH_SELFIE_DATA");
        idfcStates.add("UPLOAD_SELFIE_DATA");
        idfcStates.add("START_LOAN");
        idfcStates.add("GET_APPROVAL_STATUS");
        idfcStates.add("BANK_DETAILS");
        idfcStates.add("IFSC_DETAILS");
        idfcStates.add("REPAYMENT_MODES");
        idfcStates.add("INITIATE_PENNY_DROP");
        idfcStates.add("GENERATE_EMANDATE_URL");
        idfcStates.add("EMANDATE_REDIRECTION");
        idfcStates.add("GET_EMANDATE_STATUS");
        idfcStates.add("GET_EMANDATE_STATUS_RETRY");
        idfcStates.add("ONBOARD_LOAN");
        idfcStates.add("KFS_SCREEN");
        idfcStates.add("GENERATE_KFS_OTP");
        idfcStates.add("KFS_OTP_VERIFICATION");
        idfcStates.add("VALIDATE_KFS_OTP");
        idfcStates.add("AUTH_AUTO_DISBURSAL");
        idfcStates.add("APPLICATION_STATUS");
        idfcStates.add("GET_PAYMENT_DETAILS");
        idfcStates.add("CREATE_PROFILE_END");
        idfcStates.add("CREATE_PROFILE_START");
        idfcStates.add("CI_DETAILS_ADDR");
    }

    public Double getEligibleAmount(String plAppState, LenderDetails lenderDetails, String applicationId, String lender) {
        if (!StringUtils.isBlank(lender) && (lender.toLowerCase() != "axis" || lender.toLowerCase() != "idfc")) {
            return (double) 0;
        }
        if (!"CI_DETAILS".equals(plAppState) && !"ADDITIONAL_DETAILS".equals(plAppState) && !idfcStates.contains(plAppState)) {
            if (Objects.nonNull(lenderDetails)) {
                List<Offer> submittedOffer;
                switch (plAppState) {
                    case "OFFER_DETAILS":
                        List<com.flipkart.fintech.pandora.api.model.response.plOnboarding.Offer> generatedOffer = lenderDetails.getGeneratedOffer();
                        if (CollectionUtils.isEmpty(generatedOffer)) {
                            log.error("generated offer is null for app state: {} & appId: {}", plAppState, applicationId);
                            throw new ServiceException(new ServiceErrorResponse(
                                    Response.Status.INTERNAL_SERVER_ERROR, Response.Status.INTERNAL_SERVER_ERROR.getReasonPhrase(),
                                    String.format("generated offer is null for app state: %s & appId: %s", plAppState, applicationId)));
                        }
                        LoanAmountDetail loanAmountDetail = generatedOffer.get(0).getLoanAmountDetail();
                        if (Objects.isNull(loanAmountDetail)) {
                            log.error("loan amount detail is null for app state: {} & appId: {}", plAppState, applicationId);
                            throw new ServiceException(new ServiceErrorResponse(
                                    Response.Status.INTERNAL_SERVER_ERROR, Response.Status.INTERNAL_SERVER_ERROR.getReasonPhrase(),
                                    String.format("loan amount detail is null for app state: %s & appId: %s", plAppState, applicationId)));
                        }

                        return (double) loanAmountDetail.getMax();
                    case "REJECTED":
                        submittedOffer = lenderDetails.getSubmittedOffer();
                        if (CollectionUtils.isEmpty(submittedOffer)) {
                            return null;
                        }
                        return (double) submittedOffer.get(0).getLoanAmount();
                    case "APPLICATION_COMPLETED":
                    case "SUCCESS":
                        submittedOffer = lenderDetails.getSubmittedOffer();
                        if (CollectionUtils.isEmpty(submittedOffer)) {
                            log.error("submitted offer is null for app state: {} & appId: {}", plAppState, applicationId);
                            throw new ServiceException(new ServiceErrorResponse(
                                    Response.Status.INTERNAL_SERVER_ERROR, Response.Status.INTERNAL_SERVER_ERROR.getReasonPhrase(),
                                    String.format("submitted offer is null for app state: %s & appId: %s", plAppState, applicationId)));
                        }

                        return (double) submittedOffer.get(0).getLoanAmount();
                    default:
                        log.error("Unhandled app state: {} for fetching eligible amount", plAppState);
                        throw new ServiceException(new ServiceErrorResponse(
                                Response.Status.INTERNAL_SERVER_ERROR, Response.Status.INTERNAL_SERVER_ERROR.getReasonPhrase(),
                                String.format("Unhandled app state: %s for fetching eligible amount", plAppState)));
                }
            } else {
                if ("REJECTED".equals(plAppState)) {
                    return null;
                }
                log.error("Lender Details is null for state: {} & appId: {}", plAppState, applicationId);
                throw new ServiceException(new ServiceErrorResponse(
                        Response.Status.INTERNAL_SERVER_ERROR, Response.Status.INTERNAL_SERVER_ERROR.getReasonPhrase(),
                        String.format("Lender Details is null for state: %s", plAppState)));
            }
        } else {
            return null;
        }
    }

    public Boolean checkIgnoreState(String plAppState, String financialProvider) {
        if (IGNORE_RH_EVENT_PL_STATE_LIST.contains(plAppState) || plAppState.endsWith("_START") || plAppState.endsWith("_END") || (financialProvider.toLowerCase().equals("axis") && plAppState.equals("LENDER_PLATFORM"))) {
            return true;
        }
        return false;
    }

}
