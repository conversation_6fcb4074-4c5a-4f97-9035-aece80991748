package com.flipkart.fintech.pinaka.service.web.v6;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.pinaka.api.request.v6.UIEventIngestionRequest;
import com.flipkart.fintech.pinaka.api.response.v6.UIEventIngestionResponse;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.fintech.pinaka.service.core.v6.UIEventIngestionHandler;
import com.flipkart.kloud.config.DynamicBucket;
import io.swagger.annotations.ApiOperation;
import lombok.CustomLog;

import javax.inject.Inject;
import javax.validation.constraints.NotNull;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.Optional;

@CustomLog
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("1/ui")
public class UIEventResource {

    private final UIEventIngestionHandler uiEventIngestionHandler;
    private final DynamicBucket dynamicBucket;

    @Inject
    public UIEventResource(UIEventIngestionHandler uiEventIngestionHandler, DynamicBucket dynamicBucket) {
        this.uiEventIngestionHandler = uiEventIngestionHandler;
        this.dynamicBucket = dynamicBucket;
    }

    @POST
    @Timed
    @ExceptionMetered
    @ApiOperation(value = "ingest batched UI events")
    @Path("/batch-event")
    public Response ingestUIEvents(@javax.validation.Valid UIEventIngestionRequest uiEventIngestionRequest,
       @NotNull @HeaderParam("X-Request-Id") String requestId,
       @NotNull @HeaderParam("X-Merchant-Id") String merchantId) {
        // Adding config flag for API rollout
        if(!Optional.ofNullable(dynamicBucket.getBoolean(PinakaConstants.PLConstants.ENABLE_PL_UI_EVENT_INGESTION))
                .orElse(PinakaConstants.PLConstants.ENABLE_PL_UI_EVENT_INGESTION_DEFAULT_FLAG)) {
            log.info("UI event flow is disabled");
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(UIEventIngestionResponse.builder()
                    .status("FAILED - FLOW DISABLED")
                    .batchId(uiEventIngestionRequest.getBatchId())
                    .build()).build();
        }
        try {
            uiEventIngestionHandler.publishBatchEvents(uiEventIngestionRequest.getEvents());
        } catch (Exception e) {
            log.error(" publishBatchEvents() | Exception {} ", e.getMessage());
            return Response.status(Response.Status.INTERNAL_SERVER_ERROR).entity(UIEventIngestionResponse.builder()
                    .status("FAILED")
                    .batchId(uiEventIngestionRequest.getBatchId())
                    .build()).build();
        }
        return Response.status(Response.Status.OK).entity(UIEventIngestionResponse.builder()
                .status("SUCCESS")
                .build()).build();
    }
}