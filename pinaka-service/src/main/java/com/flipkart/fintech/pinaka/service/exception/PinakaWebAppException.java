package com.flipkart.fintech.pinaka.service.exception;

import io.dropwizard.jersey.errors.ErrorMessage;

import javax.ws.rs.WebApplicationException;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 31/08/17.
 */
public class PinakaWebAppException extends WebApplicationException {
    public PinakaWebAppException(String message){super(message);}

    public PinakaWebAppException(Response.Status status, String message){
        super(Response.status(status.getStatusCode()).
                entity(new ErrorMessage(message)).type(MediaType.APPLICATION_JSON_TYPE).build());
    }

    public PinakaWebAppException(int statusCode, String message){
        super(Response.status(statusCode).
                entity(new ErrorMessage(message)).type(MediaType.APPLICATION_JSON_TYPE).build());
    }

    public PinakaWebAppException(Throwable cause){super(cause);}
}
