package com.flipkart.fintech.pinaka.service.ruleengine.models;

import com.flipkart.fintech.pinaka.api.response.v6.Action;
import com.flipkart.fintech.pinaka.api.response.v6.ActionType;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;

public enum PlLandingPageStates {
    REPEAT_LOAN_LP,
    RESUME_JOURNEY_LOAN_LP,
    LEAD_REPEAT_LOAN_LP,
    OFFER_LP,
    ACTIVE_LOANS_SERVICING_LP;

    public String getAmsUrl(){
        switch (this){
            case REPEAT_LOAN_LP:
                return "/ams/v1/landing-page";
            case LEAD_REPEAT_LOAN_LP:
                return "/ams/v1/landing-page";
            case OFFER_LP:
                return "/ams/v1/offer-details-landing";
            default:
                return "default";
        }
    }

    public PageActionResponse getPageActionResponse(){
        return PageActionResponse.builder()
                .action(Action.builder()
                        .url(this.getAmsUrl())
                        .actionType(ActionType.NAVIGATION)
                        .build())
                .actionSuccess(true)
                .build();
    }
}
