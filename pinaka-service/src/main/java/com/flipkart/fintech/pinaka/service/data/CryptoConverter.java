package com.flipkart.fintech.pinaka.service.data;

import com.flipkart.fintech.pinaka.service.utils.CryptoUtils;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.<PERSON> on 22/03/18.
 */
@Converter
public class CryptoConverter implements AttributeConverter<String, String> {

    @Override
    public String convertToDatabaseColumn(String plainText) {
        return CryptoUtils.encrypt(plainText);
    }

    @Override
    public String convertToEntityAttribute(String encryptedText) {
        return CryptoUtils.decrypt(encryptedText);
    }

}
