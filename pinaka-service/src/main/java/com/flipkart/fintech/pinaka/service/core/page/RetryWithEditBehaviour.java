package com.flipkart.fintech.pinaka.service.core.page;

import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.RETRY_WITH_EDIT_MESSAGE;

import com.flipkart.fintech.pinaka.api.response.v6.ErrorOperation;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.api.response.v6.Status;

public class RetryWithEditBehaviour {

  private static final String message = String.format("%s|%s", Status.RETRY_WITH_EDIT, RETRY_WITH_EDIT_MESSAGE);

  public static PageActionResponse getPageActionResponse() {
    ErrorOperation errorOperation = new ErrorOperation();
    errorOperation.setMessage(message);
    return new PageActionResponse(null, false, errorOperation, null);
  }

}
