package com.flipkart.fintech.pinaka.service.web;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.pinaka.api.response.SecurityKeyResponse;
import com.flipkart.fintech.pinaka.service.application.filter.IgnoreFilter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * Created by sujeetkumar.r on 25/01/18.
 */

@Path("security")
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Api(value = "/security", description = "security")
@IgnoreFilter
public interface SecurityResource {

    @GET
    @Path("/key")
    @ApiOperation(value = "fetch security key")
    @ApiResponses(value = {
            @ApiResponse(code = 200, message = "success", response = SecurityKeyResponse.class)
    })
    @Timed
    @ExceptionMetered
    Response getKey();
}
