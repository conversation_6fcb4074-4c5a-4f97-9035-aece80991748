package com.flipkart.fintech.pinaka.service.adaptor.v6;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.ardour.api.models.EncryptionKeyData;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.UserDetails;
import com.flipkart.fintech.pinaka.api.request.v6.*;
import com.flipkart.fintech.pinaka.api.response.v6.*;
import com.flipkart.fintech.pinaka.service.adaptor.v6.formFields.FormFieldContent;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;
import com.flipkart.fintech.pinaka.service.constants.PageConstant;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import com.flipkart.fintech.pinaka.service.core.v6.impl.SecurityResourceHandler;
import com.flipkart.fintech.pinaka.service.enums.PLUserCohort;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.exception.v6.DataEnrichmentException;
import com.flipkart.fintech.pinaka.service.utils.EncryptionUtil;
import com.flipkart.fintech.pinaka.service.utils.v6.FormUtils;
import com.flipkart.fintech.winterfell.api.request.VariableData;
import com.codahale.metrics.MetricRegistry;
import liquibase.util.StringUtils;
import lombok.CustomLog;

import javax.inject.Inject;
import java.time.LocalDateTime;
import java.util.*;

@CustomLog
public class PersonalDetailsAdapter implements IPageAdapter {
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final SecurityResourceHandler securityService;

    @Inject
    public PersonalDetailsAdapter(SecurityResourceHandler securityService) {
        this.securityService = securityService;
    }

    @Override
    public Map<String, VariableData> constructWorkflowData(LoanApplication loanApplication, Map<String, Object> formData,
                                                           MerchantUser merchantUser, String applicationId, String requestId) throws DataEnrichmentException {
        Map<String, VariableData> variableDataMap = new HashMap<>();

        FormField panFormField = FormUtils.getFormField(FormFieldContent.PAN_TEXT_BOX);
        FormField dobFormField = FormUtils.getFormField(FormFieldContent.DOB_TYPE);
        FormField genderFormField = FormUtils.getFormField(FormFieldContent.GENDER);
        FormField consentFormField = FormUtils.getFormField(FormFieldContent.CONSENT_PAN_DETAILS);

        FormField cityField = FormUtils.getFormField(FormFieldContent.CITY);
        FormField houseField = FormUtils.getFormField(FormFieldContent.HOUSE_NUMBER);
        FormField areaField = FormUtils.getFormField(FormFieldContent.AREA);
        FormField stateField = FormUtils.getFormField(FormFieldContent.STATE);
        FormField pincodeField = FormUtils.getFormField(FormFieldContent.PINCODE);

        Map<String, Object> responseObj = (Map<String, Object>) formData.get(PinakaConstants.PLConstants.ENCRYPTION_DATA);
        EncryptionKeyData encryptionKeyData = EncryptionKeyData.builder()
                .encKey((String) responseObj.get(PinakaConstants.PLConstants.ENC_KEY))
                .encKeyRef((String) responseObj.get(PinakaConstants.PLConstants.ENC_KEY_REF)).build();
        String pan;
        try {
            pan = securityService.getDecryptedData(encryptionKeyData, (String) formData.get(panFormField.getName()));
            if(!Objects.isNull(formData.get(cityField.getName()))&& !formData.get(cityField.getName()).equals("")){
                String city = securityService.getDecryptedData(encryptionKeyData, (String) formData.get(cityField.getName()));
                variableDataMap.put("city", new VariableData(false, EncryptionUtil.encryptWithAes(city, PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY)));
            }
            if(!Objects.isNull(formData.get(stateField.getName()))&& !formData.get(stateField.getName()).equals("")){
                String state = securityService.getDecryptedData(encryptionKeyData, (String) formData.get(stateField.getName()));
                variableDataMap.put("state", new VariableData(false, EncryptionUtil.encryptWithAes(state, PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY)));
            }
            if(!Objects.isNull(formData.get(areaField.getName()))&& !formData.get(areaField.getName()).equals("")){
                String area = securityService.getDecryptedData(encryptionKeyData, (String) formData.get(areaField.getName()));
                variableDataMap.put("area", new VariableData(false, EncryptionUtil.encryptWithAes(area, PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY)));
            }
            if(!Objects.isNull(formData.get(houseField.getName()))&& !formData.get(houseField.getName()).equals("")){
                String house = securityService.getDecryptedData(encryptionKeyData, (String) formData.get(houseField.getName()));
                variableDataMap.put("house", new VariableData(false, EncryptionUtil.encryptWithAes(house, PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY)));
            }
        } catch (PinakaException e) {
            log.error("Error in decrypting the user details");
            throw new DataEnrichmentException(e);
        }
        if(!Objects.isNull(formData.get(pincodeField.getName()))&& !formData.get(pincodeField.getName()).equals("")){
            variableDataMap.put("pincode", new VariableData(false, formData.get(pincodeField.getName())));
        }

        variableDataMap.put("pan_number", new VariableData(false, EncryptionUtil.encryptWithAes(pan, PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY)));
        variableDataMap.put("dob", new VariableData(false, formData.get(dobFormField.getName())));
        variableDataMap.put("gender", new VariableData(false, formData.get(genderFormField.getName())));
        variableDataMap.put("application_id", new VariableData(false, applicationId));
        variableDataMap.put("category_type", new VariableData(false, ProductType.PERSONAL_LOAN.name()));
        variableDataMap.put("account_id", new VariableData(false, merchantUser.getMerchantUserId()));
        variableDataMap.put("pan_consent_provided", new VariableData(false, formData.get(consentFormField.getName())));
        variableDataMap.put("request_id", new VariableData(false, requestId));


        return variableDataMap;
    }

    @Override
    public Map<String, Object> constructApplicationData(LoanApplication loanApplication, Map<String, Object> formData, MerchantUser merchantUser, String applicationId) throws DataEnrichmentException {
        UserDetails userDetails = Objects.isNull(loanApplication.getUserDetails()) ? new UserDetails() : loanApplication.getUserDetails();

        FormField panFormField = FormUtils.getFormField(FormFieldContent.PAN_TEXT_BOX);
        FormField dobFormField = FormUtils.getFormField(FormFieldContent.DOB_TYPE);
        FormField genderFormField = FormUtils.getFormField(FormFieldContent.GENDER);
        FormField consentFormField = FormUtils.getFormField(FormFieldContent.CONSENT_PAN_DETAILS);
        FormField cityField = FormUtils.getFormField(FormFieldContent.CITY);
        FormField houseField = FormUtils.getFormField(FormFieldContent.HOUSE_NUMBER);
        FormField areaField = FormUtils.getFormField(FormFieldContent.AREA);
        FormField stateField = FormUtils.getFormField(FormFieldContent.STATE);
        FormField pincodeFormField = FormUtils.getFormField(FormFieldContent.PINCODE);

        Map<String, Object> responseObj = (Map<String, Object>) formData.get(PinakaConstants.PLConstants.ENCRYPTION_DATA);
        EncryptionKeyData encryptionKeyData = EncryptionKeyData.builder()
                .encKey((String) responseObj.get(PinakaConstants.PLConstants.ENC_KEY))
                .encKeyRef((String) responseObj.get(PinakaConstants.PLConstants.ENC_KEY_REF)).build();
        String pan;
        String house=null;
        String city=null;
        String area=null;
        String state=null;
        try {
            pan = securityService.getDecryptedData(encryptionKeyData, (String) formData.get(panFormField.getName()));
            if (!Objects.isNull(formData.get(cityField.getName())) && !formData.get(cityField.getName()).equals("")) {
                city = securityService.getDecryptedData(encryptionKeyData, (String) formData.get(cityField.getName()));
            }
            if (!Objects.isNull(formData.get(stateField.getName())) && !formData.get(stateField.getName()).equals("")) {
                state = securityService.getDecryptedData(encryptionKeyData, (String) formData.get(stateField.getName()));
            }
            if (!Objects.isNull(formData.get(areaField.getName())) && !formData.get(areaField.getName()).equals("")) {
                area = securityService.getDecryptedData(encryptionKeyData, (String) formData.get(areaField.getName()));
            }
            if (!Objects.isNull(formData.get(houseField.getName())) && !formData.get(houseField.getName()).equals("")) {
                house = securityService.getDecryptedData(encryptionKeyData, (String) formData.get(houseField.getName()));
            }
        } catch (PinakaException e) {
            log.error("Error in decrypting the pan {}", formData.get(panFormField.getName()));
            throw new DataEnrichmentException(e);
        }
        userDetails.setPan(EncryptionUtil.encryptWithAes(pan, PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
        userDetails.setDob(EncryptionUtil.encryptWithAes((String) formData.get(dobFormField.getName()), PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
        userDetails.setGender(EncryptionUtil.encryptWithAes((String) formData.get(genderFormField.getName()), PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));

        if (!Objects.isNull(house)) {
            userDetails.setHouseNumber(EncryptionUtil.encryptWithAes(house, PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
        }
        if (!Objects.isNull(area)) {
            userDetails.setArea(EncryptionUtil.encryptWithAes(area, PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
        }
        if (!Objects.isNull(state)) {
            userDetails.setState(EncryptionUtil.encryptWithAes(state, PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
        }
        if (!Objects.isNull(city)) {
            userDetails.setCity(EncryptionUtil.encryptWithAes(city, PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
        }
        if (!Objects.isNull(formData.get(pincodeFormField.getName()))) {
            userDetails.setPincode(formData.get(pincodeFormField.getName()).toString());
        }


        userDetails.setPanConsentProvided((boolean) formData.get(consentFormField.getName()));

        Map<String, Object> consentDetailsObj = (Map<String, Object>) formData.get(PinakaConstants.PLConstants.CONSENT_DETAILS);
        ConsentDetails consentDetails = objectMapper.convertValue(consentDetailsObj, new TypeReference<ConsentDetails>() {
        });
        List<ConsentDetails> consentDetailsList = Objects.isNull(loanApplication.getConsentDetailsList()) ? new ArrayList<>() : loanApplication.getConsentDetailsList();
        consentDetailsList.add(consentDetails);
        loanApplication.setConsentDetailsList(consentDetailsList);

        loanApplication.setUserDetails(userDetails);

        Map<String, Object> applicationDataMap = objectMapper.convertValue(loanApplication, Map.class);

        return applicationDataMap;
    }

    @Override
    public List<DataEnumResponse> constructPageResponse(List<DataEnum> dataEnumList, LoanApplication loanApplication,
                                                        MerchantUser merchantUser, FormType formType) {
        List<DataEnumResponse> dataEnumResponseList = new ArrayList<>();
        for (DataEnum dataEnum : dataEnumList) {
            DataEnumResponse dataEnumResponse = new DataEnumResponse();
            switch (dataEnum) {
                case BANNER:
                    dataEnumResponse = constructBannerWidget(PLUserCohort.NTB);
                    break;
                case FORM:
                    dataEnumResponse = constructFormDataWidget(loanApplication,formType);
                    break;
                case ANNOUNCEMENT:
                    dataEnumResponse = constructAnnouncementWidget();
                    break;
                case HELP:
                    dataEnumResponse = PageConstant.constructHelpWidget();
                    break;
            }
            dataEnumResponseList.add(dataEnumResponse);
        }
        return dataEnumResponseList;
    }

    private BannerDataEnumResponse constructBannerWidget(PLUserCohort userCohort) {
        BannerDataEnumResponse bannerDataEnumResponse =
                BannerDataEnumResponse.builder().
                        dataEnum(DataEnum.BANNER)
                        .url(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.headerBannerUrls.get(userCohort))//this always returns the same banner
                        .build();

        return bannerDataEnumResponse;
    }

    private AnnouncementDataEnumResponse constructAnnouncementWidget() {
        AnnouncementDataEnumResponse announcementDataEnumResponse
                = AnnouncementDataEnumResponse.builder()
                .dataEnum(DataEnum.ANNOUNCEMENT)
                .url(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.announcementImageURL)
                .aspectRatio(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.announcementImageAspectRatio)
                .build();

        return announcementDataEnumResponse;
    }

    private FormDataEnumResponse constructFormDataWidget(LoanApplication loanApplication, FormType formType) {

        FormField headerImageValue = FormUtils.getFormField(FormFieldContent.HEADER_IMAGE_VALUE);
        headerImageValue.setUrl(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.headerImageURL);

        FormField panFormField = null;
//        if (StringUtils.isEmpty(loanApplication.getUserDetails().getPan())) {
            panFormField = FormUtils.getFormField(FormFieldContent.PAN_TEXT_BOX);
            panFormField.setRegex(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.panRegex);
            panFormField.setPlaceholder(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.panPlaceholder);
            if (!StringUtils.isEmpty(loanApplication.getUserDetails().getPan())) {
                panFormField.setDefaultValue(EncryptionUtil.decryptAesCbc(loanApplication.getUserDetails().getPan(), PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
                panFormField.setDisabled(true);
            }

//        }

        FormField dobFormField = null;

        LocalDateTime min = LocalDateTime.now();
        LocalDateTime max = LocalDateTime.now();
        max = max.minusYears(21).minusDays(1);
        min = min.minusYears(60).minusDays(1);

//        if (StringUtils.isEmpty(loanApplication.getUserDetails().getDob())) {
            dobFormField = FormUtils.getFormField(FormFieldContent.DOB_TYPE);
            dobFormField.setMinValue(min.toString());
            dobFormField.setMaxValue(max.toString());
            if (!StringUtils.isEmpty(loanApplication.getUserDetails().getDob())) {
                dobFormField.setDefaultValue(loanApplication.getUserDetails().getDob());
                dobFormField.setDisabled(true);
            }
//        }

        FormField genderFormField = null;

//        if (StringUtils.isEmpty(loanApplication.getUserDetails().getGender())) {
            genderFormField = FormUtils.getFormField(FormFieldContent.GENDER);
            genderFormField.setViewType(ViewType.PILL);
            genderFormField.setOptions(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.genderOptions);
            genderFormField.setDefaultValue("M");
        if (!StringUtils.isEmpty(loanApplication.getUserDetails().getGender())) {
            genderFormField.setDefaultValue(loanApplication.getUserDetails().getGender());
            genderFormField.setDisabled(true);
        }
//        }

        FormField checkBoxFormField = FormUtils.getFormField(FormFieldContent.CONSENT_PAN_DETAILS);
        checkBoxFormField.setTitle(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.consentText);

        FormField submitButtonFormField = FormUtils.getFormField(FormFieldContent.SUBMIT_DETAILS);
        submitButtonFormField.setActionType(Type.CALM_SUBMIT_BUTTON);
        submitButtonFormField.setTitle(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.buttonText);

        if (Status.RETRY_WITHOUT_EDIT.equals(loanApplication.getCode())) {
//            if(!Objects.isNull(panFormField)) {
                panFormField.setDefaultValue(EncryptionUtil.decryptAesCbc(loanApplication.getUserDetails().getPan(), PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
                panFormField.setDisabled(true);
//            }
//            if(!Objects.isNull(dobFormField)) {
                dobFormField.setDefaultValue(EncryptionUtil.decryptAesCbc(loanApplication.getUserDetails().getDob(), PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
                dobFormField.setDisabled(true);
//            }
//            if(!Objects.isNull(genderFormField)) {
                genderFormField.setDefaultValue(EncryptionUtil.decryptAesCbc(loanApplication.getUserDetails().getGender(), PinakaConstants.PLConstants.PL_DATA_ENCRYPTION_KEY));
                genderFormField.setDisabled(true);
//            }
            checkBoxFormField.setDefaultValue("true");
            checkBoxFormField.setDisabled(true);
        }

        List<FormField> formFields = new ArrayList<>();
        formFields.add(headerImageValue);
//        if(!Objects.isNull(panFormField)) {
            formFields.add(panFormField);
//        }
//        if(!Objects.isNull(dobFormField)) {
            formFields.add(dobFormField);
//        }
//        if(!Objects.isNull(genderFormField)) {
            formFields.add(genderFormField);
//        }


        Form form = new Form();
        form.setFormType(FormType.valueOf(formType.name()));


        if (formType.equals(FormType.PL_CUSTOMER_ID_ADDR_FORM)) {
            PinakaMetricRegistry.getMetricRegistry().meter(
                    MetricRegistry.name(PersonalDetailsAdapter.class, "UserInputAddress", "IsEmpty"));

            FormField pincode = FormUtils.getFormField(FormFieldContent.PINCODE);
            pincode.setRegex(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.pincodeRegex);
            pincode.setPlaceholder(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.pincodePlaceholder);
            pincode.setUrl(PageConstant.PincodeExistenceUrl);
            pincode.setActionType(Type.FETCH_DATA);

            FormField houseNumber = FormUtils.getFormField(FormFieldContent.HOUSE_NUMBER);

            houseNumber.setPlaceholder(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.housePlaceholder);

            FormField area = FormUtils.getFormField(FormFieldContent.AREA);
            area.setPlaceholder(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.areaPlaceholder);

            FormField city = FormUtils.getFormField(FormFieldContent.CITY);
            city.setPlaceholder(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.cityPlaceholder);

            FormField state = FormUtils.getFormField(FormFieldContent.STATE);
            state.setPlaceholder(PageConstant.PL_CUSTOMER_IDENTIFICATION_FORM.statePlaceholder);

            formFields.add(pincode);
            formFields.add(houseNumber);
            formFields.add(area);
            formFields.add(city);
            formFields.add(state);
        }

        formFields.add(checkBoxFormField);
        formFields.add(submitButtonFormField);

        form.setFormFields(formFields);

        return FormDataEnumResponse.builder()
                .dataEnum(DataEnum.FORM)
                .form(form)
                .formType(FormType.valueOf(formType.name()))
                .build();
    }

}
