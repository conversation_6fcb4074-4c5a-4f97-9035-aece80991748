package com.flipkart.fintech.pinaka.service.core.v6;

import com.flipkart.fintech.pinaka.api.response.v6.SearchResponse;
import com.flipkart.fintech.pinaka.service.core.v6.impl.EmploymentServiceImpl;
import com.google.inject.ImplementedBy;

@ImplementedBy(EmploymentServiceImpl.class)
public interface EmploymentService {
  SearchResponse getPlEmployerNameSug(String prefix);

    SearchResponse getEmployerNameSug(String prefix);
}
