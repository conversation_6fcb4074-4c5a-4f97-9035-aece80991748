package com.flipkart.fintech.pinaka.service.core.v6.impl;

import com.flipkart.fintech.ardour.api.models.EncryptionKeyData;
import com.flipkart.fintech.pinaka.api.response.SecurityKeyResponse;
import com.flipkart.fintech.pinaka.service.application.SecurityKeyConfiguration;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.utils.EncryptionUtil;
import com.flipkart.fintech.security.aes.AESService;
import com.google.inject.Inject;
import lombok.CustomLog;

import javax.crypto.SecretKey;
import java.util.Base64;
import java.util.Random;


@CustomLog
public class SecurityResourceHandler {

    private SecurityKeyConfiguration securityKeyConfiguration;
    public static String prefix = "keyRef";

    @Inject
    SecurityResourceHandler(SecurityKeyConfiguration securityKeyConfiguration) {
        this.securityKeyConfiguration = securityKeyConfiguration;
    }

    public SecurityKeyResponse getKey() throws PinakaException {
        try {
            int randomIndex = new Random().nextInt(securityKeyConfiguration.getKeys().size());
            String randomKey = prefix + String.valueOf(randomIndex);
            SecurityKeyResponse securityKeyResponse = new SecurityKeyResponse();
            securityKeyResponse.setKeyRef(randomKey);
            securityKeyResponse.setPublicKey(securityKeyConfiguration.getKeys().get(randomKey).getPublicKey());
            return securityKeyResponse;
        } catch (Exception e) {
            throw new PinakaException(e.getMessage(), e);
        }
    }

    public String getDecryptedData(EncryptionKeyData keyData, String encryptedData) throws PinakaException {
        String privateKey = securityKeyConfiguration.getKeys().get(keyData.getEncKeyRef()).getPrivateKey();
        SecretKey secretKey = EncryptionUtil.decryptSymmetricKey(keyData.getEncKey(),privateKey);
        log.info("Secret Key: {}", Base64.getEncoder().encodeToString(secretKey.getEncoded()));
        log.info("Encrypted data: {}", encryptedData);
        String decryptedData;
        try {
            byte[] byteData = AESService.decrypt(secretKey, Base64.getDecoder().decode(encryptedData.getBytes()));
            decryptedData = new String(byteData);
            log.info("Decrypted data: {}", decryptedData);
        } catch (Exception e) {
            throw new PinakaException(e);
        }
        return decryptedData;
    }
}
