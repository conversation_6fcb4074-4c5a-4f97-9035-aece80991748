package com.flipkart.fintech.pinaka.service.factory.v6;

import com.flipkart.fintech.pinaka.api.request.v6.PageName;
import com.flipkart.fintech.pinaka.service.adaptor.v6.IPageAdapter;
import com.flipkart.fintech.pinaka.service.adaptor.v6.OfferDetailsAdapter;
import com.flipkart.fintech.pinaka.service.adaptor.v6.PersonalDetailsAdapter;
import com.flipkart.fintech.pinaka.service.adaptor.v6.ProfessionalDetailsAdapter;
import com.google.common.collect.ImmutableMap;

import javax.inject.Inject;

public class PageAdapterFactory {
    private final ImmutableMap<PageName, IPageAdapter> pageAdapterMap;

    @Inject
    public PageAdapterFactory(PersonalDetailsAdapter personalDetailsAdapter, ProfessionalDetailsAdapter professionalDetailsAdapter, OfferDetailsAdapter offerDetailsAdapter) {
        pageAdapterMap = ImmutableMap.<PageName, IPageAdapter>builder()
                .put(PageName.PL_CUSTOMER_IDENTIFICATION_FORM, personalDetailsAdapter)
                .put(PageName.PL_CUSTOMER_ID_ADDR_FORM, personalDetailsAdapter)
                .put(PageName.PL_ADDITIONAL_DETAILS_FORM, professionalDetailsAdapter)
                .put(PageName.PL_OFFER_SCREEN_FORM, offerDetailsAdapter)
                .build();
    }

    public IPageAdapter getAdapterByPage(String pageName) {
        IPageAdapter iPageAdapter = pageAdapterMap.get(PageName.valueOf(pageName));

        return iPageAdapter;
    }
}
