package com.flipkart.fintech.pinaka.service.core.v6.impl;

import com.flipkart.fintech.pinaka.service.core.v6.UIEventIngestionHandler;
import com.flipkart.fintech.profile.eventPulisher.BQEventPublisher;
import com.flipkart.rome.datatypes.common.events.GenericEvent;
import com.supermoney.schema.PinakaService.UIEvents;
import lombok.CustomLog;
import lombok.SneakyThrows;
import org.codehaus.jackson.JsonNode;
import org.codehaus.jackson.map.ObjectMapper;

import javax.inject.Inject;
import java.io.IOException;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

@CustomLog
public class UIEventIngestionHandlerImpl implements UIEventIngestionHandler {
    private final ObjectMapper objectMapper;
    private final BQEventPublisher bqEventPublisher;

    @Inject
    public UIEventIngestionHandlerImpl(ObjectMapper objectMapper, BQEventPublisher bqEventPublisher) {
        this.objectMapper = objectMapper;
        this.bqEventPublisher = bqEventPublisher;
    }

    @Override
    public void publishBatchEvents(List<GenericEvent> events){
        events.forEach(event -> {
            try {
                bqEventPublisher.publishEventOptional(getEventPayload(event));
            } catch (Exception e) {
                log.error("Error in process event id : {}, {}", event.getEvent_id(), e.getMessage());
                throw new RuntimeException(e);
            }
        });
    }

    @SneakyThrows
    private UIEvents getEventPayload(GenericEvent event) {
        return generateEventPayload(event);
    }

    private UIEvents generateEventPayload(GenericEvent event) throws IOException {
        return UIEvents.newBuilder()
                .setEventId(event.getEvent_id())
                .setEventType(event.getEvent_type())
                .setEventTimestamp(Instant.ofEpochMilli(event.getEvent_timestamp()))
                .setOs(event.getOs())
                .setOsVersion(event.getOs_version())
                .setBrowser(event.getBrowser())
                .setBrowserVersion(event.getBrowser_version())
                .setDeviceType(event.getDevice_type())
                .setScreenResolution(event.getScreen_resolution())
                .setUserAgent(event.getUser_agent())
                .setConnectionType(event.getConnection_type())
                .setLocale(event.getLocale())
                .setTimezone(event.getTimezone())
                .setAccountId(event.getAccount_id())
                .setApplicationId(event.getApplication_id())
                .setLoginStatus(event.getLogin_status())
                .setPageId(event.getPage_id())
                .setPageName(event.getPage_name())
                .setPageType(event.getPage_type())
                .setPreviousPageId(event.getPrevious_page_id())
                .setPreviousPageName(event.getPrevious_page_name())
                .setPreviousPageType(event.getPrevious_page_type())
                .setUtmCampaign(event.getUtm_campaign())
                .setUtmMedium(event.getUtm_medium())
                .setUtmSource(event.getUtm_source())
                .setPathname(event.getPathname())
                .setHost(event.getHost())
                .setHref(event.getHref())
                .setReferrer(event.getReferrer())
                .setReferrerDomain(event.getReferrer_domain())
                .setUrl(event.getUrl())
                .setData(getDynamicJsonFields(event))
                .build();
    }

    public String getDynamicJsonFields(GenericEvent event) throws IOException {
        JsonNode dummyJsonNode = objectMapper.readTree(objectMapper.writeValueAsString(new GenericEvent()));
        Iterator<String> nodeIterator = dummyJsonNode.getFieldNames();
        List<String> excludedFields = new ArrayList<>();
        while (nodeIterator.hasNext()) {
            excludedFields.add(nodeIterator.next());
        }

        JsonNode eventJsonNode = objectMapper.readTree(objectMapper.writeValueAsString(event));
        nodeIterator = eventJsonNode.getFieldNames();
        while (nodeIterator.hasNext()) {
            if(excludedFields.contains(nodeIterator.next())) {
                nodeIterator.remove();
            }
        }

        return objectMapper.writeValueAsString(eventJsonNode);
    }
}

