package com.flipkart.fintech.pinaka.service.utils.FormUtils;

import com.google.i18n.phonenumbers.NumberParseException;
import com.google.i18n.phonenumbers.PhoneNumberUtil;
import com.google.i18n.phonenumbers.Phonenumber;
import org.apache.commons.lang.StringUtils;

public class PhoneNumberValidator {

    private static final PhoneNumberUtil phoneNumberUtil = PhoneNumberUtil.getInstance();
    private static final String INDIA_REGION_CODE = "IN";
    private PhoneNumberValidator() {
    }

    public static boolean validPhoneNumber(String phoneNumber) throws NumberParseException {
        if (StringUtils.isBlank(phoneNumber)) {
            return false;
        }
        Phonenumber.PhoneNumber formattedNumber = null;
        try {
            formattedNumber = phoneNumberUtil.parse(phoneNumber,INDIA_REGION_CODE);
        } catch (NumberParseException e) {
            return false;
        }
        return phoneNumberUtil.isValidNumber(formattedNumber);
    }
}
