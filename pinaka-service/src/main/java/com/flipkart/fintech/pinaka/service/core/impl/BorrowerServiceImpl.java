package com.flipkart.fintech.pinaka.service.core.impl;

import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.service.core.BorrowerService;
import com.flipkart.fintech.pinaka.service.core.MerchantService;
import com.flipkart.fintech.pinaka.service.data.BorrowerEntityDao;
import com.flipkart.fintech.pinaka.service.data.model.BorrowerEntity;
import com.flipkart.fintech.pinaka.service.data.model.MerchantEntity;
import com.google.inject.Inject;
import java.util.List;
import lombok.CustomLog;


/**
 * Created by sujee<PERSON><PERSON>.r on 31/08/17.
 */
@CustomLog
public class BorrowerServiceImpl implements BorrowerService {

  private BorrowerEntityDao borrowerDao;
  private MerchantService merchantService;


  @Inject
  public BorrowerServiceImpl(MerchantService merchantService, BorrowerEntityDao borrowerDao) {
    this.merchantService = merchantService;
    this.borrowerDao = borrowerDao;
  }

  @Override
  public List<BorrowerEntity> getActiveBorrowers(String externalId, String merchantKey,
      List<ProductType> productTypeList) {
    MerchantEntity merchant = merchantService.getMerchantByKey(merchantKey);
    return borrowerDao.getActiveBorrowers(externalId, merchant.getId(), productTypeList);
  }
}
