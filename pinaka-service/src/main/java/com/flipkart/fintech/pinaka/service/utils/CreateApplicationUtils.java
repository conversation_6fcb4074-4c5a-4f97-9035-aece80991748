package com.flipkart.fintech.pinaka.service.utils;

import com.flipkart.fintech.pinaka.service.data.model.BorrowerEntity;
import java.util.Optional;
import lombok.CustomLog;
import org.json.JSONObject;

@CustomLog
public class CreateApplicationUtils {

    private CreateApplicationUtils() {
    }

    public static String getOfferId(Optional<BorrowerEntity> borrowerEntity) {
        try {
            String offer_id = null;
            JSONObject metadataObject = new JSONObject(borrowerEntity.get().getMetadata());
            if (metadataObject.has("offer_id")) {
                offer_id = (String) metadataObject.get("offer_id");
            }
            return offer_id;
        }
        catch (Exception e) {
            log.error("Error getting offer_id for account_id : {},", borrowerEntity.get().getExternalId());
        }
        return null;
    }

}
