package com.flipkart.fintech.pinaka.service.core.page;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.TaskKey;
import com.flipkart.fintech.pinaka.api.response.v6.Action;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.api.response.v6.Params;
import com.flipkart.fintech.pinaka.service.core.actionfactory.ActionFactory;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.ams.ApplicationTypeUtils;
import com.flipkart.fintech.winterfell.api.request.PendingTask;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.AmsBridge;
import com.supermoney.ams.bridge.exceptions.InvalidMerchantException;
import lombok.CustomLog;

import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import javax.inject.Inject;

import static com.flipkart.fintech.pinaka.api.enums.ProductType.PERSONAL_LOAN;

@CustomLog
public class PageActionResponseHandler {
  private final ActionFactory actionFactory;
  private final AmsBridge amsBridge;

  @Inject
  public PageActionResponseHandler(ActionFactory actionFactory, AmsBridge amsBridge) {
    this.actionFactory = actionFactory;
    this.amsBridge = amsBridge;
  }

  public PageActionResponse create(String requestId, MerchantUser merchantUser, ApplicationDataResponse applicationDataResponse) throws PinakaException {
    String applicationType = applicationDataResponse.getApplicationType();
    if(applicationType.equals(ApplicationTypeUtils.getApplicationType(PERSONAL_LOAN, Lender.AXIS.name()))) {

      if ( "REJECTED".equals(applicationDataResponse.getApplicationState()) || ("rejected".equals(applicationDataResponse.getApplicationState()))
            || "RETRY_SCREEN_1".equals(applicationDataResponse.getApplicationState()) || "RETRY_SCREEN_2".equals(applicationDataResponse.getApplicationState())) {
        return createPageActionResponse(merchantUser, applicationDataResponse);
      }
      if( !applicationDataResponse.getPendingTask().isEmpty() ){
        String pendingTaskKey = applicationDataResponse.getPendingTask().get(0).getTaskKey();
        if (Arrays.stream(TaskKey.values()).anyMatch(taskKey -> taskKey.name().equals(pendingTaskKey))) {
          return createPageActionResponse(merchantUser, applicationDataResponse);
        }
      }
      List<PendingTask> taskKeys = applicationDataResponse.getPendingTask();
      if(!taskKeys.isEmpty()){
        log.info("Creating page with axis flow for application state {} and pending task {}", applicationDataResponse.getApplicationState(), taskKeys.get(0).getTaskKey());
      }
      return createForAxis(requestId, merchantUser, applicationDataResponse);
    }
    else{
        return createPageActionResponse(merchantUser, applicationDataResponse);
    }
  }

  private PageActionResponse createForAxis(String requestId, MerchantUser merchantUser,
      ApplicationDataResponse applicationDataResponse) throws PinakaException {
    try {
      Action action = actionFactory.getAction(requestId, merchantUser, applicationDataResponse);
      Params params=new Params();
      params.setApplicationId(applicationDataResponse.getApplicationId());
      return new PageActionResponse(action, true, null, params);
    } catch (URISyntaxException | JsonProcessingException | InvalidMerchantException e) {
      throw new PinakaException(e);
    }
  }


  private PageActionResponse createPageActionResponse(MerchantUser merchantUser,
                                                      ApplicationDataResponse applicationDataResponse) {
    return amsBridge.getPageActionResponse(applicationDataResponse, merchantUser);
  }

}
