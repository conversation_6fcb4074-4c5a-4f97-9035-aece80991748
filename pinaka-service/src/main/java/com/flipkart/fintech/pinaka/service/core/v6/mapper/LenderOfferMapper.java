package com.flipkart.fintech.pinaka.service.core.v6.mapper;

import com.flipkart.fintech.pinaka.api.response.v6.UserOfferDataResponse;
import com.flipkart.fintech.pinaka.service.exception.InvalidOfferDataException;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;

public interface LenderOfferMapper {
    UserOfferDataResponse mapToUserOfferData(ApplicationDataResponse applicationDataResponse) throws InvalidOfferDataException;
}