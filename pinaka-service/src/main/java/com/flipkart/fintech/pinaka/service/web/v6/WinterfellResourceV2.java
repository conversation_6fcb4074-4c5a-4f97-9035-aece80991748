package com.flipkart.fintech.pinaka.service.web.v6;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.api.request.v6.EligibleOfferRequest;
import com.flipkart.fintech.pinaka.api.request.v6.IdentifyCustomerRequest;
import com.flipkart.fintech.pinaka.service.core.v6.WinterfellResourceHandler;
import com.flipkart.fintech.pinaka.service.datacryptography.AxisEncryptor;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.winterfell.api.response.http.WinterfellNodeResponse;
import com.google.inject.Inject;
import io.swagger.annotations.ApiOperation;
import lombok.CustomLog;

import javax.validation.Valid;
import javax.ws.rs.*;
import javax.ws.rs.core.MediaType;

@CustomLog
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("6/pl/v2")
public class WinterfellResourceV2 {
    private final WinterfellResourceHandler winterfellResourceHandler;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Inject
    public WinterfellResourceV2(WinterfellResourceHandler winterfellResourceHandler) {
        this.winterfellResourceHandler = winterfellResourceHandler;
    }

    @POST
    @Timed
    @ExceptionMetered
    @ApiOperation("Customer Identification")
    @Path("/customer-identification")
    public WinterfellNodeResponse identifyCustomer(@Valid IdentifyCustomerRequest identifyCustomerRequest,
                                                   @HeaderParam("X-Merchant-Id") String merchantId) throws PinakaException, JsonProcessingException {
        log.info("Request received for customer identification accountId: {}, applicationId: {}",
                identifyCustomerRequest.getAccountId(), identifyCustomerRequest.getApplicationId());
        IdentifyCustomerRequest axisEncryptedIdentifyCustomerRequest = AxisEncryptor.getAxisEncryptedIdentifyCustomerRequest(identifyCustomerRequest);

        return winterfellResourceHandler.identifyCustomer(axisEncryptedIdentifyCustomerRequest, merchantId);
    }

    @POST
    @Timed
    @ExceptionMetered
    @ApiOperation("Eligible Offer")
    @Path("/eligible-offer")
    public WinterfellNodeResponse eligibleOffer(@Valid EligibleOfferRequest eligibleOfferRequest,
                                                @HeaderParam("X-Merchant-Id") String merchantId,
                                                @HeaderParam("X-Request-Id") String requestId) throws PinakaException, JsonProcessingException {
        log.info("Request received for eligible Offer accountId: {}, applicationId: {}",
                eligibleOfferRequest.getAccountId(), eligibleOfferRequest.getApplicationId());
        EligibleOfferRequest axisEncryptedEligibleOfferRequest = AxisEncryptor.getAxisEncryptedEligibleOfferRequest(eligibleOfferRequest);
        return winterfellResourceHandler.eligibleOffer(axisEncryptedEligibleOfferRequest, merchantId, requestId);
    }

    @POST
    @Timed
    @ExceptionMetered
    @ApiOperation("Get Status")
    @Path("/lender-status/{application_id}")
    public WinterfellNodeResponse getLenderStatus(@PathParam("application_id") String applicationId,
                                                @HeaderParam("X-Merchant-Id") String merchantId,
                                                @HeaderParam("X-Request-Id") String requestId){
        return winterfellResourceHandler.getStatusFromLender(applicationId, requestId, merchantId);
    }

}
