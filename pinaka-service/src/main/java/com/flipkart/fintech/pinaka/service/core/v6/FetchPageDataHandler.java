package com.flipkart.fintech.pinaka.service.core.v6;

import com.flipkart.fintech.pinaka.api.request.v6.PageServiceRequest;
import com.flipkart.fintech.pinaka.api.request.v6.FetchBulkDataRequest;
import com.flipkart.fintech.pinaka.api.response.v6.FetchBulkDataResponse;
import com.flipkart.fintech.pinaka.api.response.v6.FetchBulkDataResponseV2;
import com.flipkart.fintech.pinaka.service.core.v6.impl.FetchPageDataHandlerImpl;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.google.inject.ImplementedBy;
import javax.validation.constraints.NotNull;

@ImplementedBy(FetchPageDataHandlerImpl.class)
public interface FetchPageDataHandler {
    FetchBulkDataResponse fetchBulkData(FetchBulkDataRequest fetchBulkDataRequest, String merchantId) throws PinakaException;
    FetchBulkDataResponseV2 fetchBulkDataV2(@NotNull String merchantId, PageServiceRequest pageServiceRequest)
            throws PinakaException;
}
