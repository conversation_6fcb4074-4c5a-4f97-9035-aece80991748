package com.flipkart.fintech.pinaka.service.core.v6.document;

import com.flipkart.fintech.pinaka.api.request.v4.kyc.UploadDocumentRequest;
import com.flipkart.fintech.pinaka.api.request.v6.documents.UploadDocumentRequestV6;
import com.flipkart.fintech.pinaka.api.response.v6.DocumentDownloadResponse;
import com.flipkart.fintech.pinaka.api.response.v6.DocumentUploadResponse;
import com.flipkart.fintech.winterfell.api.request.VariableData;
import com.google.inject.ImplementedBy;

import java.util.Map;

@ImplementedBy(D42DocumentHelper.class)
public interface DocumentHelper {

    Map<String, VariableData> setKycDocumentUploadRequestData(String applicationId, UploadDocumentRequest uploadDocumentRequest) throws Exception;


    void uploadDocument(String applicationId, UploadDocumentRequest uploadDocumentRequest) throws Exception;

    DocumentUploadResponse uploadDocument(String applicationId, UploadDocumentRequestV6 uploadDocumentRequest) throws Exception;

    DocumentDownloadResponse downloadDocument(String documentId) throws Exception;

}
