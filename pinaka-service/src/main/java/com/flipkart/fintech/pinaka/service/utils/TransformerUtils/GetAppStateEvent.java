package com.flipkart.fintech.pinaka.service.utils.TransformerUtils;

import com.flipkart.fintech.kafka.models.amsEvents.ApplicationEvent;
import com.flipkart.fintech.pinaka.api.model.bigfoot.PlApplication;
import com.flipkart.fintech.pinaka.api.response.v6.ApplicationStatusResponse;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.schema.PinakaService.ApplicationStateEventV1;
import lombok.CustomLog;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@CustomLog
public class GetAppStateEvent {


  public static final Map<String, String> BUSINESS_STATE_MAP;
  public static final Map<String, String> LENDER_BUSINESS_STATE_MAP;
  private static final long veryOldTimestamp=new Date("1/1/2020").getTime();

  static {
    BUSINESS_STATE_MAP = new HashMap<>();
    BUSINESS_STATE_MAP.put("AADHAAR_FORM", "Loan Offer Selection");
    BUSINESS_STATE_MAP.put("AADHAAR_OTP_GENERATION", "Loan Offer Selection");
    BUSINESS_STATE_MAP.put("ADDITIONAL_DETAILS", "Additional Details");
    BUSINESS_STATE_MAP.put("ADDRESS_DETAILS", "Address Details");
    BUSINESS_STATE_MAP.put("AUTO_DISBURSAL", "Disbursal");
    BUSINESS_STATE_MAP.put("BANK_DETAILS", "NACH");
    BUSINESS_STATE_MAP.put("BASIC_DETAILS", "Customer Details");
    BUSINESS_STATE_MAP.put("BASIC_DETAILS_SANDBOX", "Basic Details");
    BUSINESS_STATE_MAP.put("CI_DETAILS", "Customer Details");
    BUSINESS_STATE_MAP.put("CKYC_DOWNLOAD", "Loan Offer");
    BUSINESS_STATE_MAP.put("CUSTOMER_IDENTIFICATION_END", "Customer Details");
    BUSINESS_STATE_MAP.put("CUSTOMER_IDENTIFICATION_START", "Customer Details");
    BUSINESS_STATE_MAP.put("DISBURSAL_STATUS", "Disbursal");
    BUSINESS_STATE_MAP.put("DISBURSED", "Disbursal");
    BUSINESS_STATE_MAP.put("ELIGIBLE_OFFER_END", "Loan Offer");
    BUSINESS_STATE_MAP.put("ELIGIBLE_OFFER_START", "Loan Offer");
    BUSINESS_STATE_MAP.put("EMANDATE_REDIRECTION", "NACH");
    BUSINESS_STATE_MAP.put("EMANDATE_STATUS", "NACH");
    BUSINESS_STATE_MAP.put("E_STAMPING", "Approved");
    BUSINESS_STATE_MAP.put("GENERATE_OTP", "Disbursal");
    BUSINESS_STATE_MAP.put("GET_EKYC", "e-KYC");
    BUSINESS_STATE_MAP.put("KFS", "Disbursal");
    BUSINESS_STATE_MAP.put("KYC", "KYC");
    BUSINESS_STATE_MAP.put("LOAN_ONBOARDING", "Submitted");
    BUSINESS_STATE_MAP.put("NAVIGATOR", "NACH");
    BUSINESS_STATE_MAP.put("OFFER_DETAILS", "Loan Offer Selection");
    BUSINESS_STATE_MAP.put("OFFER_GENERATION", "Loan Offer");
    BUSINESS_STATE_MAP.put("OFFER_SCREEN", "Loan Offer");
    BUSINESS_STATE_MAP.put("OTP_VERIFICATION_SCREEN", "Loan Offer Selection");
    BUSINESS_STATE_MAP.put("PAN_VERIFICATION", "Customer Details");
    BUSINESS_STATE_MAP.put("PENNY_DROP", "NACH");
    BUSINESS_STATE_MAP.put("POLL_OFFER_IDFC", "Loan Offer");
    BUSINESS_STATE_MAP.put("PROCESS_APPLICATION", "Submitted");
    BUSINESS_STATE_MAP.put("REJECTED", "Rejected");
    BUSINESS_STATE_MAP.put("REPAY_MANDATE", "NACH");
    BUSINESS_STATE_MAP.put("SELFIE_CAPTURE", "e-KYC");
    BUSINESS_STATE_MAP.put("SELFIE_DATA_MATCH", "e-KYC");
    BUSINESS_STATE_MAP.put("START_LOAN", "NACH");
    BUSINESS_STATE_MAP.put("SUBMIT_OFFER", "Offer Submitted");
    BUSINESS_STATE_MAP.put("SUBMIT_OFFER_END", "Loan Offer Selection");
    BUSINESS_STATE_MAP.put("SUBMIT_OFFER_START", "Loan Offer Selection");
    BUSINESS_STATE_MAP.put("SUBMIT_OTP", "Disbursal");
    BUSINESS_STATE_MAP.put("VALIDATE_OTP", "Disbursal");
    BUSINESS_STATE_MAP.put("VCIP", "KYC");
    BUSINESS_STATE_MAP.put("WORK_DETAILS", "Work Details");

    LENDER_BUSINESS_STATE_MAP = new HashMap<>();
    LENDER_BUSINESS_STATE_MAP.put("AA_INIT",	"AA_JOURNEY");
    LENDER_BUSINESS_STATE_MAP.put("AA_LSP_REDIRECT_INIT",	"AA_JOURNEY");
    LENDER_BUSINESS_STATE_MAP.put("AA_SUCCESS",	"AA_JOURNEY");
    LENDER_BUSINESS_STATE_MAP.put("ADDITIONAL_DATA_COLLECTION",	"ADDITIONAL_DATA_COLLECTION");
    LENDER_BUSINESS_STATE_MAP.put("CKYC",	"KYC");
    LENDER_BUSINESS_STATE_MAP.put("CKYC_FAILED",	"KYC");
    LENDER_BUSINESS_STATE_MAP.put("CKYC_INIT",	"KYC");
    LENDER_BUSINESS_STATE_MAP.put("CKYC_SUCCESS",	"KYC");
    LENDER_BUSINESS_STATE_MAP.put("DATA_COLLECTION",	"ADDITIONAL_DATA_COLLECTION");
    LENDER_BUSINESS_STATE_MAP.put("DISBURSAL",	"DISBURSAL");
    LENDER_BUSINESS_STATE_MAP.put("DISBURSAL_FAILED",	"DISBURSAL");
    LENDER_BUSINESS_STATE_MAP.put("DISBURSAL_INIT",	"DISBURSAL");
    LENDER_BUSINESS_STATE_MAP.put("DISBURSAL_SUCCESS",	"DISBURSAL");
    LENDER_BUSINESS_STATE_MAP.put("EKYC",	"KYC");
    LENDER_BUSINESS_STATE_MAP.put("EKYC_FAILED",	"KYC");
    LENDER_BUSINESS_STATE_MAP.put("EKYC_INIT",	"KYC");
    LENDER_BUSINESS_STATE_MAP.put("EKYC_SUCCESS",	"KYC");
    LENDER_BUSINESS_STATE_MAP.put("ENACH",	"NACH");
    LENDER_BUSINESS_STATE_MAP.put("ENACH_SETUP",	"NACH");
    LENDER_BUSINESS_STATE_MAP.put("ENACH_SETUP_COMPLETED",	"NACH");
    LENDER_BUSINESS_STATE_MAP.put("ENACH_SETUP_FAILED",	"NACH");
    LENDER_BUSINESS_STATE_MAP.put("ENACH_SETUP_INIT",	"NACH");
    LENDER_BUSINESS_STATE_MAP.put("ENACH_SETUP_PENNY_DROP_COMPLETED",	"NACH");
    LENDER_BUSINESS_STATE_MAP.put("ENACH_SETUP_PENNY_DROP_INIT",	"NACH");
    LENDER_BUSINESS_STATE_MAP.put("INITIAL_OFFER_GENERATION_INIT",	"OFFER_GENERATION");
    LENDER_BUSINESS_STATE_MAP.put("INITIAL_OFFER_GENERATION_SUCCESS",	"OFFER_GENERATION");
    LENDER_BUSINESS_STATE_MAP.put("LOAN_AGREEMENT_SIGN_SUCCESS",	"AA_JOURNEY");
    LENDER_BUSINESS_STATE_MAP.put("LOAN_DISBURSAL",	"DISBURSAL");
    LENDER_BUSINESS_STATE_MAP.put("OFFER_GENERATION",	"OFFER_GENERATION");
    LENDER_BUSINESS_STATE_MAP.put("OFFER_SUBMISSION",	"OFFER_SUBMITTED");
    LENDER_BUSINESS_STATE_MAP.put("OFFER_SUBMISSION_COMPLETED",	"OFFER_SUBMITTED");
    LENDER_BUSINESS_STATE_MAP.put("OKYC",	"KYC");
    LENDER_BUSINESS_STATE_MAP.put("OKYC_INIT",	"KYC");
    LENDER_BUSINESS_STATE_MAP.put("VKYC",	"KYC");
    LENDER_BUSINESS_STATE_MAP.put("VKYC_INIT",	"KYC");
  }

  public static ApplicationStateEventV1 appEventToAppStateEvent(ApplicationEvent applicationEvent) {
    ApplicationStateEventV1 applicationStateEventV1 = new ApplicationStateEventV1();

    Map<String, Object> applicationData = null;
    if (!Objects.isNull(applicationEvent.getApplicationData())) {
      applicationData = (Map<String, Object>) applicationEvent.getApplicationData();
    }

    String eventIdFormat = "%s_%s";
    applicationStateEventV1.setEventId(
        String.format(eventIdFormat, applicationEvent.getApplicationId(),
            applicationEvent.getUpdatedAt().getTime()));
    applicationStateEventV1.setApplicationId(applicationEvent.getApplicationId());
    applicationStateEventV1.setCreatedAt(applicationEvent.getCreatedAt().getTime());
    applicationStateEventV1.setUpdatedAt(applicationEvent.getUpdatedAt().getTime());
    applicationStateEventV1.setMerchant(applicationEvent.getMerchantId());
    applicationStateEventV1.setConsent(new ArrayList<>());
    applicationStateEventV1.setApplicationState(
        BUSINESS_STATE_MAP.containsKey(applicationEvent.getApplicationState()) ?
            BUSINESS_STATE_MAP.get(applicationEvent.getApplicationState()) :
            applicationEvent.getApplicationState()
    );
    applicationStateEventV1.setWorkflowState(applicationEvent.getApplicationState());
    if("DISCARDED".equals(applicationEvent.getCitadelState())){
      applicationStateEventV1.setApplicationState(applicationEvent.getCitadelState());
      applicationStateEventV1.setWorkflowState(applicationEvent.getCitadelState());
    }
    applicationStateEventV1.setSmUserId(applicationEvent.getSmUserId());
    if (!Objects.isNull(applicationData) && applicationData.containsKey("createProfile")) {
      Map<String, Object> createProfile = (Map<String, Object>) applicationData.get(
          "createProfile");
      applicationStateEventV1.setProfileId((Integer) createProfile.get("profile_id"));
    }
    applicationStateEventV1.setProductType(applicationEvent.getApplicationType());
    applicationStateEventV1.setLender(String.valueOf(applicationData.get("financial_provider")));
    if (!"LEAD".equals(applicationStateEventV1.getProductType())) {
      applicationStateEventV1.setLeadId(String.valueOf(applicationData.get("leadId")));
    }
    return applicationStateEventV1;
  }

  public static ApplicationStateEventV1 webhookToAppStateEvent(PlApplication applicationEvent,
      ApplicationDataResponse applicationDataResponse) {
    ApplicationStateEventV1 applicationStateEventV1 = new ApplicationStateEventV1();

    Map<String, Object> applicationData = null;
    if (!Objects.isNull(applicationDataResponse)) {
      applicationData = applicationDataResponse.getApplicationData();
    }

    String eventIdFormat = "%s_%s";
    applicationStateEventV1.setEventId(
        String.format(eventIdFormat, applicationDataResponse.getApplicationId(),
            applicationDataResponse.getUpdatedAt().getTime()));

    applicationStateEventV1.setApplicationId(applicationDataResponse.getApplicationId());
    applicationStateEventV1.setCreatedAt(applicationDataResponse.getCreatedAt().getTime());
    if (applicationEvent.getStateTransitionTime() < veryOldTimestamp) {
      applicationStateEventV1.setUpdatedAt(applicationEvent.getStateTransitionTime()
          * 1000);//convert to millis if timestamp is before 2020 - ie it is in epoch seconds, not millis
    } else {
      applicationStateEventV1.setUpdatedAt(applicationEvent.getStateTransitionTime());
    }
    applicationStateEventV1.setMerchant(applicationDataResponse.getMerchantId());
    applicationStateEventV1.setConsent(new ArrayList<>());
    applicationStateEventV1.setLenderApplicationId(applicationEvent.getLenderApplicationId());
    applicationStateEventV1.setLenderStatus(applicationEvent.getStep());
    applicationStateEventV1.setLenderSubStatus(applicationEvent.getStepStatusDescription());
    applicationStateEventV1.setLender(String.valueOf(applicationData.get("financial_provider")));
    applicationStateEventV1.setProductType(applicationDataResponse.getApplicationType());
    if (!"LEAD".equals(applicationStateEventV1.getProductType())) {
      applicationStateEventV1.setLeadId(String.valueOf(applicationData.get("leadId")));
    }
    applicationStateEventV1.setApplicationState(
        LENDER_BUSINESS_STATE_MAP.containsKey(applicationEvent.getStep()) ?
            LENDER_BUSINESS_STATE_MAP.get(applicationEvent.getStep()) :
            applicationEvent.getApplicationState()
    );
    applicationStateEventV1.setWorkflowState(applicationEvent.getApplicationState());
    if ("AXIS".equals(applicationEvent.getLender()) && Objects.nonNull(
        applicationEvent.getStepStatus())) {
      applicationStateEventV1.setLenderStatus(
          applicationEvent.getStep().concat("_").concat(applicationEvent.getStepStatus()));
    }
    applicationStateEventV1.setSmUserId(applicationDataResponse.getSmUserId());
    if (!Objects.isNull(applicationData) && applicationData.containsKey("createProfile")) {
      Map<String, Object> createProfile = (Map<String, Object>) applicationData.get(
          "createProfile");
      applicationStateEventV1.setProfileId((Integer) createProfile.get("profile_id"));
    }
    return applicationStateEventV1;
  }

  public static ApplicationStateEventV1 getApplicationStatusToStateEvent(ApplicationDataResponse applicationDataResponse, String lender, String lenderApplicationId, ApplicationStatusResponse applicationStatusResponse) {
    try {
      ApplicationStateEventV1 applicationStateEventV1 = new ApplicationStateEventV1();
      String eventIdFormat = "%s_%s";
      Map<String, Object> applicationData = null;
      if (!Objects.isNull(applicationDataResponse.getApplicationData())) {
        applicationData = (Map<String, Object>) applicationDataResponse.getApplicationData();
      }
      applicationStateEventV1.setEventId(String.format(eventIdFormat, applicationDataResponse.getApplicationId(), Instant.now().toEpochMilli()));
      applicationStateEventV1.setApplicationId(applicationDataResponse.getApplicationId());
      applicationStateEventV1.setCreatedAt(applicationDataResponse.getCreatedAt().getTime());
      applicationStateEventV1.setUpdatedAt(applicationDataResponse.getUpdatedAt().getTime());
      applicationStateEventV1.setMerchant(applicationDataResponse.getMerchantId());
      applicationStateEventV1.setConsent(new ArrayList<>());
      applicationStateEventV1.setLender(String.valueOf(lender));
      applicationStateEventV1.setLenderApplicationId(lenderApplicationId);
      applicationStateEventV1.setMerchant(applicationDataResponse.getMerchantId());
      if (!"LEAD".equals(applicationStateEventV1.getProductType())) {
        applicationStateEventV1.setLeadId(String.valueOf(applicationData.get("leadId")));
      }
      applicationStateEventV1.setApplicationState(
              BUSINESS_STATE_MAP.containsKey(applicationDataResponse.getApplicationState()) ?
                      BUSINESS_STATE_MAP.get(applicationDataResponse.getApplicationState()) :
                      applicationDataResponse.getApplicationState()
      );
      applicationStateEventV1.setWorkflowState(applicationDataResponse.getApplicationState());
      applicationStateEventV1.setSmUserId(applicationDataResponse.getSmUserId());
      if (!Objects.isNull(applicationData) && applicationData.containsKey("createProfile")) {
        Map<String, Object> createProfile = (Map<String, Object>) applicationData.get(
                "createProfile");
        applicationStateEventV1.setProfileId((Integer) createProfile.get("profile_id"));
      }
      applicationStateEventV1.setProductType(applicationDataResponse.getApplicationType());
      applicationStateEventV1.setLenderStatus(applicationStatusResponse.getLenderStatus());
      applicationStateEventV1.setLenderSubStatus(applicationStatusResponse.getLenderSubStatus());
      return applicationStateEventV1;
    } catch (Exception e) {
      log.error("Error while getting application state event {}", e.getMessage());
      return null;
    }
  }
}
