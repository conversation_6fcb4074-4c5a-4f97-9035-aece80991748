package com.flipkart.fintech.pinaka.service.core.v6;

import com.flipkart.fintech.pinaka.api.request.v6.EligibleOfferRequest;
import com.flipkart.fintech.pinaka.api.request.v6.IdentifyCustomerRequest;
import com.flipkart.fintech.pinaka.api.request.v6.RejectApplicationRequest;
import com.flipkart.fintech.pinaka.api.request.v6.SubmitOfferRequest;
import com.flipkart.fintech.pinaka.service.core.v6.impl.WinterfellResourceHandlerImpl;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.winterfell.api.response.http.WinterfellNodeResponse;
import com.google.inject.ImplementedBy;
import com.netflix.hystrix.contrib.javanica.annotation.HystrixCommand;

@ImplementedBy(WinterfellResourceHandlerImpl.class)
public interface WinterfellResourceHandler {
    WinterfellNodeResponse identifyCustomer(IdentifyCustomerRequest identifyCustomerRequest, String merchant) throws PinakaException;

    WinterfellNodeResponse eligibleOffer(EligibleOfferRequest eligibleOfferRequest, String merchant, String requestId) throws PinakaException;

    WinterfellNodeResponse getStatusFromLender(String applicationId, String requestId, String merchantId);

    WinterfellNodeResponse submitOffer(SubmitOfferRequest submitOfferRequest, String merchant) throws PinakaException;

    WinterfellNodeResponse rejectApplication(RejectApplicationRequest rejectApplicationRequest, String merchant);

}
