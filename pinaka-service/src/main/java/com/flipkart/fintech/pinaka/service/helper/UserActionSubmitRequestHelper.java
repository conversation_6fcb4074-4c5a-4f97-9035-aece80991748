package com.flipkart.fintech.pinaka.service.helper;

import com.flipkart.fintech.pinaka.api.request.v6.useraction.FormSubmitRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.pinaka.service.datacryptography.FormDataDecryption;
import com.flipkart.fintech.pinaka.service.datacryptography.FormDataEncryption;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

@CustomLog
public class UserActionSubmitRequestHelper {

    public static final String FULL_NAME = "fullName";
    public static final String FIRST_NAME = "firstName";
    public static final String LAST_NAME = "lastName";
    public static final String BUSINESS_NAME = "businessName";
    public static final String ORGANIZATION = "organization";

    public static void processFormDataFields(UserActionRequest submitRequest, FormDataEncryption formDataEncryption, FormDataDecryption formDataDecryption) throws PinakaException {
        adjustFullName(submitRequest, formDataEncryption, formDataDecryption);
        adjustOrganizationName(submitRequest);
        adjustAnnualIncome(submitRequest);
    }

    public static void adjustAnnualIncome(UserActionRequest submitRequest) throws PinakaException {
        FormSubmitRequest formSubmitRequest = (FormSubmitRequest) submitRequest;
        String taskKey = formSubmitRequest.getTaskKey();
        if("leadV3Page1".equals(taskKey) || "leadV3Page2".equals(taskKey)) {
            Map<String, Object> formData = formSubmitRequest.getFormData();
            if(formData.containsKey("income") && formData.getOrDefault("employmentType", "").equals("SelfEmployed")) {
                Object incomeValue = formData.get("income");
                if (incomeValue instanceof String) {
                    String incomeString = (String) incomeValue;
                    if (StringUtils.isNotBlank(incomeString)) {
                        try {
                            Integer v = Integer.parseInt(incomeString);
                            Integer annualIncome = v * 12;
                            formData.put("annualTurnOver", annualIncome);
                        } catch (NumberFormatException e) {
                            log.error("Invalid income value: {}", incomeString, e);
                            throw new PinakaException("Invalid income value provided.", e);
                        }
                    }
                } else {
                    log.error("Income field is not a string: {}", incomeValue);
                    throw new PinakaException("Invalid income value provided. Not a string.");
                }
            }
        }
    }

    private static void adjustFullName(UserActionRequest submitRequest, FormDataEncryption formDataEncryption, FormDataDecryption formDataDecryption) throws PinakaException {
        FormSubmitRequest formSubmitRequest = (FormSubmitRequest) submitRequest;
        Map<String, Object> formData = formSubmitRequest.getFormData();

        if (formData == null || !formData.containsKey(FULL_NAME)) {
            return;
        }

        String fullNameValue = (String) formData.getOrDefault(FULL_NAME, "");
        if (StringUtils.isBlank(fullNameValue)) {
            throw new PinakaException(String.format("Empty name of user, userID: %s", submitRequest.getSmUserId()));
        }

        String fullName = formDataDecryption.getDecryptedPlainTextString(fullNameValue).trim();
        int lastSpaceIndex = fullName.lastIndexOf(' ');

        String firstName, lastName;
        if (lastSpaceIndex == -1) {
            firstName = lastName = fullName;
        } else {
            firstName = fullName.substring(0, lastSpaceIndex);
            lastName = fullName.substring(lastSpaceIndex + 1);
        }

        formData.remove(FULL_NAME);
        formData.put(FIRST_NAME, formDataEncryption.encryptString(firstName));
        formData.put(LAST_NAME, formDataEncryption.encryptString(lastName));
    }

    private static void adjustOrganizationName(UserActionRequest submitRequest) {
        FormSubmitRequest formSubmitRequest = (FormSubmitRequest) submitRequest;
        Map<String, Object> formData = formSubmitRequest.getFormData();
        if (formData == null || !formData.containsKey(BUSINESS_NAME)) {
            return;
        }
        formData.put(ORGANIZATION, formData.get(BUSINESS_NAME));
        formData.remove(BUSINESS_NAME);
    }

}
