package com.flipkart.fintech.pinaka.service.web.v6;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.pinaka.api.response.SecurityKeyResponse;
import com.flipkart.fintech.pinaka.service.core.v6.impl.SecurityResourceHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.exception.PinakaWebAppException;
import io.swagger.annotations.ApiOperation;
import javax.inject.Inject;
import javax.ws.rs.Consumes;
import javax.ws.rs.GET;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import lombok.CustomLog;

@CustomLog
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("6/pl/security")
public class SecurityResourceV6 {

    SecurityResourceHandler securityResourceHandler;

    @Inject
    public SecurityResourceV6(SecurityResourceHandler securityResourceHandler) {
        this.securityResourceHandler = securityResourceHandler;
    }

    @GET
    @ApiOperation(value = "Get Encryption Key", response = SecurityKeyResponse.class)
    @Timed
    @ExceptionMetered
    @Path("/key")
    public SecurityKeyResponse getEncryptionKey() throws PinakaException {
        SecurityKeyResponse response;
        try {
            response = securityResourceHandler.getKey();
        } catch (PinakaException e) {
            throw new PinakaWebAppException(Response.Status.INTERNAL_SERVER_ERROR, e.getMessage());
        }
        return response;
    }
}
