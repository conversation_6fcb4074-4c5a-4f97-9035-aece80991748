package com.flipkart.fintech.pinaka.service.core.v6.document;

import com.flipkart.ams.ApplicationService;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v4.kyc.UploadDocumentRequest;
import com.flipkart.fintech.pinaka.api.request.v6.documents.UploadDocumentRequestV6;
import com.flipkart.fintech.pinaka.api.response.v6.DocumentDownloadResponse;
import com.flipkart.fintech.pinaka.api.response.v6.DocumentUploadResponse;
import com.flipkart.fintech.pinaka.service.core.v6.DocumentService;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import lombok.CustomLog;

import javax.inject.Inject;

@CustomLog
public class DocumentServiceImpl implements DocumentService {
    private final DocumentHelper documentHelper;
    private final ApplicationService applicationService;

    @Inject
    public DocumentServiceImpl(DocumentHelper documentHelper, ApplicationService applicationService) {
        this.documentHelper = documentHelper;
        this.applicationService = applicationService;
    }

    @Override
    public void upload(MerchantUser merchantUser, String applicationRefId, UploadDocumentRequest uploadDocumentRequest) throws PinakaException {
        try {
            documentHelper.uploadDocument(applicationRefId, uploadDocumentRequest);
        } catch (Exception e) {
            log.info("Could not upload xml doc to d42 for application: {}, Storing doc in AMS", applicationRefId);
            throw new PinakaException(String.format("Could not upload xml doc to d42 for application: %s, Storing doc in AMS", applicationRefId), e);
        }
    }

    @Override
    public DocumentUploadResponse upload(MerchantUser merchantUser, String applicationRefId,
                                         UploadDocumentRequestV6 uploadDocumentRequest) throws PinakaException {
        validate(applicationRefId, merchantUser);
        try {
            return documentHelper.uploadDocument(applicationRefId, uploadDocumentRequest);
        } catch (Exception e) {
            log.info("Could not upload xml doc to d42 for application: {}, Storing doc in AMS", applicationRefId);
            throw new PinakaException(String.format("Could not upload xml doc to d42 for application: %s, Storing doc in AMS", applicationRefId), e);
        }
    }

    @Override
    public DocumentDownloadResponse downloadDocument(String documentId) throws Exception {
        return documentHelper.downloadDocument(documentId);
    }

    private void validate(String applicationId, MerchantUser merchantUser) throws PinakaException {
        ApplicationDataResponse applicationDataResponse = applicationService.fetchApplicationData(merchantUser, applicationId);
        if (applicationDataResponse == null)
            throw new PinakaException("Illegal applicationId for the user");
    }
}
