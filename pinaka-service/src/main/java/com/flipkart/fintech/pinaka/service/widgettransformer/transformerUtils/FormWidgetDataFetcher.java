package com.flipkart.fintech.pinaka.service.widgettransformer.transformerUtils;

import com.codahale.metrics.MetricRegistry;
import com.flipkart.fintech.pinaka.api.response.v6.PincodeDetailsResponse;
import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.flipkart.fintech.pinaka.library.entities.CAISHolderAddressDetails;
import com.flipkart.fintech.pinaka.service.application.PinakaMetricRegistry;
import com.flipkart.fintech.pinaka.service.core.v6.LocationRequestHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.response.LeadPageDataSourceResponse;
import com.flipkart.fintech.pinaka.service.response.ReviewUserDataSourceResponse;
import com.flipkart.fintech.pinaka.service.utils.DateUtils;
import com.flipkart.fintech.profile.response.InitialUserDataResponse;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import lombok.CustomLog;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.WordUtils;
import org.jetbrains.annotations.NotNull;

import java.text.ParseException;
import java.util.*;

import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.MetricRegistryConstants.*;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.SPACE;

@CustomLog
public class FormWidgetDataFetcher {

    public static final String EMPTY_STRING = "";
    public static final String PHONE_NUMBER_STRING = "phoneNumber";
    public static final String FIRST_NAME_STRING = "firstName";
    private static final String FULL_NAME_STRING = "fullName";
    public static final String PAN_NUMBER = "panNumber";
    public static final String DOB_STRING = "dob";
    public static final String GENDER_STRING = "gender";
    public static final String GENDER_STRING_2 = "gender_2";
    public static final String COMPANY_NAME_STRING = "companyName";
    public static final String ORGANIZATION_ID_STRING = "organizationId";
    public static final String INDUSTRY_NAME_STRING = "industryName";
    public static final String INDUSTRY_ID_STRING = "industryId";
    public static final String HOUSE_NUMBER_STRING = "houseNumber";
    public static final String AREA_STRING = "area";
    public static final String PINCODE_DETAILS_STRING = "pincodeDetails";
    public static final String MONTHLY_INCOME_STRING = "monthlyIncome";
    public static final String INCOME_SOURCE_STRING = "incomeSource";
    public static final String EMPLOYMENT_TYPE_STRING = "employmentType";
    public static final String EMAIL_STRING = "email";
    public static final String EMPTY_PINCODE = "0";
    public static final String FEMALE_GENDER_STRING = "Female";
    public static final String OTHERS_GENDER_STRING = "Others";
    public static final String MALE_GENDER_STRING = "Male";
    public static final String ADDRESSES_STRING = "addresses";
    public static final String BONUS_INCOME_STRING = "bonusIncome";

    public Map<String, Object> getDataForFields(Set<String> fields,
                                                LeadPageDataSourceResponse leadPageDataSourceResponse,
                                                ReviewUserDataSourceResponse reviewUserDataSourceResponse,
                                                Decrypter decrypter,
                                                LocationRequestHandler locationRequestHandler) {
        Map<String, Object> userData = new HashMap<>();
        // leadPageDataSourceResponse
        if (fields.contains(FIRST_NAME_STRING)) {
            userData.put(FIRST_NAME_STRING, getFirstName(leadPageDataSourceResponse, decrypter));
        }
        else if (fields.contains(FULL_NAME_STRING)) {
            userData.put(FULL_NAME_STRING, getFullName(leadPageDataSourceResponse, decrypter));
            userData.put(PHONE_NUMBER_STRING, getPhoneNumber(leadPageDataSourceResponse));
        } else {
            // ReviewUserDataSourceResponse
            userData.put(PAN_NUMBER, getPanNumber(reviewUserDataSourceResponse, decrypter));
            userData.put(DOB_STRING, getDateOfBirth(reviewUserDataSourceResponse, decrypter));
            userData.put(GENDER_STRING, getGenderString(reviewUserDataSourceResponse));
            userData.put(GENDER_STRING_2, getGenderString2(reviewUserDataSourceResponse));
            userData.put(COMPANY_NAME_STRING, getCompanyName(reviewUserDataSourceResponse));
            userData.put(ORGANIZATION_ID_STRING, getOrganizationId(reviewUserDataSourceResponse));
            userData.put(INDUSTRY_NAME_STRING, getIndustryName(reviewUserDataSourceResponse));
            userData.put(INDUSTRY_ID_STRING, getIndustryId(reviewUserDataSourceResponse));
            userData.put(HOUSE_NUMBER_STRING, getAddressLineOne(reviewUserDataSourceResponse, decrypter));
            userData.put(AREA_STRING, getAddressLineTwo(reviewUserDataSourceResponse, decrypter));
            userData.put(ADDRESSES_STRING, getAvailableAddresses(reviewUserDataSourceResponse));
            userData.put(PINCODE_DETAILS_STRING, getPincode(reviewUserDataSourceResponse, locationRequestHandler));
            userData.put(MONTHLY_INCOME_STRING, getMonthlyIncome(reviewUserDataSourceResponse));
            userData.put(BONUS_INCOME_STRING, getBonusIncome(reviewUserDataSourceResponse));
            userData.put(INCOME_SOURCE_STRING, getIncomeSource(reviewUserDataSourceResponse));
            userData.put(EMPLOYMENT_TYPE_STRING, getEmploymentType(reviewUserDataSourceResponse));
            userData.put(EMAIL_STRING, getEmail(reviewUserDataSourceResponse, decrypter));
        }
        return userData;
    }

    Integer getBonusIncome(ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        if (reviewUserDataSourceResponse.getProfile() != null && reviewUserDataSourceResponse.getProfile().getBonusIncome() != null) {
            return reviewUserDataSourceResponse.getProfile().getBonusIncome();
        }
        return 0;
    }

    String getPhoneNumber(LeadPageDataSourceResponse leadPageDataSourceResponse) {
        if (leadPageDataSourceResponse.getProfile() != null) {
            ProfileDetailedResponse profileDetailedResponse = leadPageDataSourceResponse.getProfile();
            return profileDetailedResponse.getPhoneNo();
        }
        return EMPTY_STRING;
    }

    @NotNull
    private Object getFirstName(LeadPageDataSourceResponse leadPageDataSourceResponse, Decrypter decrypter) {
        ProfileDetailedResponse profile = leadPageDataSourceResponse.getProfile();
        if (Boolean.TRUE.equals(leadPageDataSourceResponse.getIsNameEncrypted()) && StringUtils.isNotEmpty(profile.getFirstName())) {
            profile.setFirstName(decrypter.decryptString(profile.getFirstName()));
        }
        String firstName = profile.getFirstName();
        if (StringUtils.isBlank(firstName)) {
            return "";
        }
        String normalized = StringUtils.normalizeSpace(firstName);
        String[] tokens = normalized.split(SPACE);
        if (tokens.length == 0) {
            return "";
        }
        return WordUtils.capitalizeFully(tokens[0]);
    }

    @NotNull
    String getFullName(LeadPageDataSourceResponse leadPageDataSourceResponse, Decrypter decrypter) {
        ProfileDetailedResponse profile = leadPageDataSourceResponse.getProfile();
        if (leadPageDataSourceResponse.getIsNameEncrypted() && StringUtils.isNotEmpty(profile.getFirstName()) && StringUtils.isNotEmpty(profile.getLastName())) {
            profile.setFirstName(decrypter.decryptString(profile.getFirstName()));
            profile.setLastName(decrypter.decryptString(profile.getLastName()));
        }
        String firstName = StringUtils.trimToEmpty(profile.getFirstName());
        String lastName = StringUtils.trimToEmpty(profile.getLastName());
        return String.join(" ", firstName, lastName).trim();
    }

    String getPanNumber(ReviewUserDataSourceResponse reviewUserDataSourceResponse, Decrypter decrypter) {
        String panNumber = "";
        InitialUserDataResponse userDataResponse = reviewUserDataSourceResponse.getInitialUserDataResponse();
        if (reviewUserDataSourceResponse.getProfile() != null && reviewUserDataSourceResponse.getProfile().getPan() != null) {
            panNumber = decrypter.decryptString(reviewUserDataSourceResponse.getProfile().getPan());
            PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(this.getClass(), PAN_PREFILL_FROM_PROFILE)).mark();
        } else if (userDataResponse != null) {
            panNumber = userDataResponse.getPanNumber();
        }
        return panNumber;
    }

    String getDateOfBirth(ReviewUserDataSourceResponse reviewUserDataSourceResponse, Decrypter decrypter) {
        ProfileDetailedResponse profileDetailedResponse = reviewUserDataSourceResponse.getProfile();
        InitialUserDataResponse userDataResponse = reviewUserDataSourceResponse.getInitialUserDataResponse();
        String dob = "";
        if (profileDetailedResponse != null && StringUtils.isNotBlank(profileDetailedResponse.getDob())) {
            String dobString = decrypter.decryptString(profileDetailedResponse.getDob());
            try {
                dob = DateUtils.convertDateFormat(dobString, DateUtils.getSimpleDateFormat9, DateUtils.getSimpleDateFormat9);
            } catch (ParseException e) {
                log.error("Date Format from Profile is Wrong {}", dobString);
            }
        } else if (userDataResponse != null && userDataResponse.getDateOfBirth() != null) {
            String experianDob = userDataResponse.getDateOfBirth();
            if (StringUtils.isNotBlank(experianDob)) {
                try {
                    dob = DateUtils.convertDateFormat(experianDob, DateUtils.getSimpleDateFormat9, DateUtils.getSimpleDateFormat9);
                } catch (ParseException e) {
                    log.error("Invalid Dob Format in Experian for smUserId {}",
                            Optional.ofNullable(reviewUserDataSourceResponse.getProfile()).map(p -> " for smUserId " + p.getSmUserId()).orElse("Null profile received"));
                }
            }
        }
        return dob;
    }

    String getGenderString(ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        if (reviewUserDataSourceResponse.getProfile() != null) {
            ProfileDetailedResponse profileDetailedResponse = reviewUserDataSourceResponse.getProfile();
            return getGenderStringFromProfileInitial(profileDetailedResponse.getGender());
        }
        return MALE_GENDER_STRING;
    }

    String getGenderString2(ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        if (reviewUserDataSourceResponse.getInitialUserDataResponse() != null) {
            InitialUserDataResponse userDataResponse = reviewUserDataSourceResponse.getInitialUserDataResponse();
            return getGenderStringFromUserDataResponse(userDataResponse.getGender());
        }
        return MALE_GENDER_STRING;
    }

    String getGenderStringFromProfileInitial(String genderInitial) {
        if (StringUtils.isNotBlank(genderInitial)) {
            switch (genderInitial) {
                case "F":
                    return FEMALE_GENDER_STRING;
                case "O":
                    return OTHERS_GENDER_STRING;
                default:
                    return MALE_GENDER_STRING;
            }
        }
        return MALE_GENDER_STRING;
    }

    String getGenderStringFromUserDataResponse(String genderInitial) {
        if (StringUtils.isNotBlank(genderInitial)) {
            switch (genderInitial) {
                case "2":
                    return FEMALE_GENDER_STRING;
                case "3":
                    return OTHERS_GENDER_STRING;
                default:
                    return MALE_GENDER_STRING;
            }
        }
        return MALE_GENDER_STRING;
    }

    String getCompanyName(ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        if (reviewUserDataSourceResponse.getProfile() != null) {
            ProfileDetailedResponse profileDetailedResponse = reviewUserDataSourceResponse.getProfile();
            return profileDetailedResponse.getCompanyName();
        }
        return EMPTY_STRING;
    }

    String getOrganizationId(ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        if (reviewUserDataSourceResponse.getProfile() != null) {
            return reviewUserDataSourceResponse.getProfile().getOrganizationId();
        }
        return EMPTY_STRING;
    }

    String getIndustryName(ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        if (reviewUserDataSourceResponse.getProfile() != null) {
            ProfileDetailedResponse profileDetailedResponse = reviewUserDataSourceResponse.getProfile();
            return profileDetailedResponse.getIndustryType();
        }
        return EMPTY_STRING;
    }

    String getIndustryId(ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        if (reviewUserDataSourceResponse.getProfile() != null) {
            return reviewUserDataSourceResponse.getProfile().getIndustryId();
        }
        return EMPTY_STRING;
    }

    String getAddressLineOne(ReviewUserDataSourceResponse reviewUserDataSourceResponse, Decrypter decrypter) {
        ProfileDetailedResponse profileDetailedResponse = reviewUserDataSourceResponse.getProfile();
        InitialUserDataResponse userDataResponse = reviewUserDataSourceResponse.getInitialUserDataResponse();
        String addressLine1 = "";
        if (profileDetailedResponse != null && profileDetailedResponse.getAddressLine1() != null) {
            addressLine1 = decrypter.decryptString(profileDetailedResponse.getAddressLine1());
            PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(this.getClass(), ADDRESS_LINE_1_PREFILL_FROM_PROFILE)).mark();
        } else if (userDataResponse != null && userDataResponse.getAddressDetailResponse() != null) {
            addressLine1 = userDataResponse.getAddressDetailResponse().getAddressLine1();
        }
        return addressLine1;
    }

    String getAddressLineTwo(ReviewUserDataSourceResponse reviewUserDataSourceResponse, Decrypter decrypter) {
        ProfileDetailedResponse profileDetailedResponse = reviewUserDataSourceResponse.getProfile();
        InitialUserDataResponse userDataResponse = reviewUserDataSourceResponse.getInitialUserDataResponse();
        String addressLine2 = "";
        if (profileDetailedResponse != null && profileDetailedResponse.getAddressLine2() != null) {
            addressLine2 = decrypter.decryptString(profileDetailedResponse.getAddressLine2());
            PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(this.getClass(), ADDRESS_LINE_2_PREFILL_FROM_PROFILE)).mark();
        } else if (userDataResponse != null && userDataResponse.getAddressDetailResponse() != null) {
            addressLine2 = userDataResponse.getAddressDetailResponse().getAddressLine2();
        }
        return addressLine2;
    }

    List<CAISHolderAddressDetails> getAvailableAddresses(ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        InitialUserDataResponse initialUserDataResponse = reviewUserDataSourceResponse.getInitialUserDataResponse();
        if (initialUserDataResponse != null && initialUserDataResponse.getExperianAddressDetails() != null) {
            return initialUserDataResponse.getExperianAddressDetails();
        }
        return Collections.emptyList();
    }

    PincodeDetailsResponse getPincode(ReviewUserDataSourceResponse reviewUserDataSourceResponse, LocationRequestHandler locationRequestHandler) {
        ProfileDetailedResponse profileDetailedResponse = reviewUserDataSourceResponse.getProfile();
        InitialUserDataResponse userDataResponse = reviewUserDataSourceResponse.getInitialUserDataResponse();
        String pincode = EMPTY_PINCODE;
        if (profileDetailedResponse != null && profileDetailedResponse.getUserEnteredPincode() != null) {
            pincode = String.valueOf(profileDetailedResponse.getUserEnteredPincode());
            PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(this.getClass(), PINCODE_PREFILL_FROM_PROFILE)).mark();
        } else if (userDataResponse != null && userDataResponse.getAddressDetailResponse() != null) {
            pincode = userDataResponse.getAddressDetailResponse().getPincode();
        }
        PincodeDetailsResponse pincodeDetailsResponse = new PincodeDetailsResponse();
        if (!EMPTY_PINCODE.equals(pincode)) {
            try {
                return locationRequestHandler.checkPincodeExistence(pincode);
            } catch (PinakaException e) {
                log.error("FormWidgetDataFetcher Error while fetching pincode details {}", e);
            }
            pincodeDetailsResponse.setPincode(pincode);
        }
        return pincodeDetailsResponse;
    }

    String getMonthlyIncome(ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        ProfileDetailedResponse profileDetailedResponse = reviewUserDataSourceResponse.getProfile();
        if (profileDetailedResponse != null && profileDetailedResponse.getMonthlyIncome() != null) {
            return Integer.toString(profileDetailedResponse.getMonthlyIncome());
        }
        return EMPTY_STRING;
    }

    String getIncomeSource(ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        if (reviewUserDataSourceResponse.getProfile() != null && reviewUserDataSourceResponse.getProfile().getIncomeSource() != null) {
            return String.valueOf(reviewUserDataSourceResponse.getProfile().getIncomeSource());
        }
        return EMPTY_STRING;
    }

    String getEmploymentType(ReviewUserDataSourceResponse reviewUserDataSourceResponse) {
        if (reviewUserDataSourceResponse.getProfile() != null && reviewUserDataSourceResponse.getProfile().getEmploymentType() != null) {
            return String.valueOf(reviewUserDataSourceResponse.getProfile().getEmploymentType());
        }
        return EMPTY_STRING;
    }

    String getEmail(ReviewUserDataSourceResponse reviewUserDataSourceResponse, Decrypter decrypter) {
        ProfileDetailedResponse profileDetailedResponse = reviewUserDataSourceResponse.getProfile();
        InitialUserDataResponse userDataResponse = reviewUserDataSourceResponse.getInitialUserDataResponse();
        String email = "";
        if (profileDetailedResponse != null && profileDetailedResponse.getEmail() != null) {
            email = decrypter.decryptString(profileDetailedResponse.getEmail());
            PinakaMetricRegistry.getMetricRegistry().meter(MetricRegistry.name(this.getClass(), EMAIL_PREFILL_FROM_PROFILE)).mark();
        } else if (userDataResponse != null) {
            email = userDataResponse.getEmail();
        }
        return email;
    }
}
