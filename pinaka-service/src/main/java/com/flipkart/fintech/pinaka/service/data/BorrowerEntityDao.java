package com.flipkart.fintech.pinaka.service.data;

import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.request.GetBorrowerCountRequest;
import com.flipkart.fintech.pinaka.service.data.model.BorrowerEntity;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 31/08/17.
 */
public interface BorrowerEntityDao {

    BorrowerEntity saveOrUpdate(BorrowerEntity entity);

    List<BorrowerEntity> getByExternalId(String externalId);

    List<BorrowerEntity> getByExternalIdAndMerchantId(String externalId, Long merchantId);

    List<BorrowerEntity> getAllBorrowersByExternalIdAndMerchantId(String externalId, Long merchantId);

    BorrowerEntity getActiveBorrower(String externalId, Long merchantId, ProductType productType);

    List<BorrowerEntity> getAllBorrowers(String externalId, Long merchantId, List<ProductType> productTypeList);

    List<BorrowerEntity> getActiveBorrowers(String externalId, Long merchantId, List<ProductType> productTypeList);

    BorrowerEntity getById(Long id);

    BorrowerEntity getByExternalIdAndWhitelistId(String externalId, Long whitelistId);

    List<BorrowerEntity> getBorrowersForWhitelistId(Long whitelistId);

    List<BorrowerEntity> getAllBorrowers(String userId, Long merchantId);

    Long getBorrowerCount(GetBorrowerCountRequest getBorrowerCountRequest);

    BorrowerEntity getActiveBorrower(String externalId, Long merchantId, ProductType productType, String lender);
}
