package com.flipkart.fintech.pinaka.service.core.v6.document;

import com.amazonaws.services.s3.model.GetObjectRequest;
import com.flipkart.fintech.pinaka.api.response.v6.DocumentDownloadResponse;
import com.google.inject.ImplementedBy;
import java.util.Map;

@ImplementedBy(D42ObjectStore.class)
public interface ObjectStore {

  void upload(String bucketName, String documentKey,
      byte[] content, Map<String, String> d42MetaData) throws Exception;

    DocumentDownloadResponse getDocument(GetObjectRequest getObjectRequest) throws Exception;

}
