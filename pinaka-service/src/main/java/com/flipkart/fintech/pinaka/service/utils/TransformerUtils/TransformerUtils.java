package com.flipkart.fintech.pinaka.service.utils.TransformerUtils;

import lombok.CustomLog;
import org.apache.commons.io.IOUtils;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;


@CustomLog
public class TransformerUtils {

    private TransformerUtils() {
    }

    public static String readFileasString(String fileName) {

        try (InputStream inputStream = TransformerUtils.class.getClassLoader().getResourceAsStream(fileName)) {
            return IOUtils.toString(inputStream, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("Widget Template file reading failed  with error : {}", e.getMessage());
            throw new RuntimeException(e);
        }
    }
}
