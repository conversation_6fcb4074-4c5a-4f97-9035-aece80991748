package com.flipkart.fintech.pinaka.service.arsenal.selector;

import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.data.model.BorrowerEntity;
import com.google.inject.ImplementedBy;
import java.util.List;
import java.util.Optional;

@ImplementedBy(PriorityBasedBorrowerSelector.class)
public interface BorrowerSelector {
  Optional<BorrowerEntity> select(MerchantUser merchantUser, List<BorrowerEntity> list);
}
