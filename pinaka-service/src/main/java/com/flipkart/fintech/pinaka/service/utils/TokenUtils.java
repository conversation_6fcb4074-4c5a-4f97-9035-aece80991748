package com.flipkart.fintech.pinaka.service.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.api.request.v6.Token;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;

public class TokenUtils {
  private static final ObjectMapper objectMapper = new ObjectMapper();

  public static String getEncryptedToken(Token token) throws JsonProcessingException {
    return EncryptionUtil.encryptWithAes(objectMapper.writeValueAsString(token), PinakaConstants.PLConstants.PL_ENCRYPTION_KEY);
  }

  public static String getUriEncodedToken(Token token) throws JsonProcessingException, UnsupportedEncodingException {
    return URLEncoder.encode(getEncryptedToken(token), "UTF-8");
  }
}
