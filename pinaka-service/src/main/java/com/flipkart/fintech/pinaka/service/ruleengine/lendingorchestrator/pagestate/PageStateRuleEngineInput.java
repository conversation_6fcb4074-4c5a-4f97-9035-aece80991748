package com.flipkart.fintech.pinaka.service.ruleengine.lendingorchestrator.pagestate;

import com.flipkart.fintech.lending.orchestrator.model.LendingPageRequest;
import com.flipkart.fintech.offer.orchestrator.model.PreApprovedOfferDetails;
import com.flipkart.fintech.pinaka.service.ruleengine.RuleEngineInput;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class PageStateRuleEngineInput implements RuleEngineInput {
  private ApplicationDataResponse plApplicationDataResponse;
  private ApplicationDataResponse leadApplicationDataResponse;
  private LendingPageRequest lendingPageRequest;
  private PreApprovedOfferDetails preApprovedOfferDetails;
}
