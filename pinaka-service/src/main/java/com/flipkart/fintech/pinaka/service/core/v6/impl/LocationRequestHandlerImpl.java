package com.flipkart.fintech.pinaka.service.core.v6.impl;

import com.flipkart.affordability.model.response.AddressDetailResponse;
import com.flipkart.affordability.underwriting.model.dexter.FetchUserProfileResponse;
import com.flipkart.cri.alfred.api.response.v3.UserProfileResponseV3;
import com.flipkart.fintech.pandora.client.UserClient;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.response.v6.*;
import com.flipkart.fintech.pinaka.common.constants.DexterConstants;
import com.flipkart.fintech.pinaka.common.userprofilescores.UserProfileScores;
import com.flipkart.fintech.pinaka.common.utils.UserProfileInsightsUtils;
import com.flipkart.fintech.pinaka.service.core.v6.LocationRequestHandler;
import com.flipkart.fintech.pinaka.service.core.v7.CreateApplicationRequestFactory;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.external.ElasticSearchClient;
import com.flipkart.kloud.config.DynamicBucket;
import lombok.CustomLog;

import javax.inject.Inject;
import java.util.*;

import static com.flipkart.fintech.pinaka.service.application.Constants.*;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.CompanyMasterData.PL_PINCODE_INDEX_NAME_KEY;

@CustomLog
public class LocationRequestHandlerImpl implements LocationRequestHandler {
    private final ElasticSearchClient elasticSearchClient;

    private final CreateApplicationRequestFactory applicationRequestFactory;

    private final UserProfileScores userProfileScores;

    private final DynamicBucket dynamicBucket;

    private final UserClient userClient;

    @Inject
    public LocationRequestHandlerImpl(ElasticSearchClient elasticSearchClient,
                                      CreateApplicationRequestFactory applicationRequestFactory,
                                      UserProfileScores userProfileScores,
                                      UserClient userClient,
                                      DynamicBucket dynamicBucket) {
        this.elasticSearchClient = elasticSearchClient;
        this.applicationRequestFactory = applicationRequestFactory;
        this.userProfileScores = userProfileScores;
        this.userClient = userClient;
        this.dynamicBucket = dynamicBucket;
    }

    @Override
    public PincodeDetailsResponse checkPincodeExistence(String pincode) throws PinakaException {
        try {
            Map<String, Object> response = elasticSearchClient.fetchDocument(pincode, Optional.ofNullable(dynamicBucket.getString(PL_PINCODE_INDEX_NAME_KEY)).orElse(PINCODE_INDEX), ID);
            if(Objects.isNull(response)){
                return PincodeDetailsResponse.builder().pincode(pincode).isValid(false).build();
            }
            return PincodeDetailsResponse.builder().pincode(pincode)
                    .city((String) response.get("city"))
                    .state((String) response.get("state"))
                    .isValid(true).build();
        }
        catch (Exception ex) {
            log.error("Error while calling elastic search for pincode {}: {}",pincode, ex.getMessage());
            throw new PinakaException("Error while calling elastic search for pincode check");
        }
    }

    @Override
    public AddressDetailResponse getUserAddress(String userId, String merchantId, String smUserId, String requestId) throws PinakaException {
        try {
            MerchantUser merchantUser = MerchantUser.getMerchantUser(merchantId, userId, smUserId);
            UserProfileResponseV3 userProfileResponse = new UserProfileResponseV3();
            FetchUserProfileResponse fetchUserProfileResponse = new FetchUserProfileResponse();

            Boolean dexterFlag = dynamicBucket.getBoolean(DexterConstants.DEXTER_FLAG);
            if(dexterFlag){
                fetchUserProfileResponse = userProfileScores.getUserProfileByDexter(requestId, merchantUser);
            }else {
                userProfileResponse = userProfileScores.getUserProfile(merchantUser);
            }
            String shippingAddressId = UserProfileInsightsUtils.getShippingAddressId(userProfileResponse, fetchUserProfileResponse, dynamicBucket);
            return userClient.getAddressDetailResponse(shippingAddressId, merchantUser.getMerchantUserId(), null, null);
        } catch (Exception ex) {
            log.error("Error while fetching addr for user_id {}: {}", userId, ex.getMessage());
            return new AddressDetailResponse();
        }
    }
}
