package com.flipkart.fintech.pinaka.service.data.impl;

import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.request.GetBorrowerCountRequest;
import com.flipkart.fintech.pinaka.service.data.BorrowerEntityDao;
import com.flipkart.fintech.pinaka.service.data.model.BorrowerEntity;
import com.flipkart.fintech.pinaka.service.data.model.WhitelistEntity;
import com.google.inject.Inject;
import io.dropwizard.hibernate.AbstractDAO;
import org.hibernate.Criteria;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Restrictions;
import org.hibernate.query.Query;

import javax.persistence.criteria.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Created by sujee<PERSON><PERSON>.r on 31/08/17.
 */
public class BorrowerEntityDaoImpl extends AbstractDAO<BorrowerEntity> implements BorrowerEntityDao {

    private static final String EXTERNALID = "externalId";
    private static final String ENABLED = "enabled";
    private static final String WHITELIST = "whitelist";
    private static final String PRODUCT_TYPE = "productType";
    private static final String MERCHANT = "merchant";

    @Inject
    public BorrowerEntityDaoImpl(SessionFactory sessionFactory) {
        super(sessionFactory);
    }


    @Override
    public BorrowerEntity saveOrUpdate(BorrowerEntity entity) {
        return persist(entity);
    }

    @Override
    public List<BorrowerEntity> getByExternalId(String externalId) {
        Criteria criteria = criteria().add(Restrictions.eq(EXTERNALID, externalId));
        return list(criteria);
    }

    @Override
    public List<BorrowerEntity> getByExternalIdAndMerchantId(String externalId, Long merchantId) {
        Criteria criteria = criteria().add(Restrictions.eq(EXTERNALID, externalId))
                .add(Restrictions.eq(ENABLED, true));
        criteria.createCriteria(WHITELIST).add(Restrictions.eq(ENABLED, true))
                .createAlias(MERCHANT, "m")
                .add(Restrictions.eq("m.id", merchantId));
        return list(criteria);
    }

    @Override
    public List<BorrowerEntity> getAllBorrowersByExternalIdAndMerchantId(String externalId, Long merchantId) {
        Criteria criteria = criteria().add(Restrictions.eq(EXTERNALID, externalId));
        criteria.createCriteria(WHITELIST)
                .createAlias(MERCHANT, "m")
                .add(Restrictions.eq("m.id", merchantId));
        return list(criteria);
    }


    @Override
    @Timed
    public BorrowerEntity getActiveBorrower(String externalId, Long merchantId, ProductType productType) {
        Criteria criteria = criteria().add(Restrictions.eq(EXTERNALID, externalId))
                                      .add(Restrictions.eq(ENABLED, true));
        criteria.createCriteria(WHITELIST).add(Restrictions.eq(ENABLED, true))
                                            .add(Restrictions.eq(PRODUCT_TYPE, productType))
                                            .createAlias(MERCHANT, "m")
                                            .add(Restrictions.eq("m.id", merchantId));
        return list(criteria).isEmpty() ? null : list(criteria).get(0);
    }

    @Override
    public List<BorrowerEntity> getAllBorrowers(String externalId, Long merchantId, List<ProductType> productTypeList) {
        Criteria criteria = criteria().add(Restrictions.eq(EXTERNALID, externalId));
        criteria.createCriteria(WHITELIST)
                .add(Restrictions.in(PRODUCT_TYPE, productTypeList))
                .createAlias(MERCHANT, "m")
                .add(Restrictions.eq("m.id", merchantId));
        return list(criteria);
    }

    @Override
    public List<BorrowerEntity> getActiveBorrowers(String externalId, Long merchantId, List<ProductType> productTypeList) {
        Criteria criteria = criteria().add(Restrictions.eq(EXTERNALID, externalId))
                .add(Restrictions.eq(ENABLED, true));
        criteria.createCriteria(WHITELIST).add(Restrictions.eq(ENABLED, true))
                .add(Restrictions.in(PRODUCT_TYPE, productTypeList))
                .createAlias(MERCHANT, "m")
                .add(Restrictions.eq("m.id", merchantId));
        return list(criteria);
    }

    @Override
    public BorrowerEntity getById(Long id) {
        return get(id);
    }

    @Override
    public BorrowerEntity getByExternalIdAndWhitelistId(String externalId, Long whitelistId) {
        Criteria criteria = criteria().add(Restrictions.eq(EXTERNALID, externalId))
                .add(Restrictions.eq("whitelist.id", whitelistId));
        return uniqueResult(criteria);
    }

    @Override
    public List<BorrowerEntity> getBorrowersForWhitelistId(Long whitelistId) {
        Criteria criteria = criteria().add(Restrictions.eq("whitelist.id", whitelistId));
        return list(criteria);
    }

    @Override
    public List<BorrowerEntity> getAllBorrowers(String userId, Long merchantId) {
        Criteria criteria = criteria().add(Restrictions.eq(EXTERNALID, userId));
        criteria.createCriteria(WHITELIST)
                .createAlias(MERCHANT, "m")
                .add(Restrictions.eq("m.id", merchantId));
        return list(criteria);
    }

    @Override
    public Long getBorrowerCount(GetBorrowerCountRequest getBorrowerCountRequest) {
        CriteriaBuilder cb = currentSession().getCriteriaBuilder();
        CriteriaQuery<Long> cq = cb.createQuery(Long.class);
        Root<BorrowerEntity> borrowerEntityRoot = cq.from(BorrowerEntity.class);
        Join<BorrowerEntity, WhitelistEntity> borrowerWhitelistJoin = borrowerEntityRoot.join(WHITELIST);
        List<Predicate> predicateList = new ArrayList<>();
        if (Objects.nonNull(getBorrowerCountRequest)) {
            if (Objects.nonNull(getBorrowerCountRequest.getWhitelistEnabledFlag()) &&
                    getBorrowerCountRequest.getWhitelistEnabledFlag().equals(Boolean.TRUE)) {
                predicateList.add(cb.equal(borrowerEntityRoot.get(ENABLED), true));
            }
            if (Objects.nonNull(getBorrowerCountRequest.getWhitelistId())) {
                predicateList.add(cb.equal(borrowerWhitelistJoin.get("id"),
                        getBorrowerCountRequest.getWhitelistId()));
            }
            if (Objects.nonNull(getBorrowerCountRequest.getProductType())) {
                predicateList.add(cb.equal(borrowerWhitelistJoin.get(ENABLED), true));
                predicateList.add(cb.equal(borrowerWhitelistJoin.get(PRODUCT_TYPE),
                        getBorrowerCountRequest.getProductType()));
            }
            if (Objects.nonNull(getBorrowerCountRequest.getLender())) {
                predicateList.add(cb.equal(borrowerWhitelistJoin.get("lender"),
                        getBorrowerCountRequest.getLender().toString()));
            }
        }

        cq.select(cb.count(borrowerEntityRoot)).where(predicateList.toArray(new Predicate[0]));
        Query<Long> query = currentSession().createQuery(cq);
        List<Long> queryResult = query.getResultList();
        if (Objects.nonNull(queryResult) && !queryResult.isEmpty()) {
            return queryResult.get(0);
        }
        return null;
    }

    @Override
    public BorrowerEntity getActiveBorrower(String externalId, Long merchantId, ProductType productType, String lender) {
        Criteria criteria = criteria().add(Restrictions.eq(EXTERNALID, externalId))
                .add(Restrictions.eq(ENABLED, true));
        criteria.createCriteria(WHITELIST).add(Restrictions.eq(ENABLED, true))
                .add(Restrictions.eq(PRODUCT_TYPE, productType))
                .add(Restrictions.eq("lender", lender))
                .createAlias(MERCHANT, "m")
                .add(Restrictions.eq("m.id", merchantId));
        return uniqueResult(criteria);
    }
}
