package com.flipkart.fintech.pinaka.service.web.v6;

import com.codahale.metrics.MetricRegistry;
import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.fintech.pinaka.api.request.creditRequest.CreditRequest;
import com.flipkart.fintech.pinaka.api.response.v6.Action;
import com.flipkart.fintech.pinaka.api.response.v6.ActionType;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.profile.client.ProfileClient;
import com.flipkart.fintech.profile.common.PinakaMetricRegistry;
import com.flipkart.fintech.profile.request.BureauDataRequestSm;
import com.flipkart.fintech.profile.request.CrossMerchantConsentRequest;
import com.flipkart.fintech.profile.response.BureauDataResponse;
import com.flipkart.fintech.profile.response.CrossMerchantConsentResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.google.inject.Inject;
import de.client.shade.javax.validation.Valid;
import io.dropwizard.hibernate.UnitOfWork;
import io.swagger.annotations.ApiOperation;

import java.util.HashMap;
import java.util.Objects;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import lombok.CustomLog;

@CustomLog
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("6/check-score")
public class CheckScorePageFetchResource {

    private final ProfileClient profileClient;
    private final DynamicBucket dynamicBucket;

    private static final String SM_CS_PAGE_URL = "/creditscore/homepage";
    private static final String SM_NOSCORE_PAGE_URL = "/creditscore/checkscore/noScoreAvailable";


    @Inject
    public CheckScorePageFetchResource(ProfileClient profileClient, DynamicBucket dynamicBucket) {
        this.profileClient = profileClient;
        this.dynamicBucket = dynamicBucket;
    }

    @POST
    @Timed
    @ExceptionMetered
    @ApiOperation("UserAction Submit")
    @Path("/existing-score")
    @UnitOfWork
    public PageActionResponse pageFetchBureau(@Valid CreditRequest creditRequest,
                                              @HeaderParam("X-User-Agent") String userAgent,
                                              @NotNull @HeaderParam("X-Request-Id") String requestId) throws PinakaException {
        log.info("Bureau Action Request received , accountId: {}", creditRequest.getMerchantAccountId());
        try {
            BureauDataResponse bureauDataResponse = profileClient.getExistingCreditScore(creditRequest.getMerchantAccountId(),creditRequest.getSmAccountId());

            if (Objects.isNull(bureauDataResponse.getError())){
                String creditScoreUrl = "/credit-score/display";
                if(dynamicBucket.getBoolean("showCheckScoreInsights")){
                    creditScoreUrl = "/credit-score/insights";
                }
                PinakaMetricRegistry.getMetricRegistry().meter(
                        MetricRegistry.name(CheckScorePageFetchResource.class, "pageFetchForBureau", "creditScorePage")).mark();
                return PageActionResponse.builder().action(Action.builder().actionType(ActionType.NAVIGATION).url(creditScoreUrl).build()).actionSuccess(true).params(null).error(null).build();
            }else{
                PinakaMetricRegistry.getMetricRegistry().meter(
                        MetricRegistry.name(CheckScorePageFetchResource.class, "pageFetchForBureau", "creditScoreForm")).mark();
                return PageActionResponse.builder().action(Action.builder().actionType(ActionType.NAVIGATION).url("/credit-score/form").build()).actionSuccess(true).params(null).error(null).build();
            }
        } catch (Exception ex) {
            log.error("Error while submitting the page for applicationId: {}, accountId: {}: {}", creditRequest.getMerchantAccountId(),
                     ex.getMessage()); 
            throw  new PinakaException(ex);
        }
    }

    @POST
    @Timed
    @ExceptionMetered
    @ApiOperation("UserAction Submit")
    @Path("/refresh-score")
    @UnitOfWork
    @UnitOfWork(value="profile_service")
    public PageActionResponse refreshPageFetchBureau(@Valid CreditRequest creditRequest,
                                           @HeaderParam("X-User-Agent") String userAgent,
                                           @NotNull @HeaderParam("X-Request-Id") String requestId) throws PinakaException {
        log.info("Refresh bureau Action Request received , accountId: {}", creditRequest.getMerchantAccountId());
        try {
            BureauDataResponse bureauDataResponse = profileClient.getRefreshCreditScore(creditRequest.getMerchantAccountId(),creditRequest.getSmAccountId());
            PinakaMetricRegistry.getMetricRegistry().meter(
                    MetricRegistry.name(CheckScorePageFetchResource.class, "pageFetchForBureau", "refreshScore", "Succeed")).mark();
            return PageActionResponse.builder().action(Action.builder().actionType(ActionType.NAVIGATION).url("/credit-score/insights").build()).actionSuccess(true).params(null).error(null).build();
        } catch (Exception ex) {
            log.error("Error while submitting the page for applicationId: {}, accountId: {}: {}", creditRequest.getMerchantAccountId(),
                    ex.getMessage());
            throw new PinakaException(ex);
        }
    }

    @POST
    @Timed
    @ExceptionMetered
    @ApiOperation("UserAction to refresh credit score")
    @Path("/sm-refresh-score")
    @UnitOfWork
    public PageActionResponse refreshSMPageFetchBureau(@Valid String smUserId,
                                                       @HeaderParam("X-User-Agent") String userAgent,
                                                       @NotNull @HeaderParam("X-Request-Id") String requestId) throws PinakaException {
        log.info("SM Refresh bureau Action Request received , smuserId: {}", smUserId);
        try {
            profileClient.getRefreshCreditScoreSm(smUserId);  // to refresh the report in the DB
            PinakaMetricRegistry.getMetricRegistry().meter(
                    MetricRegistry.name(CheckScorePageFetchResource.class, "pageFetchForBureau", "smRefreshScore", "Succeed"))
                    .mark();
            HashMap <String,Object> params = new HashMap<>();
            params.put( "refresh", "HARD_REFRESH");
            params.put("mode","navigate");
            params.put("backDisabled","false");
            return PageActionResponse.builder()
                    .action(Action.builder().actionType(ActionType.NAVIGATION).url(SM_CS_PAGE_URL).params(params).build())
                    .actionSuccess(true)
                    .params(null)
                    .error(null)
                    .build();
        } catch (Exception ex) {
            log.error("Error while submitting the page for smUserId: {}, {}", smUserId, ex.getMessage());
            throw new PinakaException(ex);
        }
    }

    @POST
    @Timed
    @ExceptionMetered
    @ApiOperation("UserAction to get the credit Score")
    @Path("/sm-fetch-score")
    @UnitOfWork
    public PageActionResponse getSMPageFetchBureau(@Valid BureauDataRequestSm bureauDataRequestSm,
                                                   @HeaderParam("X-User-Agent") String userAgent,
                                                   @NotNull @HeaderParam("X-Request-Id") String requestId) throws PinakaException {
        log.info("Bureau Action Request on getSMPageFetchBureau received , smUserId: {}", bureauDataRequestSm.getSmAccountId());
        try {
            BureauDataResponse bureauDataResponse = profileClient.getCreditScoreSm(bureauDataRequestSm);
            if (Objects.isNull(bureauDataResponse.getError()) && Objects.nonNull(bureauDataResponse.getCreditScore())) {
                PinakaMetricRegistry.getMetricRegistry().meter(
                        MetricRegistry.name(CheckScorePageFetchResource.class, "pageFetchForBureau", "smCreditScorePage")
                ).mark();
                HashMap <String,Object> params = new HashMap<>();
                params.put( "refresh", "HARD_REFRESH");
                params.put("mode","navigate");
                params.put("backDisabled","false");
                return PageActionResponse.builder()
                        .action(Action.builder().actionType(ActionType.NAVIGATION).url(SM_CS_PAGE_URL).params(params).build())
                        .actionSuccess(true)
                        .params(null)
                        .error(null)
                        .build();
            } else {
                PinakaMetricRegistry.getMetricRegistry().meter(
                        MetricRegistry.name(CheckScorePageFetchResource.class, "pageFetchForBureau", "smNoScorePage")
                ).mark();

                HashMap <String,Object> params = new HashMap<>();
                params.put( "refresh", "HARD_REFRESH");
                params.put("mode","navigate");
                params.put("backDisabled","false");
                return PageActionResponse.builder()
                        .action(Action.builder().actionType(ActionType.NAVIGATION).url(SM_NOSCORE_PAGE_URL).params(params).build())
                        .actionSuccess(true)
                        .params(null)
                        .error(null)
                        .build();
            }
        } catch (Exception ex) {
            log.error("Error while submitting the getSMPageFetchBureau page for smUserId: {}, {}",
                    bureauDataRequestSm.getSmAccountId(), ex.getMessage());
            throw new PinakaException(ex);
        }
    }

    @POST
    @Timed
    @ExceptionMetered
    @ApiOperation("UserAction to put the credit Score Cross Merchant Consent")
    @Path("/sm-cross-merchant-consent")
    @UnitOfWork
    public PageActionResponse csCrossMerchantConsent(@Valid CrossMerchantConsentRequest crossMerchantConsentRequest,
                                                   @HeaderParam("X-User-Agent") String userAgent,
                                                   @NotNull @HeaderParam("X-Request-Id") String requestId) throws PinakaException {
        log.info("Credit Score Cross Merchant consent request received for, smUserId: {}", crossMerchantConsentRequest.getAccountId());
        try {
            CrossMerchantConsentResponse crossMerchantConsentResponse = profileClient.putCrossMerchantConsent(crossMerchantConsentRequest);
            if (Objects.nonNull(crossMerchantConsentResponse) && "SUCCESS".equals(crossMerchantConsentResponse.getStatus())) {
                PinakaMetricRegistry.getMetricRegistry().meter(
                        MetricRegistry.name(CheckScorePageFetchResource.class, "csCrossMerchantConsent", "consentAccepted")
                ).mark();
                HashMap <String,Object> params = new HashMap<>();
                params.put( "refresh", "SOFT_REFRESH");
                params.put("mode","navigate");
                return PageActionResponse.builder()
                        .action(Action.builder().actionType(ActionType.NAVIGATION).url(SM_CS_PAGE_URL).params(params).build())
                        .actionSuccess(true)
                        .params(null)
                        .error(null)
                        .build();
            } else {
                PinakaMetricRegistry.getMetricRegistry().meter(
                        MetricRegistry.name(CheckScorePageFetchResource.class, "csCrossMerchantConsent", "consentNotAccepted")
                ).mark();

                return PageActionResponse.builder()
                        .actionSuccess(false)
                        .params(null)
                        .error(null)
                        .build();
            }
        } catch (Exception ex) {
            log.error("Error while submitting the getSMPageFetchBureau page for smUserId: {}, {}",
                    crossMerchantConsentRequest.getAccountId(), ex.getMessage());
            throw new PinakaException(ex);
        }
    }
}
