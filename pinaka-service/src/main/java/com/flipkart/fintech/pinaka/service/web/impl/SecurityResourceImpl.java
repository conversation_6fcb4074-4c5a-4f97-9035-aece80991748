package com.flipkart.fintech.pinaka.service.web.impl;

import com.flipkart.fintech.pinaka.api.response.SecurityKeyResponse;
import com.flipkart.fintech.pinaka.service.core.SecurityService;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.exception.PinakaWebAppException;
import com.flipkart.fintech.pinaka.service.web.SecurityResource;
import com.google.inject.Inject;
import javax.ws.rs.core.Response;

/**
 * Created by su<PERSON><PERSON><PERSON>.r on 25/01/18.
 */
public class SecurityResourceImpl implements SecurityResource {

    private SecurityService securityService;

    @Inject
    public SecurityResourceImpl(SecurityService securityService){
        this.securityService = securityService;
    }

    @Override
    public Response getKey() {
        SecurityKeyResponse response;
        try {
            response = securityService.getKey();
        } catch (PinakaException e) {
            throw new PinakaWebAppException(Response.Status.INTERNAL_SERVER_ERROR, e.getMessage());
        }
        return Response.status(Response.Status.OK).entity(response).build();
    }
}
