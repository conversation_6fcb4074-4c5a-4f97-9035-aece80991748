package com.flipkart.fintech.pinaka.service.response;

import com.flipkart.fintech.pandora.api.model.pl.response.InitialOfferGenerationStatusResponse;
import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import java.util.Map;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@RequiredArgsConstructor
@Getter
@Setter
public class OfferScreenPageDataSourceResponse {
        private InitialOfferGenerationStatusResponse initialOfferGenerationStatusResponse;
        private EncryptionData encryptionData;
        private Map<String, Object> queryParams;

}
