package com.flipkart.fintech.pinaka.service.widgettransformer.lead.v4;

import com.flipkart.fintech.pinaka.client.ObjectMapperUtil;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.pinaka.service.utils.TransformerUtils.TransformerUtils;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.LenderCarouselTransformer;
import com.flipkart.fintech.pinaka.service.widgettransformer.widgets.ListFormWidgetTransformer;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardCarouselWidgetDataV0;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardSummaryListWidgetData;

import java.io.IOException;

public class LV4LenderCarouselTransformer implements ListFormWidgetTransformer, LenderCarouselTransformer {

    private static final String LANDING_PAGE_CAROUSEL_TEMPLATE;
    private static final String LENDER_CAROUSEL_TEMPLATE;

    static {
        LANDING_PAGE_CAROUSEL_TEMPLATE = TransformerUtils.readFileasString("template/lead/V4/LandingPageVerticalCarousel.json");
        LENDER_CAROUSEL_TEMPLATE = TransformerUtils.readFileasString("template/lead/V4/LandingPageLenderCarousel.json");
    }

    @Override
    public CardSummaryListWidgetData buildWidgetGroupData(ApplicationDataResponse applicationDataResponse) throws PinakaException {
        CardSummaryListWidgetData listWidgetData;
        try {
            listWidgetData = ObjectMapperUtil.get().readValue(LANDING_PAGE_CAROUSEL_TEMPLATE, CardSummaryListWidgetData.class);

        } catch (IOException e) {
            throw new PinakaException("Error while building widget Group Data for LV4 Landing Page for userId: " + applicationDataResponse.getSmUserId(), e);
        }
        // Cast ListWidgetData to GroupedFormWidgetData since they share the same interface
        return listWidgetData;
    }

    @Override
    public CardCarouselWidgetDataV0 buildWidgetData(ApplicationDataResponse applicationDataResponse) throws PinakaException {
        CardCarouselWidgetDataV0 cardCarouselWidgetData;
        try {
            cardCarouselWidgetData = ObjectMapperUtil.get().readValue(LENDER_CAROUSEL_TEMPLATE, CardCarouselWidgetDataV0.class);
        } catch (IOException e) {
            throw new PinakaException("Error while building widget data for LV4 Lender Carousel for userId: " + applicationDataResponse.getSmUserId(), e);
        }
        return cardCarouselWidgetData;
    }
}
