package com.flipkart.fintech.pinaka.service.utils.FormUtils;

import static com.flipkart.fintech.pinaka.service.pagehandler.Constants.*;

import com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.enums.TenureUnit;
import com.flipkart.rome.datatypes.request.fintech.calm.Tenure;
import com.flipkart.rome.datatypes.response.fintech.supermoney.EmiSchedule;
import com.flipkart.rome.datatypes.response.fintech.supermoney.TenuredEmiSchedule;
import com.flipkart.rome.datatypes.response.fintech.supermoney.UnitLevelEmiSchedule;
import com.flipkart.rome.datatypes.response.fintech.supermoney.formfields.custom.BankOfferFormFieldData;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.List;

import com.google.common.annotations.VisibleForTesting;
import org.apache.poi.ss.formula.functions.FinanceLib;

public class OfferScreenUtils {


    private static final int CUT_OFF_DATE = 18; // TODO: rename this to a domain specific name

    private OfferScreenUtils() {
    }

    //todo : Add ut
    public static BankOfferFormFieldData setEmiSchedules(BankOfferFormFieldData bankOfferFormFieldData, String processingFee)
        throws ParseException {
        List<TenuredEmiSchedule> tenuredEmiSchedules = new ArrayList<>();
        int currentTenure = (int) bankOfferFormFieldData.getTenureSlider().getValue().getMinValue();
        while (currentTenure <= (int) bankOfferFormFieldData.getTenureSlider().getValue().getMaxValue()) {
            TenuredEmiSchedule tenuredEmiSchedule = new TenuredEmiSchedule();
            Tenure tenure = new Tenure();
            tenure.setValue(currentTenure);
            tenure.setUnit(TENURE_UNIT);
            UnitLevelEmiSchedule unitLevelEmiSchedule = new UnitLevelEmiSchedule(DEFAULT_PRINCIPAL_AMT,
                getEmiSchedule(bankOfferFormFieldData.getInterest().getValue(), currentTenure));
            tenuredEmiSchedule.setTenure(tenure);
            tenuredEmiSchedule.setUnitLevelEmiSchedule(unitLevelEmiSchedule);
            tenuredEmiSchedules.add(tenuredEmiSchedule);
            if (currentTenure < bankOfferFormFieldData.getTenureSlider().getValue().getMaxValue()
                && currentTenure + 3 > bankOfferFormFieldData.getTenureSlider().getValue().getMaxValue()) {
                currentTenure = (int) bankOfferFormFieldData.getTenureSlider().getValue().getMaxValue();
                continue;
            }
            currentTenure = currentTenure + 3;
        }
        bankOfferFormFieldData.getEmiCalculator().setEmiSchedules(tenuredEmiSchedules);
        bankOfferFormFieldData.getEmiCalculator().getCharges().get(0).setValue(Double.valueOf(processingFee));
        return bankOfferFormFieldData;
    }

    private static List<EmiSchedule> getEmiSchedule(double rate, int tenurePeriod) throws ParseException {
        List<EmiSchedule> emiSchedules = new ArrayList<>();
        LocalDate date = getScheduleStartDate();
        for (int i = 0; i < tenurePeriod; i++) {
            EmiSchedule emiSchedule = new EmiSchedule();
            date = date.plusDays(4);
            DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern(EMI_DATE_PATTERN);
            String formattedDate = date.format(dateFormatter);
            emiSchedule.setDate(formattedDate);
            emiSchedule.setNumberOfDays((int) ChronoUnit.DAYS.between(date,
                date.with(TemporalAdjusters.firstDayOfNextMonth()).plusDays(4)));
            emiSchedule.setEmiUnitAmount(getEmiAmount(rate, tenurePeriod));
            emiSchedules.add(emiSchedule);
            date = date.with(TemporalAdjusters.firstDayOfNextMonth());
        }
        return emiSchedules;
    }

    public static LocalDate getScheduleStartDate() {
        if(LocalDate.now().getDayOfMonth()> CUT_OFF_DATE) {
            return (LocalDate.now().with(TemporalAdjusters.firstDayOfNextMonth())).with(TemporalAdjusters.firstDayOfNextMonth());
        }
        return LocalDate.now().with(TemporalAdjusters.firstDayOfNextMonth());
    }

    @VisibleForTesting
    static double getEmiAmount(double rate, int tenurePeriod) throws ParseException {
        DecimalFormat df = new DecimalFormat("0.00");
        rate = rate/100;
        String formate = df.format(FinanceLib.pmt(rate / 12, tenurePeriod, EMI_CALCULATOR_AMT, 0, false));
        return Double.parseDouble(formate);
    }

    public static Double getEmi(Double loanAmount, com.flipkart.fintech.pandora.api.model.pl.sandbox.dto.Tenure tenure, Double roi){
        int tenureVal = tenure.getUnit() == TenureUnit.YEAR ? tenure.getValue() * 12 : tenure.getValue();
        Double effectRoi = roundToFixedDecimal(roi, 5)/(12*100);
        Double onePlusRPowerN = Math.pow(1 + effectRoi, tenureVal);
        Double emiValue = 0D;
        if(onePlusRPowerN > 1){
            emiValue = roundToFixedDecimal((loanAmount * effectRoi * onePlusRPowerN)/(onePlusRPowerN - 1), 2);
        }
        return emiValue;
    }

    private static Double roundToFixedDecimal(Double num, int places){
        Double expnt = Math.pow(10, places);
        return  Math.round(num*expnt)/expnt;
    }

}