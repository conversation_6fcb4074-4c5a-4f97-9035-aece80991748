package com.flipkart.fintech.pinaka.service.core.v6.impl;

import com.flipkart.fintech.pinaka.api.response.v6.SearchOption;
import com.flipkart.fintech.pinaka.api.response.v6.SearchResponse;
import com.flipkart.fintech.pinaka.service.constants.PinakaConstants.CompanyMasterData;
import com.flipkart.fintech.pinaka.service.core.v6.EmploymentService;
import com.flipkart.fintech.pinaka.service.external.ElasticSearchClient;
import com.flipkart.kloud.config.DynamicBucket;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import javax.inject.Inject;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;

import static com.flipkart.fintech.pinaka.service.application.Constants.EMPLOYER_INDEX;
import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.CompanyMasterData.PL_EMPLOYER_INDEX_NAME_KEY;

public class EmploymentServiceImpl implements EmploymentService {
  private final ElasticSearchClient elasticSearchClient;
  private final DynamicBucket dynamicBucket;

  @Inject
  public EmploymentServiceImpl(ElasticSearchClient elasticSearchClient, DynamicBucket dynamicBucket) {
    this.elasticSearchClient = elasticSearchClient;
    this.dynamicBucket = dynamicBucket;
  }

  @Override
  public SearchResponse getPlEmployerNameSug(String prefix) {
    SearchResponse.SearchResponseBuilder responseBuilder = SearchResponse.builder();
    int size = Optional.ofNullable(dynamicBucket.getInt(CompanyMasterData.PL_EMP_AUTO_SUGGEST_SIZE))
        .orElse(CompanyMasterData.PL_EMP_AUTO_SUGGEST_DEFAULT_SIZE);
    String index_pattern = Optional.ofNullable(dynamicBucket.getString(PL_EMPLOYER_INDEX_NAME_KEY)).orElse(EMPLOYER_INDEX);
    SearchHits hits = elasticSearchClient
        .prefixSearch(prefix, index_pattern,
            CompanyMasterData.PL_EMPLOYER_NAME, size, "priority_order");
    if(Objects.nonNull(hits.getHits()) && hits.getHits().length > 0) {
      List<SearchOption> list = new ArrayList<>();
      for(SearchHit hit: hits.getHits()) {
        Map<String, Object> map = hit.getSourceAsMap();
        if(Objects.nonNull(map)) {
          SearchOption searchOption = SearchOption.builder()
              .id((String) map.get(CompanyMasterData.PL_EMP_ID))
              .title((String) map.get(CompanyMasterData.PL_EMPLOYER_NAME))
              .build();
          list.add(searchOption);
        }
      }
      responseBuilder.options(list);
    }
    else {
      responseBuilder.options(Collections.emptyList());
    }

    return responseBuilder.build();
  }

  @Override
  public SearchResponse getEmployerNameSug(String prefix) {
    SearchResponse.SearchResponseBuilder responseBuilder = SearchResponse.builder();
    int size = Optional.ofNullable(dynamicBucket.getInt(CompanyMasterData.PL_EMP_AUTO_SUGGEST_SIZE))
        .orElse(CompanyMasterData.PL_EMP_AUTO_SUGGEST_DEFAULT_SIZE);
    String index_pattern = "employer_master";
    SearchHits hits = elasticSearchClient
        .prefixSearch(prefix, index_pattern,
            CompanyMasterData.PL_EMPLOYER_NAME, size, null);
    if(Objects.nonNull(hits.getHits()) && hits.getHits().length > 0) {
      List<SearchOption> list = new ArrayList<>();
      for(SearchHit hit: hits.getHits()) {
        Map<String, Object> map = hit.getSourceAsMap();
        if(Objects.nonNull(map)) {
          SearchOption searchOption = SearchOption.builder()
              .id(hit.getId())
              .title((String) map.get(CompanyMasterData.PL_EMPLOYER_NAME))
              .build();
          list.add(searchOption);
        }
      }
      responseBuilder.options(list);
    }
    else {
      responseBuilder.options(Collections.emptyList());
    }

    return responseBuilder.build();
  }
}
