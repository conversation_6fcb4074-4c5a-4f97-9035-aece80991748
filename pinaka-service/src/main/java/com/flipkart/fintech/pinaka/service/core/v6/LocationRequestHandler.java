package com.flipkart.fintech.pinaka.service.core.v6;

import com.flipkart.affordability.model.response.AddressDetailResponse;
import com.flipkart.fintech.pinaka.api.response.v6.PincodeDetailsResponse;
import com.flipkart.fintech.pinaka.service.core.v6.impl.LocationRequestHandlerImpl;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.google.inject.ImplementedBy;

@ImplementedBy(LocationRequestHandlerImpl.class)
public interface LocationRequestHandler {
    PincodeDetailsResponse checkPincodeExistence(String pincode) throws PinakaException;

    AddressDetailResponse getUserAddress(String userId, String merchantId, String smUserId, String requestId) throws PinakaException;
}
