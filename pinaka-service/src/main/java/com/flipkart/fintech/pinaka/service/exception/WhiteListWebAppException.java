package com.flipkart.fintech.pinaka.service.exception;

import io.dropwizard.jersey.errors.ErrorMessage;

import javax.ws.rs.WebApplicationException;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON>.r on 08/11/17.
 */
public class WhiteListWebAppException extends WebApplicationException {
    public WhiteListWebAppException(String message){super(message);}

    public WhiteListWebAppException(Response.Status status, String message){
        super(Response.status(status.getStatusCode()).
                entity(new ErrorMessage(message)).type(MediaType.APPLICATION_JSON_TYPE).build());
    }

    public WhiteListWebAppException(int statusCode, String message){
        super(Response.status(statusCode).
                entity(new ErrorMessage(message)).type(MediaType.APPLICATION_JSON_TYPE).build());
    }

    public WhiteListWebAppException(Throwable cause){super(cause);}
}
