package com.flipkart.fintech.pinaka.service.core.impl;

import com.flipkart.fintech.pinaka.service.core.MerchantService;
import com.flipkart.fintech.pinaka.service.data.MerchantEntityDao;
import com.flipkart.fintech.pinaka.service.data.model.MerchantEntity;
import com.google.inject.Inject;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 28/09/17.
 */
public class MerchantServiceImpl implements MerchantService {

    private final MerchantEntityDao merchantDao;

    @Inject
    public MerchantServiceImpl(MerchantEntityDao merchantDao){
        this.merchantDao = merchantDao;
    }

    @Override
    public MerchantEntity getMerchantByKey(String merchantKey) {
        return merchantDao.getByKey(merchantKey);
    }

    @Override
    public MerchantEntity getMerchantById(Long id) {
        return merchantDao.getById(id);
    }
}
