package com.flipkart.fintech.pinaka.service.core.v6.document;

import com.flipkart.fintech.common.enums.Tenant;
import com.flipkart.fintech.pinaka.api.request.v6.documents.Document;
import org.apache.commons.lang3.StringUtils;
import javax.validation.constraints.NotNull;

public class DocumentUtils {

  @NotNull
  @Deprecated
  public static String getBucketName(Tenant tenant) {
    return String.join("-", "fk-p", tenant.name().toLowerCase(), "fintech-xml-kyc-docs");
  }

  @NotNull
  public static String getBucketName(Tenant tenant, Document document) {
    String documentType = document.getDocumentType().toLowerCase();
    if (StringUtils.containsWhitespace(documentType)) {
      throw new IllegalArgumentException("DocumentType contains whitespace");
    }
    return String.join("-", "fk-p", tenant.name().toLowerCase(), documentType, "docs");
  }

  @NotNull
  public static String getDocumentKey(String applicationId) {
    return applicationId;
  }

}
