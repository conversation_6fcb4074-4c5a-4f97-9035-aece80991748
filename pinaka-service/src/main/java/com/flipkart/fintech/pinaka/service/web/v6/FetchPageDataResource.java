package com.flipkart.fintech.pinaka.service.web.v6;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.api.request.v6.FetchBulkDataRequest;
import com.flipkart.fintech.pinaka.api.request.v6.PageServiceRequest;
import com.flipkart.fintech.pinaka.api.response.v6.FetchBulkDataResponse;
import com.flipkart.fintech.pinaka.api.response.v6.FetchBulkDataResponseV2;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.service.core.v6.FetchPageDataHandler;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.google.inject.Inject;
import de.client.shade.javax.validation.Valid;
import io.dropwizard.hibernate.UnitOfWork;
import io.swagger.annotations.ApiOperation;
import javax.validation.constraints.NotNull;
import javax.ws.rs.Consumes;
import javax.ws.rs.HeaderParam;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;
import lombok.CustomLog;


@CustomLog
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("6/pl")
public class FetchPageDataResource {

    private final FetchPageDataHandler fetchPageDataHandler;

    @Inject
    public FetchPageDataResource(FetchPageDataHandler fetchPageDataHandler) {
        this.fetchPageDataHandler = fetchPageDataHandler;
    }

    @POST
    @Timed
    @ExceptionMetered
    @ApiOperation("Personal Loan Page Fetch Data")
    @Path("/fetch-bulk-data")
    @UnitOfWork
    public FetchBulkDataResponse fetchBulkData(@Valid FetchBulkDataRequest fetchBulkDataRequest,
                                               @NotNull @HeaderParam("X-Request-Id") String requestId,
                                               @NotNull @HeaderParam("X-Merchant-Id") String merchantId) throws PinakaException {
        log.info("Fetch Bulk Data V1 Request received for accountId: {}, applicationId: {}, request: {}",
                fetchBulkDataRequest.getAccountId(),
                fetchBulkDataRequest.getApplicationId(), fetchBulkDataRequest);
        return fetchPageDataHandler.fetchBulkData(fetchBulkDataRequest, merchantId);
    }

    @POST
    @Timed
    @ExceptionMetered
    @ApiOperation("Personal Loan IDFC Page Fetch Data")
    @Path("/fetch-bulk-data-v2")
    @UnitOfWork(value = "profile_service")
    public FetchBulkDataResponseV2 fetchBulkDataV2(@Valid PageServiceRequest pageServiceRequest,
            @NotNull @HeaderParam("X-Merchant-Id") String merchantId,
            @NotNull @HeaderParam("X-Request-Id") String requestId)
            throws PinakaException {
        log.info("Fetch Bulk Data V2 Request received for accountId: {}, requestId {},request: {}", pageServiceRequest.getAccountId(),
                requestId,pageServiceRequest);
        try {
            return fetchPageDataHandler.fetchBulkDataV2(merchantId,
                pageServiceRequest);
        } catch (Exception e) {
            log.error(" fetch bulk data failed for {},{},{}, with exception: {}",
                requestId,pageServiceRequest.getAccountId(), pageServiceRequest.getApplicationId(),
                e.getMessage());
            throw e;
        }
    }

}
