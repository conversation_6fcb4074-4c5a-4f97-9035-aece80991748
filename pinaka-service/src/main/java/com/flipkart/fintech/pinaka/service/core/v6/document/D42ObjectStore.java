package com.flipkart.fintech.pinaka.service.core.v6.document;

import com.amazonaws.AmazonServiceException;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectInputStream;
import com.flipkart.fintech.pinaka.api.response.v6.DocumentDownloadResponse;
import com.google.inject.Inject;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import lombok.CustomLog;

@CustomLog
public class D42ObjectStore implements ObjectStore {
  private static final List<Integer> RETRY_TIME = Collections.emptyList();
  private final AmazonS3 amazonS3;
  @Inject
  public D42ObjectStore(AmazonS3 amazonS3) {
    this.amazonS3 = amazonS3;
  }

  @Override
  public void upload(String bucketName, String documentKey,
      byte[] content, Map<String, String> d42MetaData) throws Exception {
    try {
      this.uploadDocumentToD42(bucketName, documentKey, content, d42MetaData);
    } catch (Exception var8) {
      log.error("Could not upload doc to d42 for application, Storing doc in AMS", var8);
      throw new Exception(String.format("Could not upload doc to d42 for application: %s, Storing doc in AMS", documentKey));
    }
  }

    @Override
    public DocumentDownloadResponse getDocument(GetObjectRequest getObjectRequest) throws Exception {
        S3Object object = this.amazonS3.getObject(getObjectRequest);
        S3ObjectInputStream objectContent = object.getObjectContent();
        try {
            Map<String,String>usermetaData = object.getObjectMetadata().getUserMetadata();
            byte[] document = getDocumentFromInputStream(objectContent);
            String base64Image=new String(document, StandardCharsets.UTF_8);
            return DocumentDownloadResponse.builder().base64EncodedImage(base64Image).imageMetaData(usermetaData).build();
        } catch (IOException e) {
            log.error("Error while reading document from s3", e);
        }
        throw new Exception("Error while reading document from s3");
    }


    private void uploadDocumentToD42(String bucketName, String key, byte[] content,
      Map<String, String> metaData) throws Exception {
    ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(content);
    ObjectMetadata objectMetadata = createObjectMetadata(content, metaData);
    int i = 0;

    while(i <= RETRY_TIME.size()) {
      try {
        this.amazonS3.putObject(bucketName, key, byteArrayInputStream, objectMetadata);
        return;
      } catch (AmazonServiceException var11) {
        log.error("Exception while uploading document in GCS bucket for key: {}, bucketName: {}, error : {} ", new Object[]{key, bucketName, var11});
        if (i == RETRY_TIME.size()) {
          log.error("Error while uploading the document for key : {}.", new Object[]{key, var11});
          throw new Exception("Error while uploading the document after " + RETRY_TIME.size() + "retries.", var11);
        }

        log.info("Document upload failed for key : {}. Retrying after {} seconds", new Object[]{key, RETRY_TIME.get(i)});

        try {
          Thread.sleep(RETRY_TIME.get(i) * 1000L);
        } catch (InterruptedException var10) {
          throw new Exception("Error while uploading the document.", var11);
        }

        ++i;
      }
    }

  }

  private static ObjectMetadata createObjectMetadata(byte[] content, Map<String, String> metaData) {
    ObjectMetadata metadata = new ObjectMetadata();
    metadata.setContentLength(content.length);
    metadata.setUserMetadata(metaData);
    return metadata;
  }

    private byte[] getDocumentFromInputStream(InputStream inputStream) throws IOException {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            outputStream.write(buffer, 0, bytesRead);
        }
        return outputStream.toByteArray();
    }
}
