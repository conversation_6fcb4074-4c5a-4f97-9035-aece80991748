package com.flipkart.fintech.pinaka.service.helper;


import com.flipkart.fintech.lead.model.PaOffer;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.profile.model.EmploymentType;
import lombok.CustomLog;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;

import static com.flipkart.fintech.pinaka.service.constants.PinakaConstants.PLConstants.PA_OFFER;

@Data
@CustomLog
public class ApplicationUserInputData {
    private String firstName;
    private String lastName;
    private String phoneNumber;
    private String dob;
    private String city;
    private String state;
    private Integer pincode;
    private String gender;
    private String area;
    private String email;
    private String pan;
    private String loanPurpose;
    private String houseNumber;
    private EmploymentType employmentType;
    private String income;
    private String bonusIncome;
    private Object organization;
    private String incomeSource;
    private Object industryName;
    private String annualTurnOver;
    private Object consentListData;
    private Object consentData;
    private PaOffer paOffer;

    public static ApplicationUserInputData getApplicationResponseData(Map<String, Object> applicationDataResponse) throws PinakaException {
        ApplicationUserInputData data = new ApplicationUserInputData();

        Map<String, Object> combinedDetails = new HashMap<>();
        combinedDetails.putAll(ApplicationInputDataMapper.getNameDetails(applicationDataResponse));
        combinedDetails.putAll(ApplicationInputDataMapper.getBasicDetails(applicationDataResponse));
        combinedDetails.putAll(ApplicationInputDataMapper.getIncomeDetails(applicationDataResponse));

        data.firstName = getString(combinedDetails, "firstName");
        data.lastName = getString(combinedDetails, "lastName");
        data.phoneNumber = getString(combinedDetails, "phoneNumber");
        data.dob = getString(combinedDetails, "dob");
        data.gender = getString(combinedDetails, "gender");
        data.area = getString(combinedDetails, "area");
        data.email = getString(combinedDetails, "email");
        data.income = getString(combinedDetails, "income");
        data.loanPurpose = getString(combinedDetails, "loanPurpose");
        data.houseNumber = getString(combinedDetails, "houseNumber");
        data.incomeSource = getString(combinedDetails, "incomeSource");
        data.annualTurnOver = getString(combinedDetails, "annualTurnOver");
        data.bonusIncome = getString(combinedDetails, "bonusIncome");

        Object employment = combinedDetails.get("employmentType");
        if (employment instanceof String && StringUtils.isNotBlank((String) employment)) {
            data.employmentType = EmploymentType.valueOf((String) employment);
        }

        data.organization = combinedDetails.get("organization");
        data.industryName = combinedDetails.get("industryName");
        data.consentListData = combinedDetails.get("consentListData");
        data.consentData = combinedDetails.get("consentData");

        // Handle pan or panNumber
        data.pan = getString(combinedDetails, "panNumber");
        if (StringUtils.isBlank(data.pan)) {
            data.pan = getString(combinedDetails, "pan");
        }

        // Handle pincodeDetails or flat
        Object pincodeObj = combinedDetails.get("pincodeDetails");
        if (pincodeObj instanceof Map) {
            Map<?, ?> pincodeMap = (Map<?, ?>) pincodeObj;
            data.pincode = parseIntSafe(pincodeMap.get("pincode"), "pincode");
            data.city = getString(pincodeMap, "city");
            data.state = getString(pincodeMap, "state");
        } else {
            data.pincode = parseIntSafe(combinedDetails.get("pincode"), "pincode");
            data.city = getString(combinedDetails, "city");
            data.state = getString(combinedDetails, "state");
        }

        if (applicationDataResponse.get(PA_OFFER) instanceof PaOffer) {
            data.paOffer = (PaOffer) applicationDataResponse.get(PA_OFFER);
        }

        return data;
    }

    private static String getString(Map<?, ?> map, String key) {
        Object value = map.get(key);
        return value instanceof String && StringUtils.isNotBlank(value.toString()) ? value.toString() : null;
    }

    private static Integer parseIntSafe(Object value, String field) throws PinakaException {
        try {
            if (value instanceof Integer) {
                return (Integer) value;
            } else if (value instanceof String && StringUtils.isNumeric((String) value)) {
                return Integer.parseInt((String) value);
            }
        } catch (Exception e) {
            throw new PinakaException(String.format("Failed to parse integer for field: %s, value: %s ", field, value));
        }
        return null;
    }
}
