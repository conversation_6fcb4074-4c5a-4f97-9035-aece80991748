package com.flipkart.fintech.pinaka.service.data.model;

import com.flipkart.fintech.pinaka.api.enums.MerchantStatus;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

/**
 * Created by <PERSON><PERSON><PERSON>kumar.r on 31/08/17.
 */
@Entity
@Table(name = "merchants")
@Data
public class MerchantEntity extends BaseEntity {

    @Column(name = "merchant_key")
    private String merchantKey;

    @Column(name = "merchant_name")
    private String merchantName;

    @Column(name = "status")
    @Enumerated(EnumType.STRING)
    private MerchantStatus status;

    @Column(name = "mod_count")
    private int modCount;

}
