package com.flipkart.fintech.pinaka.service.core.page;

import com.flipkart.fintech.pinaka.api.response.v6.*;
import com.flipkart.fintech.pinaka.service.constants.PageConstant.ActionHandlerV2Constants;
import lombok.CustomLog;
import org.apache.commons.lang3.StringUtils;
import java.util.HashMap;
import java.util.Map;

@CustomLog
public class InvalidMerchantBehaviour {

  public static PageActionResponse getPageActionResponse(String merchantIdReq, String merchantIdApp, String leadId) {
      log.error("Merchant mismatch between application: {} and API: {}", merchantIdApp, merchantIdReq);
      Map<String, Object> params = null;
      if(StringUtils.isNotEmpty(leadId)){
          params = new HashMap<>();
          params.put("applicationId", leadId);
      }
      return PageActionResponse.builder().action(
          Action.builder().actionType(ActionType.NAVIGATION).url(ActionHandlerV2Constants.NO_LENDER_ALLOCATED)
                  .params(params).build()).actionSuccess(true).error(null).params(null).build();
  }

}
