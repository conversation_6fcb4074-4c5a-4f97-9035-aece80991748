package com.flipkart.fintech.pinaka.service.response;

import com.flipkart.rome.datatypes.response.fintech.onboarding.EncryptionData;
import java.util.HashMap;
import java.util.Map;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;

@RequiredArgsConstructor
@Getter
@Setter
public class OtpVerificationPageDataSourceResponse {

    private Map<String, Object> queryParams;
    private EncryptionData encryptionData;
    private Map<String,Object> resendOtpformData;

    public Map<String,Object> getResendOtpActionParams() {
        Map<String,Object> resendActionParams = new HashMap<>();
        resendActionParams.putAll(queryParams);
        resendActionParams.put("formData",resendOtpformData);
        return resendActionParams;
    }

}