package com.flipkart.sm.pages.dp.page.config;

import com.flipkart.dataprovider.models.controller.request.SingleSourcePageFetchControllerRequest;
import com.flipkart.fintech.pinaka.models.FeatureFlagConfig;
import com.flipkart.fintech.profile.client.ProfileClient;
import com.flipkart.fintech.profile.response.BureauDataResponse;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.sm.pages.dp.constants.Constants;
import com.google.inject.Inject;
import com.google.inject.name.Named;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.CustomLog;

import java.util.HashMap;
import java.util.Map;

@CustomLog
public class CsHomePageConfigArgumentsResolver implements IPageConfigArgumentsResolver {
    private static final String CREDIT_SCORE_FEATURE_FLAG = "creditScoreFeatureFlag";

    private final ProfileClient profileClient;
    private final DynamicBucket dynamicBucket;

    @Inject
    public CsHomePageConfigArgumentsResolver(
            ProfileClient profileClient,
            @Named("featureFlagConfigBucket") DynamicBucket dynamicBucket
    ) {
        this.profileClient = profileClient;
        this.dynamicBucket = dynamicBucket;
    }

    @Override
    public Map<String, String> getArguments(SingleSourcePageFetchControllerRequest request) {
        Map<String, String> tags = new HashMap<>();
        tags.put(Constants.ORG_KEY, Constants.SUPERMONEY);
        if (!getFeatureConfig().isFeatureEnabledForUser(
                request.getRequestContext().getDeviceContext().getOsName().toUpperCase(),
                request.getRequestContext().getUserAgentResponse().fkApp.versionName,
                request.getAccountId())) {
            tags.put(Constants.USER_CREDIT_REPORT_STATUS, Constants.COMING_SOON);
            return tags;
        }

        BureauDataResponse bureauDataResponse;
        try {
            String accountId = request.getRequestContext().getAccountId();
            bureauDataResponse = profileClient.getExistingCreditScoreSm(accountId);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        tags.put(
                Constants.USER_CREDIT_REPORT_STATUS,
                bureauDataResponse.isReportValid() ? Constants.EXISTING_CREDIT_REPORT : Constants.NO_EXISTING_CREDIT_REPORT
        );
        return tags;
    }

    // todo: Need to cache this with ttl of 10 mins
    private FeatureFlagConfig getFeatureConfig() {
        String jsonString = dynamicBucket.getString(CREDIT_SCORE_FEATURE_FLAG);
        try {
            return ObjectMapperUtil.get().readValue(jsonString, FeatureFlagConfig.class);
        } catch (Exception e) {
            log.error("Failed to fetch creditscore (app) feature flag from dynamic bucket", e);
            throw new RuntimeException(e);
        }
    }


}
