package com.flipkart.sm.pages.utils;

import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.common.enums.FontWeight;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.common.leaf.value.HeaderValue;
import com.flipkart.rome.datatypes.response.common.leaf.value.RichTextValue;
import com.flipkart.rome.datatypes.response.common.leaf.value.TextStyle;
import com.flipkart.rome.datatypes.response.common.leaf.value.TitleValue;
import com.flipkart.rome.datatypes.response.enums.LoginType;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.PageTitleWidgetData;
import com.flipkart.sm.pages.dp.constants.style.Colors;
import com.flipkart.sm.pages.dp.constants.style.FontFamily;
import com.flipkart.sm.pages.dp.constants.style.FontSizes;
import org.apache.commons.lang.StringUtils;

public class WidgetBuilderUtils {

    public static PageTitleWidgetData buildPageTitleWidget(String titleText, String subtitleText) {
        PageTitleWidgetData widgetData = new PageTitleWidgetData();
        HeaderValue headerValue = new HeaderValue();
        Integer titleFontSize = FontSizes.SIZE_32;
        Integer subtitleFontSize = FontSizes.SIZE_16;
        headerValue.setTitleValue(buildTitleValue(
                titleText, titleFontSize,
                String.valueOf(FontFamily.ANEK_LATIN), FontWeight.bold,
                Colors.PrimaryHeaderThemeColor.toString())
        );
        if (StringUtils.isNotEmpty(subtitleText)) {
            headerValue.setSubTitleValue(buildTitleValue(
                    subtitleText, subtitleFontSize,
                    String.valueOf(FontFamily.ANEK_LATIN), FontWeight.normal,
                    Colors.Grey600.toString())
            );
        }

        RenderableComponent<HeaderValue> renderableComponent = new RenderableComponent<>();
        renderableComponent.setValue(headerValue);
        widgetData.setPageTitle(renderableComponent);
        return widgetData;
    }

    public static RenderableComponent<RichTextValue> buildRenderableRichText(String titleText, TextStyle style) {
        RichTextValue richTextValue = new RichTextValue();
        richTextValue.setText(titleText);
        richTextValue.setStyle(style);
        RenderableComponent<RichTextValue> titleComponent = new RenderableComponent<>();
        titleComponent.setValue(richTextValue);
        return titleComponent;
    }

    private RenderableComponent<RichTextValue> buildRenderableRichText(String title, FontWeight fontWeight, String fontFamily, String color, Integer fontSize) {
        TextStyle textStyle = new TextStyle();
        textStyle.setFontWeight(fontWeight);
        textStyle.setFontFamily(fontFamily);
        textStyle.setColor(color);
        textStyle.setFontSize(fontSize);

        RichTextValue richTextValue = new RichTextValue();
        richTextValue.setText(title);
        richTextValue.setStyle(textStyle);
        RenderableComponent<RichTextValue> titleComponent = new RenderableComponent<>();
        titleComponent.setValue(richTextValue);
        return titleComponent;
    }


    public static RenderableComponent<HeaderValue> buildWidgetTitle(String title) {
        RenderableComponent<HeaderValue> t = new RenderableComponent<>();
        HeaderValue headerValue = new HeaderValue();
        headerValue.setTitleValue(buildRenderableTitle(
                title, FontSizes.SIZE_20,
                String.valueOf(FontFamily.ANEK_LATIN), FontWeight.semibold,
                Colors.Grey800.toString())
        );
        headerValue.getTitleValue().getStyle().setLineHeight(24);
        t.setValue(headerValue);
        return t;
    }

    public static RenderableComponent<HeaderValue> buildWidgetTitle(String title, String subtitle) {
        RenderableComponent<HeaderValue> t = new RenderableComponent<>();
        HeaderValue headerValue = new HeaderValue();
        Integer titleFontSize = 10;
        Integer subtitleFontSize = 10;
        headerValue.setTitleValue(buildTitleValue(
                title, titleFontSize,
                String.valueOf(FontFamily.ANEK_LATIN), FontWeight.bold,
                Colors.PrimaryHeaderThemeColor.toString())
        );
        headerValue.setSubTitleValue(buildTitleValue(
                subtitle, subtitleFontSize,
                String.valueOf(FontFamily.ANEK_LATIN),
                FontWeight.normal,
                Colors.Grey600.toString())
        );
        t.setValue(headerValue);
        return t;
    }

    public static TitleValue buildRenderableTitle(String titleText, Integer fontSize,
                                                  String fontFamily, FontWeight fontWeight, String color) {

        return buildTitleValue(titleText, fontSize, fontFamily, fontWeight, color);
    }

    public static TitleValue buildTitleValue(String titleText, Integer fontSize,
                                             String fontFamily, FontWeight fontWeight,
                                             String color, Integer lineHeight) {

        TitleValue titleValue = new TitleValue();
        TextStyle titleStyle = new TextStyle();

        titleStyle.setFontFamily(String.valueOf(fontFamily));
        titleStyle.setFontWeight(fontWeight);
        titleStyle.setFontSize(fontSize);
        titleStyle.setColor(color);
        titleValue.setText(titleText);
        titleValue.setStyle(titleStyle);
        titleStyle.setLineHeight(lineHeight);

        return titleValue;
    }

    public static TitleValue buildTitleValue(String titleText, Integer fontSize,
                                             String fontFamily, FontWeight fontWeight, String color) {

        TitleValue titleValue = new TitleValue();
        TextStyle titleStyle = new TextStyle();
        titleStyle.setFontFamily(String.valueOf(fontFamily));
        titleStyle.setFontWeight(fontWeight);
        titleStyle.setFontSize(fontSize);
        titleStyle.setColor(color);
        titleValue.setText(titleText);
        titleValue.setStyle(titleStyle);

        return titleValue;
    }

    public static Action buildAction(String ActionType, String url, LoginType loginType, String screenType) {
        Action action = new Action();
        action.setLoginType(loginType);
        action.setScreenType(screenType);
        action.setUrl(url);
        action.setType(ActionType);
        return action;
    }

}
