package com.flipkart.sm.pages.dp.widget.builders.creditscore.bottomsheets;

import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.fintech.profile.response.BureauDataResponse;
import com.flipkart.rome.datatypes.response.common.leaf.value.RichTextValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardSummaryListWidgetData;
import com.flipkart.sm.pages.dp.constants.Constants;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.provider.template.WidgetTemplateProvider;
import com.flipkart.fintech.pinaka.common.utils.GuiceInjector;
import com.flipkart.sm.pages.utils.WidgetUtils;
import java.text.DecimalFormat;
import java.util.Objects;


public class CreditUtilisationCreditScoreCardSummaryListWidgetBuilder extends WidgetBuilder<CardSummaryListWidgetData> {

    private static final String WIDGET_IDENTIFIER = "CREDIT_UTILIZATION_CREDIT_METRIC_CARD_SUMMARY_LIST_IDENTIFIER";
    private final WidgetTemplateProvider widgetTemplateProvider;
    private final BureauDataResponse bureauDataResponse;

    public CreditUtilisationCreditScoreCardSummaryListWidgetBuilder(WidgetInfo widgetInfo) {
        super(widgetInfo);
        this.widgetTemplateProvider = GuiceInjector.getInjector().getInstance(WidgetTemplateProvider.class);
        this.bureauDataResponse = WidgetUtils.getBureauData(widgetInfo);
    }

    @Override
    public CardSummaryListWidgetData getWidgetData() {
        CardSummaryListWidgetData cardSummaryListWidgetData = widgetTemplateProvider.getWidgetTemplate(
                WIDGET_IDENTIFIER,
                new TypeReference<CardSummaryListWidgetData>() {}
        );
        cardSummaryListWidgetData.getRenderableComponents().forEach(renderableComponent -> {
                    renderableComponent.getValue().getSuperTitle().setText(getValueForCard(renderableComponent.getValue().getTitle().getValue().getText()));
                    changeFontStyle(renderableComponent.getValue().getSuperTitle());
                }
        );
        return cardSummaryListWidgetData;
    }

    private String getValueForCard(String cardName) {
        switch (cardName) {
            case "Total credit limit":
                return "₹" +formatNumber(bureauDataResponse.getCreditLimit());
            case "Total utilised credit":
                return "₹" +formatNumber(bureauDataResponse.getCurrentBalance());
            default:
                return null;
        }
    }

    private String formatNumber(double value) {
        boolean isNegative = false;
        if (value < 0){
            value = value*-1;
            isNegative = true;
        }
        String result = "";
        if (value < 1000) {
            result = format(Constants.UNDER_THOUSAND_DECIMAL_PATTERN, value);
        } else {
            double hundreds = value % 1000;
            int other = (int) (value / 1000);
            result = format(",##", other) + ',' + format(Constants.OVER_THOUSAND_DECIMAL_FLOW, hundreds);
        }
        if (isNegative){
            result = "-" + result;
        }
        return  result;
    }

    private static String format(String pattern, Object value) {
        return new DecimalFormat(pattern).format(value);
    }

    private static void changeFontStyle(RichTextValue textValue){
        if(Objects.nonNull(textValue) && textValue.getText().length()>8){
            textValue.getStyle().setFontSize(16);
            textValue.getStyle().setLineHeight(20);
        }
    }

}
