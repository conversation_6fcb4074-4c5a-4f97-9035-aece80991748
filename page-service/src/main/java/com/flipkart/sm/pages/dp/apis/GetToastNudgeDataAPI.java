package com.flipkart.sm.pages.dp.apis;

import com.flipkart.aapi.multiwidget.annotations.DataAPI;
import com.flipkart.aapi.multiwidget.apis.AbstractDataAPI;
import com.flipkart.aapi.multiwidget.models.request.DataAPIRequest;
import com.flipkart.rome.datatypes.response.fintech.supermoney.enums.PageNudgeToastType;
import com.flipkart.rome.datatypes.response.fintech.supermoney.nudge.NudgeType;
import com.flipkart.rome.datatypes.response.fintech.supermoney.nudge.ToastNudgeData;
import com.flipkart.rome.datatypes.response.notifyMe.ToastType;
import com.flipkart.sm.pages.dp.constants.PageServiceDataAPIType;

@DataAPI(PageServiceDataAPIType.GET_TOAST_MESSAGE)
public class GetToastNudgeDataAPI extends AbstractDataAPI {
    private static final String NUDGE_ID_FORMATTER = "nudge-%s";

    public GetToastNudgeDataAPI(DataAPIRequest request) {
        super(request);
    }

    @Override
    public ToastNudgeData process() throws Exception {
        if(pageLevelToastNudgeEnabled(request) && !request.isRefetched()){
            return buildToastNudgeData(request);
        }
        return null;
    }


    public static boolean pageLevelToastNudgeEnabled(DataAPIRequest request) {
        return request.getQueryParams().containsKey("showToast") && request.getQueryParams().get("showToast").equalsIgnoreCase("true");
    }


    public static ToastNudgeData buildToastNudgeData(DataAPIRequest request) {
        PageNudgeToastType pageNudgeToastType = PageNudgeToastType.valueOf(request.getQueryParams().get("toastType"));
        return buildToastNudgeDataForPageNudgeToastType(pageNudgeToastType, request);
    }

    public static String createNudgeIdFromRequestId(DataAPIRequest request) {
        return String.format(NUDGE_ID_FORMATTER, request.getContext().getRequestId());
    }

    private static ToastNudgeData buildToastNudgeDataForPageNudgeToastType(PageNudgeToastType pageNudgeToastType, DataAPIRequest request) {

        switch (pageNudgeToastType) {
            default: {
                return null;
            }
        }
    }

    private static ToastNudgeData buildToastNudgeDataForEditDetailsChangedSuccessfully(DataAPIRequest request) {
        ToastNudgeData toastNudgeData = new ToastNudgeData();
        toastNudgeData.setToastType(ToastType.SUCCESS);
        toastNudgeData.setToastImage("");
        toastNudgeData.setToastMessage("Changes saved");
        toastNudgeData.setNudgeType(NudgeType.TOAST_NUDGE);
        toastNudgeData.setNudgeId(createNudgeIdFromRequestId(request));
        return toastNudgeData;
    }
}
