package com.flipkart.sm.pages.dp.widget.builderMappers;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.SubmitButtonWidgetData;
import com.flipkart.sm.pages.dp.constants.Constants;
import com.flipkart.sm.pages.dp.constants.PageType;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.builders.creditscore.CheckCreditScoreButtonWidgetBuilder;
import com.flipkart.sm.pages.dp.widget.builders.creditscore.ImproveCSButtionListWidgetBuilder;
import com.flipkart.sm.pages.dp.widget.builders.creditscore.RefreshCSSubmitWidgetBuilder;

public class SubmitButtonWidgetBuilderMapper {

    public static WidgetBuilder<SubmitButtonWidgetData> getWidgetBuilderByType(WidgetInfo widgetInfo) {
        PageType context = PageType.valueOf((String) widgetInfo.getSlotParams().get(Constants.TYPE));
        switch (context) {
            case CHECK_CREDIT_SCORE:
                return new CheckCreditScoreButtonWidgetBuilder(widgetInfo);
            case CREDIT_SCORE:
                return new RefreshCSSubmitWidgetBuilder(widgetInfo);
            case CREDIT_SCORE_HOW_TO_IMPROVE:
                return new ImproveCSButtionListWidgetBuilder(widgetInfo);
            default:
                throw new IllegalStateException("Unexpected value: " + context);
        }
    }
}
