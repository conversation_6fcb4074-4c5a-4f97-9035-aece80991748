package com.flipkart.sm.pages.dp.serde;

import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.flipkart.mobile.parser.vo.Device;

import java.io.IOException;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

public class DeviceSerde extends StdDeserializer<Device> {
    public DeviceSerde(Class<?> vc) {
        super(vc);
    }

    @Override
    public Device deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JacksonException {
        if (p == null) return null;
        JsonNode node = p.getCodec().readTree(p);
        if (node == null) return null;
        Map<String, String> map = new HashMap<>();
        for (Field field : Device.class.getFields()) {
            if (node.get(field.getName()) == null) continue;
            map.put(field.getName(), node.get(field.getName()).textValue());
        }
        return Device.fromMap(map);
    }
}
