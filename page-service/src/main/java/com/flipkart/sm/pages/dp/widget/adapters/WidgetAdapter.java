package com.flipkart.sm.pages.dp.widget.adapters;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.aapi.multiwidget.widgetadapter.AbstractWidgetAdapter;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.common.leaf.value.FooterValue;
import com.flipkart.rome.datatypes.response.common.leaf.value.HeaderValue;
import com.flipkart.rome.datatypes.response.page.v4.WidgetData;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;

import java.util.HashMap;
import java.util.Map;

public abstract class WidgetAdapter<W extends WidgetData> extends AbstractWidgetAdapter {

  WidgetBuilder<W> widgetBuilder;

  public WidgetAdapter(WidgetInfo widgetInfo, WidgetBuilder<W> widgetBuilder) {
    super(widgetInfo);
    this.widgetBuilder = widgetBuilder;
  }

  @Override
  public W getWidgetData(WidgetInfo widgetInfo) {
    return widgetBuilder.getWidgetData();
  }

  @Override
  protected RenderableComponent<HeaderValue> getRenderableHeaders() {
    return widgetBuilder.getRenderableHeaders();
  }

  @Override
  protected RenderableComponent<FooterValue> getRenderableFooters() {
    return widgetBuilder.getRenderableFooters();
  }

  @Override
  protected Map<String, String> getTrackingParamsForWidget() {
    Map<String, String> trackingParams = super.getTrackingParamsForWidget();
    if(trackingParams == null) trackingParams = new HashMap<>();
    trackingParams.putAll(
            widgetBuilder.getTrackingParamsForWidget()
    );
    return trackingParams;
  }
}
