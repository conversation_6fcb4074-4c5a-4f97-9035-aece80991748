
package com.flipkart.sm.pages.dp.widget.builderMappers;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardWidgetData;
import com.flipkart.sm.pages.dp.constants.Constants;
import com.flipkart.sm.pages.dp.constants.PageType;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.builders.creditscore.CSfromPartnerCardWidgetBuilder;
import com.flipkart.sm.pages.dp.widget.builders.creditscore.CashComingSoonCardWidgetBuilder;
import com.flipkart.sm.pages.dp.widget.builders.creditscore.bottomsheets.CreditScoreBottomSheetCardWidgetBuilder;
import com.flipkart.sm.pages.dp.widget.builders.creditscore.NoCSCardWidgetBuilder;

public class CardWidgetMapper {
    public static WidgetBuilder<CardWidgetData> getWidgetBuilderByType(WidgetInfo widgetInfo) {
        PageType context = PageType.valueOf((String) widgetInfo.getSlotParams().get(Constants.TYPE));
        switch (context) {
            case CREDIT_SCORE_NOTE_CARD:
                return new CreditScoreBottomSheetCardWidgetBuilder(widgetInfo);
            case NO_CS_AVAILABLE:
                return new NoCSCardWidgetBuilder(widgetInfo);
            case CS_FROM_PARTNER:
                return  new CSfromPartnerCardWidgetBuilder(widgetInfo);
            case COMING_SOON_CASH:
                return new CashComingSoonCardWidgetBuilder(widgetInfo);
            default:
                throw new IllegalStateException("Unexpected value: " + context);
        }
    }
}
