package com.flipkart.sm.pages.dp.widget.builders.creditscore;

import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.fintech.pinaka.common.utils.GuiceInjector;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.common.leaf.value.HeaderValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.MarkDownWidgetData;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.provider.template.WidgetTemplateProvider;

public class TncMarkDownWidgetBuilder extends WidgetBuilder<MarkDownWidgetData> {

    private static final String TEMPLATE_KEY = "EXPERIAN_TERMS_AND_CONDITIONS";
    private static final String HEADER_WIDGET_IDENTIFIER = "EXPERIAN_TnC_HEADER";
    private final WidgetTemplateProvider widgetTemplateProvider;

    public TncMarkDownWidgetBuilder(WidgetInfo widgetInfo) {
        super(widgetInfo);
        this.widgetTemplateProvider = GuiceInjector.getInjector().getInstance(WidgetTemplateProvider.class);
    }

    @Override
    public MarkDownWidgetData getWidgetData() {
        return widgetTemplateProvider.getWidgetTemplate(TEMPLATE_KEY, new TypeReference<MarkDownWidgetData>() {
        });
    }

    @Override
    public RenderableComponent<HeaderValue> getRenderableHeaders() {
        return widgetTemplateProvider.getWidgetTemplate(HEADER_WIDGET_IDENTIFIER, new TypeReference<RenderableComponent<HeaderValue>>() {
        });
    }
}

