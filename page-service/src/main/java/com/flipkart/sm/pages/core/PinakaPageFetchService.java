package com.flipkart.sm.pages.core;

import com.flipkart.dataprovider.models.controller.exceptions.PageFetchControllerException;
import com.flipkart.dataprovider.models.controller.request.PageFetchControllerRequest;
import com.flipkart.dataprovider.models.controller.request.SingleSourcePageFetchControllerRequest;
import com.flipkart.dataprovider.models.controller.response.ControllerViewResponse;

public interface PinakaPageFetchService {
    ControllerViewResponse fetchPage(PageFetchControllerRequest request) throws PageFetchControllerException;
    ControllerViewResponse fetchPageFromSingleSourceRequest(SingleSourcePageFetchControllerRequest request) throws PageFetchControllerException;
}
