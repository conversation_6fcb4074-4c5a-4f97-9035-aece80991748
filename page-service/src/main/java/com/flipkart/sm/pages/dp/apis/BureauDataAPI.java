package com.flipkart.sm.pages.dp.apis;

import com.flipkart.aapi.multiwidget.annotations.DataAPI;
import com.flipkart.aapi.multiwidget.apis.AbstractDataAPI;
import com.flipkart.aapi.multiwidget.models.request.DataAPIRequest;
import com.flipkart.fintech.profile.client.ProfileClient;
import com.flipkart.fintech.profile.response.BureauDataResponse;
import com.flipkart.sm.pages.config.PageServiceConfig;
import com.flipkart.sm.pages.dp.constants.PageServiceDataAPIType;
import com.flipkart.fintech.pinaka.common.utils.GuiceInjector;
import lombok.CustomLog;

@CustomLog
@DataAPI(PageServiceDataAPIType.BUREAU_DATA)
public class BureauDataAPI extends AbstractDataAPI {
    private final PageServiceConfig config;
    private final ProfileClient profileClient;

    public BureauDataAPI(DataAPIRequest request) {
        super(request);
        this.profileClient = GuiceInjector.getInjector().getInstance(ProfileClient.class);
        this.config = GuiceInjector.getInjector().getInstance(PageServiceConfig.class);
    }

    @Override
    public BureauDataResponse process() throws Exception {
        String accountId = request.getContext().getAccountId();
        return profileClient.getExistingCreditScoreSm(accountId);

    }

}