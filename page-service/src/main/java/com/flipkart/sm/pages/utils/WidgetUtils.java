package com.flipkart.sm.pages.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.fintech.profile.response.BureauDataResponse;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.common.leaf.value.FooterValue;
import com.flipkart.rome.datatypes.response.common.leaf.value.ImageValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.SubmitButtonValue;
import com.flipkart.sm.pages.dp.constants.Constants;
import com.flipkart.sm.pages.dp.constants.PageServiceDataAPIType;
import com.flipkart.sm.pages.dp.constants.PageType;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import com.supermoney.ams.bridge.utils.UrlUtil;
import lombok.CustomLog;
import org.apache.http.NameValuePair;
import org.jetbrains.annotations.NotNull;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@CustomLog
public class WidgetUtils {

    public static ApplicationDataResponse getApplicationData(WidgetInfo widgetInfo) {
        return (ApplicationDataResponse) widgetInfo.getData().get(PageServiceDataAPIType.WINTERFELL_APPLICATION);
    }

    public static BureauDataResponse getBureauData(WidgetInfo widgetInfo) {
        return (BureauDataResponse) widgetInfo.getData().get(PageServiceDataAPIType.BUREAU_DATA);
    }

    public static PageType getPageTypeContext(WidgetInfo widgetInfo) {
        return PageType.valueOf((String) widgetInfo.getSlotParams().get(Constants.TYPE));
    }


    @NotNull
    public static Map<String, Object> getParamsMapFromData(ApplicationDataResponse applicationDataResponse) {
        List<NameValuePair> pairList = UrlUtil.getQueryParams(applicationDataResponse);
        Map<String, Object> params = pairList.stream().collect(Collectors.toMap(NameValuePair::getName, NameValuePair::getValue));
        return params;
    }


    public static void setFooterImageInSubmitButton(String alternateText, String imageKey, SubmitButtonValue submitButtonValue){
        ImageValue imageValue = buildImageValue(alternateText, 40, 312, imageKey);
        FooterValue footerValue = new FooterValue();
        footerValue.setImageValue(imageValue);
        RenderableComponent<FooterValue> footerValueRenderableComponent = new RenderableComponent<>();
        footerValueRenderableComponent.setValue(footerValue);
        submitButtonValue.setFooterValue(footerValueRenderableComponent);
    }

    private static final String bankImagesMapJson = FileUtils.readFileasString(Constants.BANK_IMAGES_PATH);

    public static String getBankImage(String bankName) {
        Map<String, String> bankImageMap = null;
        try {
            bankImageMap = ObjectMapperUtil.get().readValue(bankImagesMapJson, new TypeReference<HashMap<String, String>>() {});
            return bankImageMap.getOrDefault(bankName, null);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public static ImageValue buildImageValue(String alternateText, Integer height, Integer width, String url) {

        ImageValue imageValue = new ImageValue();
        imageValue.setAlternateText(alternateText);
        imageValue.setHeight(height);
        imageValue.setWidth(width);
        imageValue.setDynamicImageUrl(url);
        imageValue.setSource(url);

        return imageValue;
    }

    public static ImageValue buildImageValue(
            String alternateText, Integer height, Integer width, String url, String aspectRatio) {

        ImageValue imageValue = new ImageValue();
        imageValue.setAlternateText(alternateText);
        imageValue.setHeight(height);
        imageValue.setWidth(width);
        imageValue.setDynamicImageUrl(url);
        imageValue.setSource(url);
        imageValue.setAspectRatio(aspectRatio);

        return imageValue;
    }
}
