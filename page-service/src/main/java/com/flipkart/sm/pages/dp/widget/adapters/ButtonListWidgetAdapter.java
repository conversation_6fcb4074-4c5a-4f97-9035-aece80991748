package com.flipkart.sm.pages.dp.widget.adapters;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.ButtonListWidgetData;
import com.flipkart.sm.pages.dp.widget.builderMappers.ButtonListWidgetBuilderMapper;
import lombok.CustomLog;

@CustomLog
@com.flipkart.aapi.multiwidget.annotations.WidgetAdapter(widgetType = WidgetTypeV4.BUTTON_LIST)
public class ButtonListWidgetAdapter extends WidgetAdapter<ButtonListWidgetData> {

  public ButtonListWidgetAdapter(WidgetInfo widgetInfo) {
    super(widgetInfo, ButtonListWidgetBuilderMapper.getWidgetBuilderByType(widgetInfo));
  }
}
