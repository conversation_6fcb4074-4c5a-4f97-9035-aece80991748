package com.flipkart.sm.pages.dp.widget.builders.creditscore;

import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.SubmitButtonWidgetData;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.provider.template.WidgetTemplateProvider;
import com.flipkart.fintech.pinaka.common.utils.GuiceInjector;


public class CheckCreditScoreButtonWidgetBuilder extends WidgetBuilder<SubmitButtonWidgetData> {
    private static final String WIDGET_IDENTIFIER = "CHECK_CREDIT_SCORE_BUTTON";


    private final WidgetTemplateProvider widgetTemplateProvider;

    public CheckCreditScoreButtonWidgetBuilder(WidgetInfo widgetInfo) {
        super(widgetInfo);
        this.widgetTemplateProvider = GuiceInjector.getInjector().getInstance(WidgetTemplateProvider.class);
    }

    @Override
    public SubmitButtonWidgetData getWidgetData() {

        return widgetTemplateProvider.getWidgetTemplate(WIDGET_IDENTIFIER, new TypeReference<SubmitButtonWidgetData>() {
        });
    }

}
