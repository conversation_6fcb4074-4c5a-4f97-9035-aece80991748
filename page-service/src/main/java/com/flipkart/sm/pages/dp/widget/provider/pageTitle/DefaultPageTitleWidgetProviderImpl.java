package com.flipkart.sm.pages.dp.widget.provider.pageTitle;

import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.PageTitleWidgetData;
import com.flipkart.sm.pages.config.PageTitleWidgetConfig;
import com.flipkart.sm.pages.dp.constants.PageType;
import com.flipkart.sm.pages.dp.widget.provider.config.WidgetConfigProvider;
import com.flipkart.sm.pages.utils.WidgetBuilderUtils;
import com.google.inject.Inject;
import com.google.inject.Singleton;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Singleton
public class DefaultPageTitleWidgetProviderImpl implements DefaultPageTitleWidgetProvider {
    private final static String CONFIG_KEY = "PAGE_TITLE_TEXT";
    private final Map<PageType, PageTitleWidgetData> titleWidgets;

    @Inject
    public DefaultPageTitleWidgetProviderImpl(WidgetConfigProvider widgetConfigProvider) {
        List<PageTitleWidgetConfig> pageTitleWidgetConfigs = widgetConfigProvider.getWidgetConfig(CONFIG_KEY, new TypeReference<List<PageTitleWidgetConfig>>() {});
        this.titleWidgets = new HashMap<>();
        pageTitleWidgetConfigs.forEach(pageTitleConfig -> this.titleWidgets.put(
                pageTitleConfig.getPageType(),
                WidgetBuilderUtils.buildPageTitleWidget(pageTitleConfig.getTitleText(), pageTitleConfig.getSubTitleText())
        ));
    }

    public PageTitleWidgetData getWidgetData(PageType pageType) {
        if (titleWidgets.containsKey(pageType)) {
            return titleWidgets.get(pageType);
        }
        throw new RuntimeException("PageTitle not found for PageType " + pageType);
    }

}