package com.flipkart.sm.pages.dp.apis;

import com.flipkart.aapi.multiwidget.annotations.DataAPI;
import com.flipkart.aapi.multiwidget.apis.AbstractDataAPI;
import com.flipkart.aapi.multiwidget.models.request.DataAPIRequest;
import com.flipkart.ams.WinterfellUtils;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.sm.pages.config.PageServiceConfig;
import com.flipkart.sm.pages.dp.constants.PageServiceDataAPIType;
import com.flipkart.sm.pages.exception.PageServiceException;
import com.flipkart.fintech.pinaka.common.utils.GuiceInjector;
import com.flipkart.sm.pages.utils.FileUtils;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.CustomLog;
import org.apache.commons.lang.StringUtils;

@CustomLog
@DataAPI(PageServiceDataAPIType.WINTERFELL_APPLICATION)
public class WinterfellApplicationDataAPI extends AbstractDataAPI {
    private static final String APPLICATION_ID_KEY = "applicationId";

    private final PageServiceConfig config;
    private final WinterfellUtils winterfellUtils;


    public WinterfellApplicationDataAPI(DataAPIRequest request) {
        super(request);
        this.winterfellUtils = GuiceInjector.getInjector().getInstance(WinterfellUtils.class);
        this.config = GuiceInjector.getInjector().getInstance(PageServiceConfig.class);
    }

    @Override
    public ApplicationDataResponse process() throws Exception {
        String accountId = request.getContext().getAccountId();
        String applicationId = request.getQueryParams().get(APPLICATION_ID_KEY);
        if(StringUtils.isEmpty(applicationId)) return null;
        ApplicationDataResponse applicationDataResponse = getApplicationDataResponse(applicationId, accountId);
        return applicationDataResponse;
    }

    private ApplicationDataResponse getApplicationDataResponse(String applicationId, String accountId) throws PageServiceException {
        try {
            if (config.isWinterfellMocked()) {
                String mockAppData = FileUtils.readFileasString("mock/application.json");
                return ObjectMapperUtil.get().readValue(mockAppData, ApplicationDataResponse.class);
            }
            return winterfellUtils.fetchApplicationData(applicationId, accountId);
        } catch (Exception e) {
            log.error("Failed to get ApplicationDataResponse with error {}", e.getMessage(), e);
            throw new PageServiceException(e);
        }
    }
}