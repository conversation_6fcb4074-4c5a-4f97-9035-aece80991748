package com.flipkart.sm.pages.dp.constants;

import com.flipkart.rome.datatypes.response.common.enums.FontWeight;
import com.flipkart.rome.datatypes.response.common.enums.TextAlign;
import com.flipkart.rome.datatypes.response.common.leaf.value.TextStyle;
import com.flipkart.sm.pages.dp.constants.style.Colors;
import com.flipkart.sm.pages.dp.constants.style.FontFamily;
import com.flipkart.sm.pages.dp.constants.style.FontSizes;
import com.flipkart.sm.pages.utils.StyleUtils;

public class StyleConstants {
    public static final TextStyle REVIEW_PAGE_CARD_STYLE = StyleUtils.buildTextStyle(Colors.Grey800,
            FontFamily.ANEK_LATIN,
            FontSizes.SIZE_16,
            FontWeight.medium,
            2);

    public static final TextStyle CARD_WIDGET_TITLE_TEXT_STYLE = StyleUtils.buildTextStyle(
            Colors.PrimaryHeaderThemeColor,
            FontSizes.SIZE_20,
            F<PERSON><PERSON>eight.bold,
            TextAlign.left,
            24
    );
}
