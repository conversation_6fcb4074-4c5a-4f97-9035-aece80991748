package com.flipkart.sm.pages.utils;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.flipkart.fintech.pinaka.api.response.deserializer.AccordianWidgetDeserializer;
import com.flipkart.mobile.parser.vo.AppDevice;
import com.flipkart.mobile.parser.vo.Device;
import com.flipkart.mobile.parser.vo.FkApp;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.AccordionWidgetData;
import com.flipkart.sm.pages.deserializer.AppDeviceDeserializer;
import com.flipkart.sm.pages.deserializer.DeviceDeserializer;
import com.flipkart.sm.pages.deserializer.FkAppDeserializer;

public class ObjectMapperProvider {
  private static final ObjectMapper objectMapper;

  static {
    objectMapper = new ObjectMapper();
    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    SimpleModule module = new SimpleModule();
    module.addDeserializer(AccordionWidgetData.class, new AccordianWidgetDeserializer());
    module.addDeserializer(AppDevice.class, new AppDeviceDeserializer());
    module.addDeserializer(Device.class, new DeviceDeserializer());
    module.addDeserializer(FkApp.class, new FkAppDeserializer());
    objectMapper.registerModule(module);
  }


  public static ObjectMapper get() {
    return objectMapper;
  }
}
