package com.flipkart.sm.pages.module;

import com.codahale.metrics.MetricRegistry;
import com.flipkart.aapi.multiwidget.module.MultiWidgetModule;
import com.flipkart.fintech.cryptex.config.DynamicBucketConfig;
import com.flipkart.fintech.pinaka.models.DatabaseConfig;
import com.flipkart.kloud.config.ConfigClient;
import com.flipkart.kloud.config.ConfigClientBuilder;
import com.flipkart.kloud.config.DynamicBucket;
import com.flipkart.kloud.config.error.ConfigServiceException;
import com.flipkart.sm.pages.config.PageServiceConfig;
import com.flipkart.sm.pages.core.PinakaPageFetchService;
import com.flipkart.sm.pages.core.PinakaPageFetchServiceImpl;
import com.flipkart.sm.pages.dp.page.config.CsHomePageConfigArgumentsResolver;
import com.flipkart.sm.pages.dp.page.config.IPageConfigArgumentsResolver;
import com.flipkart.sm.pages.dp.widget.provider.config.WidgetConfigProvider;
import com.flipkart.sm.pages.dp.widget.provider.config.WidgetConfigProviderImpl;
import com.flipkart.sm.pages.dp.widget.provider.pageTitle.DefaultPageTitleWidgetProvider;
import com.flipkart.sm.pages.dp.widget.provider.pageTitle.DefaultPageTitleWidgetProviderImpl;
import com.flipkart.sm.pages.dp.widget.provider.template.WidgetTemplateProvider;
import com.flipkart.sm.pages.dp.widget.provider.template.WidgetTemplateProviderImpl;
import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import com.google.inject.multibindings.MapBinder;
import com.google.inject.name.Named;
import com.supermoney.pages.configurations.PageManagementConfiguration;
import com.supermoney.pages.configurations.PageManagementDBConfiguration;
import com.supermoney.pages.module.PagesModule;
import lombok.CustomLog;

import java.util.Collections;

@CustomLog
public class PageServiceModule extends AbstractModule {

    private final PageServiceConfig pageServiceConfig;
    private final MetricRegistry pageServiceMetric;

    public PageServiceModule(
            PageServiceConfig pageServiceConfig,
            DatabaseConfig databaseConfig,
            MetricRegistry pageServiceMetric
    ) {
        this.pageServiceConfig = pageServiceConfig;
        this.pageServiceConfig.setDatabaseConfig(databaseConfig);
        this.pageServiceMetric = pageServiceMetric;

    }

    @Override
    protected void configure() {
        com.flipkart.aapi.multiwidget.configuration.Configuration multiWidgetConfiguration =
                com.flipkart.aapi.multiwidget.configuration.Configuration.builder()
                        .noOfThreads(30)
                        .noOfWidgetAdapterThreads(20)
                        .dataAPIPackages(Collections.singletonList(pageServiceConfig.getDataAPIPath()))
                        .widgetAdapterPackages(Collections.singletonList(pageServiceConfig.getWidgetAdapterPath()))
                        .build();
        install(new MultiWidgetModule(multiWidgetConfiguration));
        install(new PagesModule(getPageManagementConfiguration()));
        bind(WidgetTemplateProvider.class).toInstance(new WidgetTemplateProviderImpl(pageServiceConfig));
        bind(WidgetConfigProvider.class).toInstance(new WidgetConfigProviderImpl(pageServiceConfig));
        bind(DefaultPageTitleWidgetProvider.class).to(DefaultPageTitleWidgetProviderImpl.class);
        bind(PinakaPageFetchService.class).to(PinakaPageFetchServiceImpl.class);
        bindIPageConfigArgumentsResolver();
    }

    void bindIPageConfigArgumentsResolver(){
        MapBinder<String, IPageConfigArgumentsResolver> mapbinder = MapBinder.newMapBinder(binder(), String.class, IPageConfigArgumentsResolver.class);
        mapbinder.addBinding("/creditscore/homepage").to(CsHomePageConfigArgumentsResolver.class);
    }

    @Provides
    @Singleton
    @Named("pageServiceMetric")
    private MetricRegistry provideMetricRegistry() {
        return this.pageServiceMetric;
    }

    @Provides
    @Singleton
    @Named("pageServiceStaticResources")
    public DynamicBucket providesCbcUpswingStaticResourcesBucketHelper() throws ConfigServiceException {
        return getDynamicBucket(pageServiceConfig.getPageServiceStaticResourcesBucket());
    }

    @Provides
    @Singleton
    @Named("featureFlagConfigBucket")
    public DynamicBucket providesFeatureFlagConfigBucket() throws ConfigServiceException {
        return getDynamicBucket(pageServiceConfig.getFeatureFlagConfigBucket());
    }

    private static DynamicBucket getDynamicBucket(DynamicBucketConfig bucketConfig)
            throws ConfigServiceException {
        ConfigClient configClient = new ConfigClientBuilder().build();
        return configClient.getDynamicBucket(bucketConfig.getBucketName());
    }

    private PageManagementConfiguration getPageManagementConfiguration(){
        PageManagementDBConfiguration dbConfiguration = new PageManagementDBConfiguration();
        dbConfiguration.setPassword(pageServiceConfig.getDatabaseConfig().getEncryptedPassword());
        dbConfiguration.setUsername(pageServiceConfig.getDatabaseConfig().getEncryptedUser());
        dbConfiguration.setConnectionUrl(pageServiceConfig.getDatabaseConfig().getEncryptedUrl());
        dbConfiguration.setDriverClassName(pageServiceConfig.getDatabaseConfig().getDriverClass());
        PageManagementConfiguration pageManagementConfiguration = new PageManagementConfiguration();
        pageManagementConfiguration.setPageManagementDBConfiguration(dbConfiguration);
        pageManagementConfiguration.setResolver(pageServiceConfig.getPageConfigResolverName());
        pageManagementConfiguration.setPollingInterval(pageServiceConfig.getPageConfigPollingInterval());
        return pageManagementConfiguration;
    }
}
