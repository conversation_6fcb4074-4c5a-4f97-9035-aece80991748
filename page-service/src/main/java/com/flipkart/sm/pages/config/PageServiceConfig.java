package com.flipkart.sm.pages.config;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.cryptex.config.DynamicBucketConfig;
import com.flipkart.fintech.pinaka.models.DatabaseConfig;
import com.flipkart.fintech.pinaka.models.FeatureFlagConfig;
import com.supermoney.pages.configurations.PageManagementDBConfiguration;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class PageServiceConfig {
    @JsonProperty("isWinterfellMocked")
    private boolean winterfellMocked = false;
    private DynamicBucketConfig pageServiceStaticResourcesBucket;
    private DynamicBucketConfig featureFlagConfigBucket;

    private String dataAPIPath = "com.flipkart.sm.pages.dp.apis";
    private String widgetAdapterPath = "com.flipkart.sm.pages.dp.widget.adapters";
    private Map<String, String> widgetTemplateMap;
    private Map<String, String> widgetConfigMap;

    private String pageConfigResolverName;
    private Integer pageConfigPollingInterval = 24*60;
    private DatabaseConfig databaseConfig;
}
