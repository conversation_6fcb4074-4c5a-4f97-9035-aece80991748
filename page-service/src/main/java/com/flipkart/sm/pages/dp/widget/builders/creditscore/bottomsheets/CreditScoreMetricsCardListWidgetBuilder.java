package com.flipkart.sm.pages.dp.widget.builders.creditscore.bottomsheets;

import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.fintech.profile.response.BureauDataResponse;
import com.flipkart.rome.datatypes.response.common.leaf.RenderableComponent;
import com.flipkart.rome.datatypes.response.common.leaf.value.HeaderValue;
import com.flipkart.rome.datatypes.response.fintech.supermoney.cards.PrimitiveCard;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.CardSummaryListWidgetData;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.provider.template.WidgetTemplateProvider;
import com.flipkart.fintech.pinaka.common.utils.GuiceInjector;
import com.flipkart.sm.pages.utils.WidgetUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;

public class CreditScoreMetricsCardListWidgetBuilder extends WidgetBuilder<CardSummaryListWidgetData> {
    private static final String WIDGET_IDENTIFIER = "CREDIT_SCORE_METRICS_CARDS";
    private static final String HEADER_WIDGET_IDENTIFIER = "CREDIT_SCORE_METRICS_TITLE";
    private final WidgetTemplateProvider widgetTemplateProvider;
    private final BureauDataResponse bureauDataResponse;

    public CreditScoreMetricsCardListWidgetBuilder(WidgetInfo widgetInfo) {
        super(widgetInfo);
        this.widgetTemplateProvider = GuiceInjector.getInjector().getInstance(WidgetTemplateProvider.class);
        this.bureauDataResponse = WidgetUtils.getBureauData(widgetInfo);
    }

    @Override
    public RenderableComponent<HeaderValue> getRenderableHeaders() {
        return widgetTemplateProvider.getWidgetTemplate(HEADER_WIDGET_IDENTIFIER, new TypeReference<RenderableComponent<HeaderValue>>() {
        });
    }

    @Override
    public CardSummaryListWidgetData getWidgetData() {
        CardSummaryListWidgetData cardSummaryListWidgetData = widgetTemplateProvider.getWidgetTemplate(WIDGET_IDENTIFIER, new TypeReference<CardSummaryListWidgetData>() {
        });
        List<RenderableComponent<PrimitiveCard>> renderableComponentsList = new ArrayList<>();

        if (Objects.nonNull(bureauDataResponse)) {
            addRenderableComponent(bureauDataResponse.getOnTimePayment(), cardSummaryListWidgetData, renderableComponentsList, 0, this::getOnTimePayments);
            addRenderableComponent(bureauDataResponse.getCreditUsage(), cardSummaryListWidgetData, renderableComponentsList, 1, this::getCreditUtilization);
            addRenderableComponent(bureauDataResponse.getCreditAge(), cardSummaryListWidgetData, renderableComponentsList, 2, bureauDataResponse::getCreditAge);
            addRenderableComponent(bureauDataResponse.getLast180DaysCreditEnquiries(), cardSummaryListWidgetData, renderableComponentsList, 3,
                    () -> String.valueOf(bureauDataResponse.getLast180DaysCreditEnquiries()));
            addRenderableComponent(bureauDataResponse.getCreditMix(), cardSummaryListWidgetData, renderableComponentsList, 4, bureauDataResponse::getCreditMix);
        }

        cardSummaryListWidgetData.setRenderableComponents(renderableComponentsList);
        return cardSummaryListWidgetData;
    }

    private void addRenderableComponent(Object condition, CardSummaryListWidgetData cardSummaryListWidgetData,
                                        List<RenderableComponent<PrimitiveCard>> renderableComponentsList, int index, Supplier<String> textSupplier) {
        if (Objects.nonNull(condition)) {
            RenderableComponent<PrimitiveCard> component = cardSummaryListWidgetData.getRenderableComponents().get(index);
            component.getValue().getSuperTitle().setText(textSupplier.get());
            renderableComponentsList.add(component);
        }
    }

    String getCreditUtilization() {
        long creditUsage = Math.round(bureauDataResponse.getCreditUsage());
        String result = creditUsage + "%";
        return result;
    }

    String getOnTimePayments() {
        long ontimePayment = Math.round(bureauDataResponse.getOnTimePaymentPer());
        String result = ontimePayment + "%";
        return result;
    }
}
