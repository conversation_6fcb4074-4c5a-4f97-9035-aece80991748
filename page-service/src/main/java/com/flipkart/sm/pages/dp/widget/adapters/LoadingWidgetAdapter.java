package com.flipkart.sm.pages.dp.widget.adapters;


import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.LoadingWidgetData;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import com.flipkart.sm.pages.dp.widget.builderMappers.LoadingWidgetMapper;
import lombok.CustomLog;

@CustomLog
@com.flipkart.aapi.multiwidget.annotations.WidgetAdapter(widgetType = WidgetTypeV4.LOADING_WIDGET)
public class LoadingWidgetAdapter extends WidgetAdapter<LoadingWidgetData> {

    public LoadingWidgetAdapter(WidgetInfo widgetInfo) {
        super(widgetInfo, LoadingWidgetMapper.getWidgetBuilderByType(widgetInfo));
    }
}
