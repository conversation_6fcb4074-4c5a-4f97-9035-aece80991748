package com.flipkart.sm.pages.dp.widget.builders.creditscore;

import com.fasterxml.jackson.core.type.TypeReference;
import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.ButtonListWidgetData;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.provider.template.WidgetTemplateProvider;
import com.flipkart.fintech.pinaka.common.utils.GuiceInjector;

public class NoCsButtonWidgetBuilder extends WidgetBuilder<ButtonListWidgetData> {
    private static final String WIDGET_IDENTIFIER = "NO_CS_BUTTON";

    private final WidgetTemplateProvider widgetTemplateProvider;

    public NoCsButtonWidgetBuilder(WidgetInfo widgetInfo) {
        super(widgetInfo);
        this.widgetTemplateProvider = GuiceInjector.getInjector().getInstance(WidgetTemplateProvider.class);
    }

    @Override
    public ButtonListWidgetData getWidgetData() {
        return widgetTemplateProvider.getWidgetTemplate(WIDGET_IDENTIFIER,
                new TypeReference<ButtonListWidgetData>() {
                });
    }
}
