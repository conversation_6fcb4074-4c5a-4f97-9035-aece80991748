package com.flipkart.sm.pages.dp.widget.builderMappers;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.fintech.supermoney.widgets.SmFooterWidgetData;
import com.flipkart.sm.pages.dp.constants.Constants;
import com.flipkart.sm.pages.dp.constants.PageType;
import com.flipkart.sm.pages.dp.widget.builders.WidgetBuilder;
import com.flipkart.sm.pages.dp.widget.builders.creditscore.PoweredByExperianFooterWidgetBuilder;
import com.flipkart.sm.pages.dp.widget.builders.creditscore.PoweredByUPIFooterWidgetBuilder;

public class SmFooterWidgetMapper {
    public static WidgetBuilder<SmFooterWidgetData> getWidgetBuilderByType(WidgetInfo widgetInfo) {
        PageType context = PageType.valueOf((String) widgetInfo.getSlotParams().get(Constants.TYPE));
        switch (context) {
            case EXPERIAN_FOOTER:
                return new PoweredByExperianFooterWidgetBuilder(widgetInfo);
            case POWERED_BY_UPI_FOOTER:
                return new PoweredByUPIFooterWidgetBuilder(widgetInfo);

        }
        return null;
    }
}
