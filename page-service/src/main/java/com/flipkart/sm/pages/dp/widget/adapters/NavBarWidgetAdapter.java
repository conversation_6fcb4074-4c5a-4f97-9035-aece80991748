package com.flipkart.sm.pages.dp.widget.adapters;

import com.flipkart.aapi.multiwidget.models.request.WidgetInfo;
import com.flipkart.rome.datatypes.response.page.v4.WidgetTypeV4;
import com.flipkart.rome.datatypes.response.page.v4.widgetData.NavigationBarWidgetData;
import com.flipkart.sm.pages.dp.widget.builderMappers.NavBarWidgetMapper;
import lombok.CustomLog;

@CustomLog
@com.flipkart.aapi.multiwidget.annotations.WidgetAdapter(widgetType = WidgetTypeV4.NAVIGATION_BAR_WIDGET)
public class NavBarWidgetAdapter extends WidgetAdapter<NavigationBarWidgetData> {
    public NavBarWidgetAdapter(WidgetInfo widgetInfo) {
        super(widgetInfo, NavBarWidgetMapper.getWidgetBuilderByType(widgetInfo));
    }
}
