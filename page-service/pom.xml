<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.flipkart.fintech</groupId>
        <artifactId>pinaka</artifactId>
        <version>3.4.15-SM</version>
    </parent>

    <groupId>com.flipkart.sm.pages</groupId>
    <artifactId>page-service</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>2.15.2</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>fintech-cryptex-bundle</artifactId>
            <version>1.1.3-SM-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>io.dropwizard</groupId>
            <artifactId>dropwizard-db</artifactId>
            <version>2.0.4</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>io.dropwizard</groupId>
            <artifactId>dropwizard-hibernate</artifactId>
            <version>2.0.4</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>13.0</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>fintech-logger</artifactId>
            <version>${fintech.logger.version}</version>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>pinaka-api</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-annotations</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.datatype</groupId>
                    <artifactId>jackson-jackson-datatype-jsr310</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.aapi</groupId>
                    <artifactId>models-aapi</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.affordability</groupId>
                    <artifactId>abb-models</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.affordability</groupId>
                    <artifactId>affordability-service-clients</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.aapi</groupId>
                    <artifactId>multiwidget</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>ams-bridge</artifactId>
            <version>${project.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.flipkart.affordability</groupId>
                    <artifactId>abb-models</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.affordability</groupId>
                    <artifactId>affordability-service-clients</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>ams-connector</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.flipkart.aapi</groupId>
            <artifactId>models-aapi</artifactId>
            <version>${aapi.models.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.flipkart.affordability</groupId>
                    <artifactId>abb-models</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.affordability</groupId>
                    <artifactId>affordability-service-clients</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.supermoney</groupId>
            <artifactId>page-management</artifactId>
            <version>1.2.1</version>
        </dependency>
        <dependency>
            <groupId>com.flipkart.aapi</groupId>
            <artifactId>multiwidget</artifactId>
            <version>${aapi.multiwidget.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.flipkart.affordability</groupId>
                    <artifactId>abb-models</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.affordability</groupId>
                    <artifactId>affordability-service-clients</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.flipkart.aapi</groupId>
            <artifactId>dataprovider-core</artifactId>
            <version>${dataprovider.core.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.flipkart.security</groupId>
                    <artifactId>cryptex-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.aapi</groupId>
                    <artifactId>models-aapi</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.rome</groupId>
                    <artifactId>datasources</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.rome</groupId>
                    <artifactId>datatypes</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.fasterxml.jackson.datatype</groupId>
                    <artifactId>jackson-datatype-jdk8</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.cp</groupId>
                    <artifactId>oms-minions-models</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.cp</groupId>
                    <artifactId>oms-models</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.affordability</groupId>
                    <artifactId>abb-models</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.affordability</groupId>
                    <artifactId>affordability-service-clients</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.primus</groupId>
                    <artifactId>primus-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.nimbusds</groupId>
                    <artifactId>nimbus-jose-jwt</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.netflix.hystrix</groupId>
                    <artifactId>hystrix-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.google.protobuf</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>io.grpc</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.smoketurner</groupId>
            <artifactId>dropwizard-swagger</artifactId>
            <version>${dropwizard.swagger.version}</version>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>winterfell-client</artifactId>
            <version>${winterfell.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>2.15.2</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech.pinaka.common</groupId>
            <artifactId>pinaka-common</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.flipkart.fintech</groupId>
                    <artifactId>pinaka-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.fintech</groupId>
                    <artifactId>pinaka-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.fintech</groupId>
                    <artifactId>profile-service</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.flipkart.fintech</groupId>
                    <artifactId>winterfell-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>RELEASE</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>2.10.2</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.flipkart.fintech</groupId>
            <artifactId>profile-service-client</artifactId>
            <version>${project.version}</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>
