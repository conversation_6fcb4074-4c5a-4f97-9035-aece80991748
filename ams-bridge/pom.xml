<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>pinaka</artifactId>
    <groupId>com.flipkart.fintech</groupId>
    <version>3.4.15-SM</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>

  <artifactId>ams-bridge</artifactId>

  <properties>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <json-schema-validator.version>2.0.0</json-schema-validator.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
      <version>2.10.2</version>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.30</version>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.flipkart.fintech</groupId>
      <artifactId>pinaka-api</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.flipkart.fintech</groupId>
      <artifactId>pandora-service-client</artifactId>
      <version>${pandora.version}</version>
      <exclusions>
        <exclusion>
          <groupId>org.glassfish.jersey.media</groupId>
          <artifactId>jersey-media-multipart</artifactId>
        </exclusion>
        <exclusion>
          <groupId>javax.validation</groupId>
          <artifactId>validation-api</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.flipkart.affordability</groupId>
          <artifactId>affordability-service-clients</artifactId>
        </exclusion>
        <exclusion>
          <groupId>io.dropwizard</groupId>
          <artifactId>dropwizard-jersey</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.glassfish.hk2.external</groupId>
          <artifactId>aopalliance-repackaged</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-annotations</artifactId>
        </exclusion>
        <exclusion>
          <groupId>org.glassfish.jersey.core</groupId>
          <artifactId>jersey-client</artifactId>
        </exclusion>
        <exclusion>
          <groupId>net.minidev</groupId>
          <artifactId>json-smart</artifactId>
        </exclusion>
        <exclusion>
          <groupId>com.github.rholder</groupId>
          <artifactId>guava-retrying</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.github.fge</groupId>
      <artifactId>json-schema-validator</artifactId>
      <version>${json-schema-validator.version}</version>
    </dependency>
    <dependency>
      <groupId>com.flipkart.fintech</groupId>
      <artifactId>fintech-schema-validator</artifactId>
      <version>1.1.0-SM</version>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.26</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.flipkart.fintech</groupId>
      <artifactId>fintech-logger</artifactId>
      <version>${fintech.logger.version}</version>
    </dependency>
      <dependency>
          <groupId>com.flipkart.fintech</groupId>
          <artifactId>pandora-client</artifactId>
          <version>${pandora.version}</version>
          <scope>compile</scope>
      </dependency>
  </dependencies>

</project>