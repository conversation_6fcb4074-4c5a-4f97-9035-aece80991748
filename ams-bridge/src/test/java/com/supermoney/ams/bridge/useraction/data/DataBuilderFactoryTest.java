package com.supermoney.ams.bridge.useraction.data;

import com.flipkart.fintech.pandora.client.PandoraClientConfiguration;
import com.flipkart.fintech.pandora.client.PlClient;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.FormSubmitRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.MandateSubmitRequest;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static com.supermoney.ams.bridge.utils.FileUtils.readType;

@RunWith(MockitoJUnitRunner.class)
public class DataBuilderFactoryTest {

    @Mock
    PlClient plClient;

    @Before
    public void setUp() throws Exception{
    }
    @Test
    public void getTest() {
        DataBuilderFactory factory = new DataBuilderFactory(plClient);
        MandateSubmitRequest mandateSubmitRequest = readType("ams/mandate_response_success.json", MandateSubmitRequest.class);
        Assert.assertTrue(factory.get(mandateSubmitRequest) instanceof MandateDataBuilder);

        FormSubmitRequest formSubmitRequest = readType("ams/form1.json", FormSubmitRequest.class);
        Assert.assertTrue(factory.get(formSubmitRequest) instanceof FormDataBuilder);
    }

}