package com.supermoney.ams.bridge.utils;

import com.fasterxml.jackson.core.type.TypeReference;

import java.io.IOException;
import java.io.InputStream;

public class FileUtils {

  public static <T> T readType(String fileName, Class<T> type) {
    try {
      InputStream inputStream = FileUtils.class.getClassLoader().getResourceAsStream(fileName);
      return ObjectMapperUtil.get().readValue(inputStream, type);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

  public static <T> T readTypeFromTypeReference(String fileName, TypeReference<T> type) {
    try {
      InputStream inputStream = FileUtils.class.getClassLoader().getResourceAsStream(fileName);
      return ObjectMapperUtil.get().readValue(inputStream, type);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

}
