package com.supermoney.ams.bridge.interaction;

import static org.junit.Assert.*;

import com.supermoney.ams.bridge.dao.FileBasedConfigurationDao;
import com.supermoney.ams.bridge.models.ApplicationState;
import com.supermoney.ams.bridge.models.interaction.FormInteractionConfig;
import com.supermoney.ams.bridge.models.InteractionConfig;
import org.junit.Before;
import org.junit.Test;

public class InteractionResolverTest {
  InteractionResolver interactionResolver;

  @Before
  public void setUp() throws Exception {
    FileBasedConfigurationDao configurationDao = new FileBasedConfigurationDao();
    interactionResolver = new InteractionResolverImpl(configurationDao);
  }

  @Test
  public void taskKeyBasedInteraction() {
    ApplicationState applicationState = ApplicationState.builder()
        .applicationType("PERSONAL_LOAN_TEST")
        .applicationState("some state")
        .taskKey("FORM0")
        .build();
    InteractionConfig interaction = interactionResolver.getInteraction(applicationState);
    boolean isInternalPage = interaction instanceof FormInteractionConfig;
    assertTrue("InternalPage", isInternalPage);
    FormInteractionConfig internalPageInteractionConfig = (FormInteractionConfig) interaction;
    assertEquals("/ams/1/basic-details", internalPageInteractionConfig.getPageUrl());
  }

  @Test
  public void applicationStateBased() {
    ApplicationState applicationState = ApplicationState.builder()
        .applicationType("PERSONAL_LOAN_TEST")
        .applicationState("POLL_OFFER")
        .taskKey("")
        .build();
    InteractionConfig interaction = interactionResolver.getInteraction(applicationState);
    boolean isInternalPage = interaction instanceof FormInteractionConfig;
    assertTrue("InternalPage", isInternalPage);
    FormInteractionConfig internalPageInteractionConfig = (FormInteractionConfig) interaction;
    assertEquals("/ams/1/loading-screen", internalPageInteractionConfig.getPageUrl());
  }

  @Test
  public void missingTask() {
    ApplicationState applicationState = ApplicationState.builder()
        .applicationType("PERSONAL_LOAN_TEST")
        .applicationState("POLL_OFFER")
        .taskKey("SOME_TASK")
        .build();
    InteractionConfig interaction = interactionResolver.getInteraction(applicationState);
    boolean isInternalPage = interaction instanceof FormInteractionConfig;
    assertTrue("InternalPage", isInternalPage);
    FormInteractionConfig internalPageInteractionConfig = (FormInteractionConfig) interaction;
    assertEquals("/ams/1/missing-page", internalPageInteractionConfig.getPageUrl());
  }
}