package com.supermoney.ams.bridge.useraction.data;

import static com.supermoney.ams.bridge.utils.FileUtils.readType;
import static org.junit.Assert.*;

import com.flipkart.fintech.pinaka.api.request.v6.useraction.FormSubmitRequest;
import com.supermoney.ams.bridge.exceptions.InvalidPayloadException;
import java.util.Map;
import org.junit.Test;

public class FormDataBuilderTest {

  @Test
  public void correct() {
    FormSubmitRequest formSubmitRequest = readFormSubmitRequest("ams/form1.json");
    Map<String, Object> data = new FormDataBuilder(formSubmitRequest).build();
    assertEquals(4, data.size());
  }

  @Test(expected = InvalidPayloadException.class)
  public void inCorrect() {
    FormSubmitRequest formSubmitRequest = readFormSubmitRequest("ams/form1.json");
    formSubmitRequest.getFormData().put("consentData", "some data to clash");
    Map<String, Object> data = new FormDataBuilder(formSubmitRequest).build();
  }

  private FormSubmitRequest readFormSubmitRequest(String fileName) {
    Class<FormSubmitRequest> type = FormSubmitRequest.class;
    return readType(fileName, type);
  }

}