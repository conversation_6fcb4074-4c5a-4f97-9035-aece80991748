package com.supermoney.ams.bridge.utils;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.supermoney.ams.bridge.exceptions.InvalidPayloadException;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.security.InvalidAlgorithmParameterException;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.spec.AlgorithmParameterSpec;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Arrays;
import java.util.Base64;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import lombok.CustomLog;
import org.apache.commons.lang.StringUtils;


@CustomLog
public class EncryptionUtil {
    private EncryptionUtil() {

    }
    private static final String CS5_PADDING   = "AES/CBC/PKCS5PADDING";
    private static final String ECB_MODE      = "AES/ECB/PKCS5PADDING";
    private static final String AES_ALGORITHM = "AES";
    private static final String UTF_8         = "UTF-8";

    public static String encryptWithAesCS5(String plainText, String encryptionKey, byte[] iv) throws Exception {
        if (StringUtils.isEmpty(plainText)) return "";
        AlgorithmParameterSpec paramSpec = new IvParameterSpec(iv);
        // Generate a secret key from the hex string as key
        SecretKeySpec skeySpec = getSecretKeySpecFromHexString(AES_ALGORITHM, encryptionKey);
        // Creating a cipher instance with the AES_ALGORITHM and padding
        Cipher cipher = Cipher.getInstance(CS5_PADDING);
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec, paramSpec);
        // generating the encrypted result
        byte[] encrypted = cipher.doFinal(plainText.getBytes(UTF_8));
        // To add iv in encrypted string.
        byte[] encryptedWithIV = copyIVAndCipher(encrypted, iv);
        return Base64.getEncoder().encodeToString(encryptedWithIV);
    }

    public static String decryptWithAesCS5(String ciphertext, String encryptionKey) throws Exception {
        if (StringUtils.isEmpty(ciphertext)) return "";
        SecretKeySpec skeySpec = getSecretKeySpecFromHexString(AES_ALGORITHM, encryptionKey);
        byte[] encryptedIVandTextAsBytes = Base64.getDecoder().decode(ciphertext);
        // First 16 bytes are always the IV
        byte[] iv = Arrays.copyOf(encryptedIVandTextAsBytes, 16);
        byte[] ciphertextByte = Arrays.copyOfRange(encryptedIVandTextAsBytes, 16, encryptedIVandTextAsBytes.length);
        // Decrypt the message
        Cipher cipher = Cipher.getInstance(CS5_PADDING);
        cipher.init(Cipher.DECRYPT_MODE, skeySpec, new IvParameterSpec(iv));
        byte[] decryptedTextBytes = cipher.doFinal(ciphertextByte);
        return new String(decryptedTextBytes, UTF_8);
    }

    private static SecretKeySpec getSecretKeySpecFromHexString(String algoCommonName, String hexString) {
        byte[] encodedBytes = hexStrToByteArray(hexString);
        return new SecretKeySpec(encodedBytes, algoCommonName);
    }

    private static byte[] hexStrToByteArray(String hex) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream(hex.length() / 2);

        for (int i = 0; i < hex.length(); i += 2) {
            String output = hex.substring(i, i + 2);
            int decimal = Integer.parseInt(output, 16);
            baos.write(decimal);
        }
        return baos.toByteArray();
    }

    private static byte[] copyIVAndCipher(byte[] encryptedText, byte[] iv) throws Exception {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        os.write(iv);
        os.write(encryptedText);
        return os.toByteArray();
    }

    public static String decryptWithAes(String encryptedText, String encryptionKey) {
        String decryptedText = "";
        try {
            Cipher        cipher    = Cipher.getInstance(ECB_MODE);
            byte[]        key       = encryptionKey.getBytes(UTF_8);
            SecretKeySpec secretKey = new SecretKeySpec(key, AES_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, secretKey);
            Base64.Decoder decoder    = Base64.getDecoder();
            byte[]         cipherText = decoder.decode(encryptedText.getBytes(UTF_8));
            decryptedText = new String(cipher.doFinal(cipherText), UTF_8);

        } catch (Exception E) {
            log.error("Exception in decryption:{} ", E.getMessage());
        }

        return decryptedText;
    }

    public static String encryptWithAes(final String text, final String encryptionKey) {
        if (StringUtils.isEmpty(text)) {
            return null;
        }
        byte[] key = encryptionKey.getBytes(StandardCharsets.UTF_8);
        try {
            byte[] cipherText = encrypt(key, key, text.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(cipherText);
        } catch (InvalidPayloadException e) {
            log.error("Failed to encrypt data");
        }

        return PinakaConstants.EMPTY_STRING;
    }

    public static String decryptAesCbc(final String text, final String encryptionKey) {
        if (StringUtils.isEmpty(text)) {
            return null;
        }
        byte[] key = encryptionKey.getBytes(StandardCharsets.UTF_8);
        try {
            return new String(decrypt(key, key, Base64.getDecoder().decode(text)));
        } catch (InvalidPayloadException e) {
            log.error("Failed to decrypt data");
        }

        return PinakaConstants.EMPTY_STRING;
    }

    private static byte[] decrypt(final byte[] key, final byte[] ivParameterSpec, final byte[] message) {
        return encryptDecrypt(Cipher.DECRYPT_MODE, key, ivParameterSpec, message);
    }

    private static byte[] encrypt(final byte[] key, final byte[] ivParameterSpec, final byte[] message) {
        return encryptDecrypt(Cipher.ENCRYPT_MODE, key, ivParameterSpec, message);
    }

    private static byte[] encryptDecrypt(final int mode, final byte[] key, final byte[] ivParameterSpec, final byte[] message) {
        try {
            final Cipher cipher = Cipher.getInstance(CS5_PADDING);
            final SecretKeySpec keySpec = new SecretKeySpec(key, AES_ALGORITHM);
            final IvParameterSpec ivSpec = new IvParameterSpec(ivParameterSpec);
            cipher.init(mode, keySpec, ivSpec);

            return cipher.doFinal(message);
        } catch (NoSuchAlgorithmException | InvalidKeyException | InvalidAlgorithmParameterException |
                NoSuchPaddingException | BadPaddingException | IllegalBlockSizeException e) {
            log.error("Failed to encrypt/decrypt data", e);
            throw new InvalidPayloadException("Failed to encrypt/decrypt data");
        }
    }



    @Timed
    @ExceptionMetered
    public static SecretKey decryptSymmetricKey(String encryptedSymmetricKey, String privateKeyString) {
        PrivateKey privateKey = getPKCS8EncodedRSAPrivateKeyFromString(privateKeyString);
        SecretKey symmetricKey;
        byte[] symmetricKeyBytes;
        try {
            Cipher cipher = Cipher.getInstance("RSA/ECB/PKCS1Padding");
            cipher.init(Cipher.DECRYPT_MODE, privateKey);
            symmetricKeyBytes = cipher.doFinal(Base64.getDecoder().decode(encryptedSymmetricKey.getBytes()));
        } catch (Exception e) {
            log.error("Unable to decrypt", e);
            throw new InvalidPayloadException("Unable to decrypt");
        }
        symmetricKey = new SecretKeySpec(symmetricKeyBytes, "AES");
        log.info("Decryption successful");
        return symmetricKey;
    }

    private static PrivateKey getPKCS8EncodedRSAPrivateKeyFromString(String privateKey) {
        PrivateKey prvKey = null;
        try {
            byte[] decodeByte = Base64.getDecoder().decode(privateKey);
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(decodeByte);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            prvKey = keyFactory.generatePrivate(keySpec);
        } catch (Exception e) {
            log.error("Exception converting privateKey {}", e);
            throw new InvalidPayloadException("Exception converting privateKey");
        }
        return prvKey;
    }

}
