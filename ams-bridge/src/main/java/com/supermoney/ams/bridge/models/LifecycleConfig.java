package com.supermoney.ams.bridge.models;

import java.util.concurrent.TimeUnit;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class LifecycleConfig {
  private TimeUnit timeUnit;
  private int timeValue;

  public LifecycleConfig(TimeUnit timeUnit, int timeValue) {
    this.timeUnit = timeUnit;
    this.timeValue = timeValue;
  }
}
