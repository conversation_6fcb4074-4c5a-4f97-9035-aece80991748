package com.supermoney.ams.bridge.handlers;

import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.response.v6.Action;
import com.flipkart.fintech.pinaka.api.response.v6.ActionType;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.pinaka.api.response.v6.Params;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.exceptions.InvalidPayloadException;
import com.supermoney.ams.bridge.models.interaction.DocumentInteractionConfig;
import com.supermoney.ams.bridge.utils.UrlUtil;
import java.net.URISyntaxException;
import java.util.List;
import lombok.CustomLog;
import org.apache.http.NameValuePair;
import org.apache.http.client.utils.URIBuilder;

@CustomLog
public class DocumentInteractionHandler implements InteractionHandler {

  private final DocumentInteractionConfig interactionConfig;

  public DocumentInteractionHandler(DocumentInteractionConfig interactionConfig) {
    this.interactionConfig = interactionConfig;
  }

  @Override
  public PageActionResponse getPageActionResponse(ApplicationDataResponse applicationDataResponse,
      MerchantUser merchantUser) {
    Action action = getAction(applicationDataResponse, merchantUser);
    Params params=new Params();
    params.setApplicationId(applicationDataResponse.getApplicationId());
    return new PageActionResponse(action, true, null, params);
  }

  private Action getAction(ApplicationDataResponse applicationDataResponse, MerchantUser merchantUser) {
    String url = createUrl(applicationDataResponse);
    return new Action(url, ActionType.NAVIGATION);
  }

  private String createUrl(ApplicationDataResponse applicationDataResponse) {
    try {
      List<NameValuePair> queryParamsList = UrlUtil.getQueryParams(applicationDataResponse);
      return new URIBuilder(interactionConfig.getPageUrl())
          .addParameters(queryParamsList)
          .toString();
    } catch (URISyntaxException e) {
      log.error("Unable to create token", e);
      throw new InvalidPayloadException("Unable to create token");
    }
  }
}
