package com.supermoney.ams.bridge.utils;

import com.flipkart.fintech.pandora.api.model.common.STATUS;
import com.flipkart.fintech.pandora.api.model.request.mandate.MandateStatus;
import com.flipkart.fintech.pinaka.api.enums.ApplicationStatus;
import com.flipkart.fintech.pinaka.api.enums.ApplicationUpdateContext;
import com.flipkart.fintech.pinaka.api.enums.KycUpdateContext;
import com.flipkart.fintech.pinaka.api.enums.MandateState;
import com.flipkart.fintech.pinaka.api.enums.UserNodeFormKey;
import com.flipkart.fintech.pinaka.api.response.dataprovider.Option;
import com.google.common.collect.Lists;
import java.util.*;

import com.google.common.collect.Sets;

/**
 * Created by sujeet<PERSON>.r on 29/08/17.
 */
public class PinakaConstants {

    public static final Map<String, Object> DUMMY_FEATURES = new HashMap<>();

    public static final Map<String, String> NATURE_OF_BUSINESS_MAPPING = new LinkedHashMap<>();

    public static final String CONSENT_ID_PAN_PAGE = "consentIdPanPage";

    public static final String CONSENT_ID_EMP_MFI = "consentIdEmpMFI";

    public static final String CONSENT_ID_EMP_CKCY = "consentIdEmpCKYC";

    public static final String CONSENT_ID_EMP_NONPEP = "consentIdEmpNonPEP";

    static {
        NATURE_OF_BUSINESS_MAPPING.put("Information Technology", "Information Technology");
        NATURE_OF_BUSINESS_MAPPING.put("Professional Service Provider", "Professional Service provider");
        NATURE_OF_BUSINESS_MAPPING.put("Agriculture", "Agriculture");
        NATURE_OF_BUSINESS_MAPPING.put("Bullion/Gold Jewellery", "Bullion/Gold Jewellery");
        NATURE_OF_BUSINESS_MAPPING.put("Stock Broker", "Stock Broker");
        NATURE_OF_BUSINESS_MAPPING.put("Real Estate", "Real Estate");
        NATURE_OF_BUSINESS_MAPPING.put("Retail Trade", "Retail Trade");
        NATURE_OF_BUSINESS_MAPPING.put("Wholesale Trade", "Wholesale Trade");
        NATURE_OF_BUSINESS_MAPPING.put("Money Lender", "Money Lender");
        NATURE_OF_BUSINESS_MAPPING.put("Service", "Professional Service provider");
        NATURE_OF_BUSINESS_MAPPING.put("Manufacturing", "Professional Service provider");
        NATURE_OF_BUSINESS_MAPPING.put("Food Processing", "Professional Service provider");
        NATURE_OF_BUSINESS_MAPPING.put("Logistics/Supply Chain", "Professional Service provider");
        NATURE_OF_BUSINESS_MAPPING.put("Export/Import", "Professional Service provider");
        NATURE_OF_BUSINESS_MAPPING.put("Entertainment", "Professional Service provider");
        NATURE_OF_BUSINESS_MAPPING.put("Pharma/Healthcare/Medical", "Professional Service provider");
    }


    static {
        Map<String, Object> surrogates = new HashMap<>();
        surrogates.put("account_vintage", 1185);
        surrogates.put("days_since_last_order", 1);
        surrogates.put("no_of_orders_fulfilled", 100);
        surrogates.put("gmv_till_date", 163140.0);
        surrogates.put("percentage_of_orders_returned_or_cancelled", 1);
        surrogates.put("highest_gmv_in_a_month", 20000.0);
        surrogates.put("second_highest_gmv_in_a_month", 20000.0);
        surrogates.put("no_of_orders_cmn_addr_12_mnths", 5);
        surrogates.put("number_of_prepaid_orders", 20);
        DUMMY_FEATURES.putAll(surrogates);
    }


    public static final Map<UserNodeFormKey, List<ApplicationUpdateContext>> workflowFormKeyContextMap = new HashMap<>();

    static {
        workflowFormKeyContextMap.put(UserNodeFormKey.EKYC_PAN_AADHAAR_SUBMIT, Lists.newArrayList(ApplicationUpdateContext.EKYC_PAN_AADHAAR_SUBMIT));
        workflowFormKeyContextMap.put(UserNodeFormKey.FETCH_AADHAAR_OTP, Lists.newArrayList(ApplicationUpdateContext.EKYC_PAN_AADHAAR_SUBMIT, ApplicationUpdateContext.EKYC_SUBMIT_OTP, ApplicationUpdateContext.EKYC_RESEND_OTP));
        workflowFormKeyContextMap.put(UserNodeFormKey.REVIEW_SCREEN, Lists.newArrayList(ApplicationUpdateContext.KYC_DISCARD, ApplicationUpdateContext.KYC_REVIEW_AND_SUBMIT));
        workflowFormKeyContextMap.put(UserNodeFormKey.COLLECT_PERMISSIONS, Lists.newArrayList(ApplicationUpdateContext.UPDATE_DEVICE_PERMISSIONS));
        workflowFormKeyContextMap.put(UserNodeFormKey.KYC_REJECTED_RETRIABLE, Lists.newArrayList(ApplicationUpdateContext.KYC_DISCARD, ApplicationUpdateContext.KYC_RETRY));
        workflowFormKeyContextMap.put(UserNodeFormKey.WAITING_BUREAU_INSIGHTS, Lists.newArrayList());
        workflowFormKeyContextMap.put(UserNodeFormKey.WAIT_FOR_SMS_UPLOAD, Lists.newArrayList());
        workflowFormKeyContextMap.put(UserNodeFormKey.GET_EXTERNAL_USER_CONSENT, Lists.newArrayList());
        workflowFormKeyContextMap.put(UserNodeFormKey.INITIATE_PENNY_DROP, Lists.newArrayList(ApplicationUpdateContext.CONDITIONAL_APPROVAL_PAGE));
    }

    public static final Map<UserNodeFormKey, List<KycUpdateContext>> workflowkycUpgradeFormKeyContextMap = new HashMap<>();

    static {
        workflowkycUpgradeFormKeyContextMap.put(UserNodeFormKey.AADHAAR_XML_KYC_PAN_SUBMIT, Lists.newArrayList(KycUpdateContext.AADHAAR_XML_PAN_SUBMIT));
        workflowkycUpgradeFormKeyContextMap.put(UserNodeFormKey.INITIATE_AADHAAR_VERIFICATION, Lists.newArrayList(KycUpdateContext.INITIATE_AADHAAR_VERIFICATION, KycUpdateContext.AADHAAR_XML_PAN_SUBMIT));
        workflowkycUpgradeFormKeyContextMap.put(UserNodeFormKey.REVIEW_SCREEN, Lists.newArrayList(KycUpdateContext.KYC_REVIEW_AND_SUBMIT, KycUpdateContext.KYC_DISCARD));
        workflowkycUpgradeFormKeyContextMap.put(UserNodeFormKey.KYC_REJECTED_RETRIABLE, Lists.newArrayList(KycUpdateContext.KYC_DISCARD, KycUpdateContext.KYC_RETRY));
        workflowkycUpgradeFormKeyContextMap.put(UserNodeFormKey.WAITING_FOR_AADHAAR_VERIFICATION, Lists.newArrayList(KycUpdateContext.XML_KYC_DOCUMENT_UPLOAD, KycUpdateContext.INITIATE_AADHAAR_VERIFICATION, KycUpdateContext.AADHAAR_XML_PAN_SUBMIT));
        workflowkycUpgradeFormKeyContextMap.put(UserNodeFormKey.CKYC_PAN_SUBMIT, Lists.newArrayList(KycUpdateContext.CKYC_PAN_SUBMIT));
        workflowkycUpgradeFormKeyContextMap.put(UserNodeFormKey.EKYC_PAN_SUBMIT, Lists.newArrayList(KycUpdateContext.EKYC_PAN_SUBMIT));
        workflowkycUpgradeFormKeyContextMap.put(UserNodeFormKey.CKYC_GENERATE_OTP, Lists.newArrayList(KycUpdateContext.CKYC_GENERATE_OTP, KycUpdateContext.CKYC_PAN_SUBMIT));
        workflowkycUpgradeFormKeyContextMap.put(UserNodeFormKey.EKYC_GENERATE_OTP, Lists.newArrayList(KycUpdateContext.CKYC_GENERATE_OTP, KycUpdateContext.EKYC_PAN_SUBMIT));
        workflowkycUpgradeFormKeyContextMap.put(UserNodeFormKey.FETCH_AADHAAR_OTP, Lists.newArrayList(KycUpdateContext.CKYC_VERIFY_OTP, KycUpdateContext.CKYC_GENERATE_OTP, KycUpdateContext.EKYC_GENERATE_OTP, KycUpdateContext.EKYC_PAN_SUBMIT, KycUpdateContext.CKYC_PAN_SUBMIT));

    }

    public static final String CBC_SALARIED = "SALARIED";
    public static final String CBC_INDEPENDENT_WORKER = "INDEPENDENT_WORKER";
    public static final String CBC_PROFESSIONAL = "PROFESSIONAL";
    public static final String CBC_BUSINESS_OWNER = "BUSINESS_OWNER";
    public static final String CBC_STUDENT = "STUDENT";
    public static final String CBC_RETIRED = "RETIRED";
    public static final String CBC_HOMEMAKER = "HOMEMAKER";
    public static final String CBC_SELF_EMPLOYED = "SELF EMPLOYED";
    public static final String CBC_SELF_EMPLOYED_ENUM = "SELF_EMPLOYED";

    public static final List<String> CBC_SELF_EMPLOYED_LIST                            = new ArrayList<>();
    static {
        CBC_SELF_EMPLOYED_LIST.add(CBC_INDEPENDENT_WORKER);
        CBC_SELF_EMPLOYED_LIST.add(CBC_PROFESSIONAL);
        CBC_SELF_EMPLOYED_LIST.add(CBC_BUSINESS_OWNER);
        CBC_SELF_EMPLOYED_LIST.add(CBC_SELF_EMPLOYED);
        CBC_SELF_EMPLOYED_LIST.add(CBC_SELF_EMPLOYED_ENUM);
    }

    private static final Map<String, String> CBC_NEW_EMPLOYMENT_OPTIONS = new LinkedHashMap<>();
    private static final Map<String, String> CBC_NTB_EMPLOYMENT_OPTIONS = new LinkedHashMap<>();

    static {
        CBC_NEW_EMPLOYMENT_OPTIONS.put(CBC_SALARIED, "Salaried");
        CBC_NEW_EMPLOYMENT_OPTIONS.put(CBC_SELF_EMPLOYED_ENUM, "Self Employed /  Professional / Business Owner");
        CBC_NEW_EMPLOYMENT_OPTIONS.put(CBC_STUDENT, "Student");
        CBC_NEW_EMPLOYMENT_OPTIONS.put(CBC_RETIRED, "Retired");
        CBC_NEW_EMPLOYMENT_OPTIONS.put(CBC_HOMEMAKER, "Homemaker");

        CBC_NTB_EMPLOYMENT_OPTIONS.put(CBC_SALARIED, "Salaried");
        CBC_NTB_EMPLOYMENT_OPTIONS.put(CBC_SELF_EMPLOYED_ENUM, "Self Employed /  Professional / Business Owner");
    }

    public static List<Option> CBC_EMPLOYMENT_STATUS;

    static {
        List<Option> options = new ArrayList<>();
        for (Map.Entry<String, String> optionEntry : PinakaConstants.CBC_NEW_EMPLOYMENT_OPTIONS.entrySet()) {
            Option option = new Option();
            option.setId(optionEntry.getKey());
            option.setTitle(optionEntry.getValue());
            options.add(option);
        }
        CBC_EMPLOYMENT_STATUS = options;
    }

    public static List<Option> CBC_NTB_EMPLOYMENT_STATUS;

    static {
        List<Option> options = new ArrayList<>();
        for (Map.Entry<String, String> optionEntry : PinakaConstants.CBC_NTB_EMPLOYMENT_OPTIONS.entrySet()) {
            Option option = new Option();
            option.setId(optionEntry.getKey());
            option.setTitle(optionEntry.getValue());
            options.add(option);
        }
        CBC_NTB_EMPLOYMENT_STATUS = options;
    }

    private static final Map<String, String> CBC_USAGE_OPTIONS = new LinkedHashMap<String, String>();

    static {
        CBC_USAGE_OPTIONS.put("international", "International & Domestic");
        CBC_USAGE_OPTIONS.put("domestic", "Domestic");
    }

    public static List<Option> CBC_USAGE_TYPE;

    static {
        List<Option> options = new ArrayList<>();
        for (Map.Entry<String, String> optionEntry : PinakaConstants.CBC_USAGE_OPTIONS.entrySet()) {
            Option option = new Option();
            option.setId(optionEntry.getKey());
            option.setTitle(optionEntry.getValue());
            options.add(option);
        }
        CBC_USAGE_TYPE = options;
    }


    public static final Map<String, String> CBC_ADDRESS_OPTIONS = new LinkedHashMap<>();

    static {
        CBC_ADDRESS_OPTIONS.put("current", "Current Residence");
        CBC_ADDRESS_OPTIONS.put("work", "Work");
    }

    public static List<Option> CBC_ADDRESS_TYPE;

    static {
        List<Option> options = new ArrayList<>();
        for (Map.Entry<String, String> optionEntry : PinakaConstants.CBC_ADDRESS_OPTIONS.entrySet()) {
            Option option = new Option();
            option.setId(optionEntry.getKey());
            option.setTitle(optionEntry.getValue());
            options.add(option);
        }
        CBC_ADDRESS_TYPE = options;
    }

    private static final Map<String, String> CBC_YES_NO_OPTIONS = new LinkedHashMap<>();

    static {
        CBC_YES_NO_OPTIONS.put("yes", "Yes");
        CBC_YES_NO_OPTIONS.put("no", "No");
    }

    public static List<Option> CBC_YES_NO_TYPE;

    static {
        List<Option> options = new ArrayList<>();
        for (Map.Entry<String, String> optionEntry : PinakaConstants.CBC_YES_NO_OPTIONS.entrySet()) {
            Option option = new Option();
            option.setId(optionEntry.getKey());
            option.setTitle(optionEntry.getValue());
            options.add(option);
        }
        CBC_YES_NO_TYPE = options;
    }

    private static final Map<String, String> STATE_OPTIONS = new LinkedHashMap<>();

    static {
        STATE_OPTIONS.put("andaman_and_nicobar_islands", "Andaman and Nicobar Islands");
        STATE_OPTIONS.put("andhra_pradesh", "Andhra Pradesh");
        STATE_OPTIONS.put("arunachal_pradesh", "Arunachal Pradesh");
        STATE_OPTIONS.put("assam", "Assam");
        STATE_OPTIONS.put("bihar", "Bihar");
        STATE_OPTIONS.put("chandigarh", "Chandigarh");
        STATE_OPTIONS.put("chhasttisgarh", "Chhattisgarh");
        STATE_OPTIONS.put("dadra_and_nagar_haveli", "Dadra and Nagar Haveli");
        STATE_OPTIONS.put("daman_and_diu", "Daman and Diu");
        STATE_OPTIONS.put("delhi", "Delhi");
        STATE_OPTIONS.put("goa", "Goa");
        STATE_OPTIONS.put("gujarat", "Gujarat");
        STATE_OPTIONS.put("haryana", "Haryana");
        STATE_OPTIONS.put("himachal_pradesh", "Himachal Pradesh");
        STATE_OPTIONS.put("jammu_&_kashmir", "Jammu & Kashmir");
        STATE_OPTIONS.put("jharkhand", "Jharkhand");
        STATE_OPTIONS.put("karnataka", "Karnataka");
        STATE_OPTIONS.put("kerala", "Kerala");
        STATE_OPTIONS.put("lakshadweep", "Lakshadweep");
        STATE_OPTIONS.put("madhya_pradesh", "Madhya Pradesh");
        STATE_OPTIONS.put("maharashtra", "Maharashtra");
        STATE_OPTIONS.put("manipur", "Manipur");
        STATE_OPTIONS.put("meghalaya", "Meghalaya");
        STATE_OPTIONS.put("mizoram", "Mizoram");
        STATE_OPTIONS.put("nagaland", "Nagaland");
        STATE_OPTIONS.put("odisha", "Odisha");
        STATE_OPTIONS.put("pondicherry", "Pondicherry");
        STATE_OPTIONS.put("punjab", "Punjab");
        STATE_OPTIONS.put("rajasthan", "Rajasthan");
        STATE_OPTIONS.put("sikkim", "Sikkim");
        STATE_OPTIONS.put("tamilnadu", "Tamilnadu");
        STATE_OPTIONS.put("telangana", "Telangana");
        STATE_OPTIONS.put("tripura", "Tripura");
        STATE_OPTIONS.put("uttarakhand", "Uttarakhand");
        STATE_OPTIONS.put("uttar_padesh", "Uttar Pradesh");
        STATE_OPTIONS.put("west_bengal", "West Bengal");
    }

    public static List<Option> STATES;

    static {
        List<Option> options = new ArrayList<>();
        for (Map.Entry<String, String> optionEntry : PinakaConstants.STATE_OPTIONS.entrySet()) {
            Option option = new Option();
            option.setId(optionEntry.getKey());
            option.setTitle(optionEntry.getValue());
            options.add(option);
        }
        STATES = options;
    }

    private static final Map<String, String> GENDER_OPTIONS = new LinkedHashMap<>() ;

    static {
        GENDER_OPTIONS.put("male", "Male");
        GENDER_OPTIONS.put("female", "Female");
        GENDER_OPTIONS.put("transgender", "Transgender");
    }

    public static final List<Option> GENDERS;

    static {
        List<Option> options = new ArrayList<>();
        for (Map.Entry<String, String> optionEntry : PinakaConstants.GENDER_OPTIONS.entrySet()) {
            Option option = new Option();
            option.setId(optionEntry.getKey());
            option.setTitle(optionEntry.getValue());
            options.add(option);
        }
        GENDERS = options;
    }

    public static final Map<String, String> CBC_GENDER_OPTIONS = new LinkedHashMap<>() ;

    static {
        CBC_GENDER_OPTIONS.put("male", "Male");
        CBC_GENDER_OPTIONS.put("female", "Female");
        CBC_GENDER_OPTIONS.put("transgender", "Transgender");
    }

    public static final List<Option> CBC_GENDERS;

    static {
        List<Option> options = new ArrayList<>();
        for (Map.Entry<String, String> optionEntry : PinakaConstants.CBC_GENDER_OPTIONS.entrySet()) {
            Option option = new Option();
            option.setId(optionEntry.getKey());
            option.setTitle(optionEntry.getValue());
            options.add(option);
        }
        CBC_GENDERS = options;
    }

    public static final Map<String, String> MARITAL_OPTIONS = new LinkedHashMap<>();

    static {
        MARITAL_OPTIONS.put("single", "Single");
        MARITAL_OPTIONS.put("married", "Married");
        MARITAL_OPTIONS.put("others", "Others");
    }

    public static final List<Option> MARITAL;

    static {
        List<Option> options = new ArrayList<>();
        for (Map.Entry<String, String> optionEntry : PinakaConstants.MARITAL_OPTIONS.entrySet()) {
            Option option = new Option();
            option.setId(optionEntry.getKey());
            option.setTitle(optionEntry.getValue());
            options.add(option);
        }
        MARITAL = options;
    }

    public static final Map<String, String> EDUCATION_OPTIONS = new LinkedHashMap<>() ;

    static {
        EDUCATION_OPTIONS.put("undergraduate", "Undergraduate");
        EDUCATION_OPTIONS.put("graduate", "Graduate");
        EDUCATION_OPTIONS.put("post graduate", "Post Graduate");
    }

    public static final List<Option> EDUCATION;

    static {
        List<Option> options = new ArrayList<>();
        for (Map.Entry<String, String> optionEntry : PinakaConstants.EDUCATION_OPTIONS.entrySet()) {
            Option option = new Option();
            option.setId(optionEntry.getKey());
            option.setTitle(optionEntry.getValue());
            options.add(option);
        }
        EDUCATION = options;
    }

    private static final Map<String, String> AUTODEBIT_OPTIONS = new LinkedHashMap<>();

    static {
        AUTODEBIT_OPTIONS.put("MAD", "Minimum Amount Due");
        AUTODEBIT_OPTIONS.put("TAD", "Total Amount Due");
    }
    public static final List<Option> AUTO_DEBIT;
    static {
        List<Option> options = new ArrayList<>();
        for(Map.Entry<String, String> optionEntry : PinakaConstants.AUTODEBIT_OPTIONS.entrySet()) {
            Option option = new Option();
            option.setId(optionEntry.getKey());
            option.setTitle(optionEntry.getValue());
            options.add(option);
        }
        AUTO_DEBIT = options;
    }


    public static final Map<String, String> TENURE_IN_ORG_OPTIONS = new LinkedHashMap<>();

    static {
        TENURE_IN_ORG_OPTIONS.put("01", "0 - 1 Year");
        TENURE_IN_ORG_OPTIONS.put("02", "1 - 2 Years");
        TENURE_IN_ORG_OPTIONS.put("03", "2 - 3 Years");
        TENURE_IN_ORG_OPTIONS.put("04", "3 - 4 Years");
        TENURE_IN_ORG_OPTIONS.put("05", "4 - 5 Years");
        TENURE_IN_ORG_OPTIONS.put("06", "> 5 Years");
    }

    public static final  List<Option>        TENURE_IN_ORG;

    static {
        List<Option> options = new ArrayList<>();
        for (Map.Entry<String, String> optionEntry : PinakaConstants.TENURE_IN_ORG_OPTIONS.entrySet()) {
            Option option = new Option();
            option.setId(optionEntry.getKey());
            option.setTitle(optionEntry.getValue());
            options.add(option);
        }
        TENURE_IN_ORG = options;
    }


    public static final Map<String, String> INDUSTRY_TYPE_OPTIONS = new LinkedHashMap<>();

    static {
        INDUSTRY_TYPE_OPTIONS.put("Advertising/Media", "Advertising/Media");
        INDUSTRY_TYPE_OPTIONS.put("Banking", "Banking");
        INDUSTRY_TYPE_OPTIONS.put("Manufacturing/Engineering/Infrastructure", "Manufacturing/Engineering/Infrastructure");
        INDUSTRY_TYPE_OPTIONS.put("Government Services/ Bodies", "Government Services/ Bodies");
        INDUSTRY_TYPE_OPTIONS.put("IT/ITES/BPO", "IT/ITES/BPO");
        INDUSTRY_TYPE_OPTIONS.put("Travel/Entertainment/Hotel/Airlines", "Travel/Entertainment/Hotel/Airlines");
        INDUSTRY_TYPE_OPTIONS.put("Insurance", "Insurance");
        INDUSTRY_TYPE_OPTIONS.put("Construction/Real Estate", "Construction/Real Estate");
        INDUSTRY_TYPE_OPTIONS.put("Telecom", "Telecom");
        INDUSTRY_TYPE_OPTIONS.put("Mutual Funds/ Broking/NBFCs/FIs", "Mutual Funds/ Broking/NBFCs/FIs");
    }

    public static final  List<Option>        INDUSTRY_TYPE;

    static {
        List<Option> options = new ArrayList<>();
        for (Map.Entry<String, String> optionEntry : PinakaConstants.INDUSTRY_TYPE_OPTIONS.entrySet()) {
            Option option = new Option();
            option.setId(optionEntry.getKey());
            option.setTitle(optionEntry.getValue());
            options.add(option);
        }
        INDUSTRY_TYPE = options;
    }

    public static final Map<String, String> EMPLOYMENT_SECTOR_OPTIONS = new LinkedHashMap<>();

    static {
        EMPLOYMENT_SECTOR_OPTIONS.put("Public Sector", "Public Sector Undertaking (PSU)");
        EMPLOYMENT_SECTOR_OPTIONS.put("Government", "Government (Central/State)");
        EMPLOYMENT_SECTOR_OPTIONS.put("Multinational", "Multinational Company");
        EMPLOYMENT_SECTOR_OPTIONS.put("Pvt Ltd", "Private Ltd. Company (India)");
        EMPLOYMENT_SECTOR_OPTIONS.put("Public Ltd", "Public Ltd. (India)");
        EMPLOYMENT_SECTOR_OPTIONS.put("Proprietorship", "Proprietorship (India)");
        EMPLOYMENT_SECTOR_OPTIONS.put("Partnership firm", "Partnership firm (India)");
        EMPLOYMENT_SECTOR_OPTIONS.put("Trust/Association/Society/Club", "Others (Trust/Association/Society/Club)");
    }

    public static final  List<Option>        EMPLOYMENT_SECTOR;

    static {
        List<Option> options = new ArrayList<>();
        for (Map.Entry<String, String> optionEntry : PinakaConstants.EMPLOYMENT_SECTOR_OPTIONS.entrySet()) {
            Option option = new Option();
            option.setId(optionEntry.getKey());
            option.setTitle(optionEntry.getValue());
            options.add(option);
        }
        EMPLOYMENT_SECTOR = options;
    }

    public static final Map<String, String> RESIDENCE_TYPE_OPTIONS = new LinkedHashMap<>();

    static {
        RESIDENCE_TYPE_OPTIONS.put("Rented", "Rented");
        RESIDENCE_TYPE_OPTIONS.put("With Parents", "Parents owned");
        RESIDENCE_TYPE_OPTIONS.put("Owned", "Owned");
        RESIDENCE_TYPE_OPTIONS.put("Others", "Others");
    }

    public static final  List<Option>        RESIDENCE_TYPE;

    static {
        List<Option> options = new ArrayList<>();
        for (Map.Entry<String, String> optionEntry : PinakaConstants.RESIDENCE_TYPE_OPTIONS.entrySet()) {
            Option option = new Option();
            option.setId(optionEntry.getKey());
            option.setTitle(optionEntry.getValue());
            options.add(option);
        }
        RESIDENCE_TYPE = options;
    }

    public static final Map<String, String> OWNERSHIP_TYPE_OPTIONS = new LinkedHashMap<>();

    static {
        OWNERSHIP_TYPE_OPTIONS.put("Sole Proprietorship", "Sole Proprietorship");
        OWNERSHIP_TYPE_OPTIONS.put("Private Limited", "Private Limited");
        OWNERSHIP_TYPE_OPTIONS.put("Partnership", "Partnership");
    }

    public static final  List<Option>        OWNERSHIP_TYPE;

    static {
        List<Option> options = new ArrayList<>();
        for (Map.Entry<String, String> optionEntry : PinakaConstants.OWNERSHIP_TYPE_OPTIONS.entrySet()) {
            Option option = new Option();
            option.setId(optionEntry.getKey());
            option.setTitle(optionEntry.getValue());
            options.add(option);
        }
        OWNERSHIP_TYPE = options;
    }

    public static final Map<String, String> NATURE_OF_BUSINESS_TYPE_OPTIONS = new LinkedHashMap<>();

    static {
        NATURE_OF_BUSINESS_TYPE_OPTIONS.put("Service", "Service");
        NATURE_OF_BUSINESS_TYPE_OPTIONS.put("Exporter / Importer", "Exporter / Importer");
        NATURE_OF_BUSINESS_TYPE_OPTIONS.put("Professional", "Professional");
        NATURE_OF_BUSINESS_TYPE_OPTIONS.put("Trading", "Trading");
        NATURE_OF_BUSINESS_TYPE_OPTIONS.put("Manufacturing", "Manufacturing");
    }

    public static final  List<Option>        NATURE_OF_BUSINESS_TYPE;

    static {
        List<Option> options = new ArrayList<>();
        for (Map.Entry<String, String> optionEntry : PinakaConstants.NATURE_OF_BUSINESS_TYPE_OPTIONS.entrySet()) {
            Option option = new Option();
            option.setId(optionEntry.getKey());
            option.setTitle(optionEntry.getValue());
            options.add(option);
        }
        NATURE_OF_BUSINESS_TYPE = options;
    }

    public static final Map<String, String> NATURE_OF_BUSINESS_TYPE_E2E_OPTIONS = new LinkedHashMap<>();

    static {
        NATURE_OF_BUSINESS_TYPE_E2E_OPTIONS.put("Information Technology", "Information Technology");
        NATURE_OF_BUSINESS_TYPE_E2E_OPTIONS.put("Professional Service Provider", "Professional Service Provider");
        NATURE_OF_BUSINESS_TYPE_E2E_OPTIONS.put("Agriculture", "Agriculture");
        NATURE_OF_BUSINESS_TYPE_E2E_OPTIONS.put("Bullion/Gold Jewellery", "Bullion/Gold Jewellery");
        NATURE_OF_BUSINESS_TYPE_E2E_OPTIONS.put("Stock Broker", "Stock Broker");
        NATURE_OF_BUSINESS_TYPE_E2E_OPTIONS.put("Real Estate", "Real Estate");
        NATURE_OF_BUSINESS_TYPE_E2E_OPTIONS.put("Retail Trade", "Retail Trade");
        NATURE_OF_BUSINESS_TYPE_E2E_OPTIONS.put("Wholesale Trade", "Wholesale Trade");
        NATURE_OF_BUSINESS_TYPE_E2E_OPTIONS.put("Money Lender", "Money Lender");
        NATURE_OF_BUSINESS_TYPE_E2E_OPTIONS.put("Service", "Service Business");
        NATURE_OF_BUSINESS_TYPE_E2E_OPTIONS.put("Manufacturing", "Manufacturer");
        NATURE_OF_BUSINESS_TYPE_E2E_OPTIONS.put("Food Processing", "Food Processing");
        NATURE_OF_BUSINESS_TYPE_E2E_OPTIONS.put("Logistics/Supply Chain", "Logistics/Supply Chain");
        NATURE_OF_BUSINESS_TYPE_E2E_OPTIONS.put("Export/Import", "Export/Import/Merchandising");
        NATURE_OF_BUSINESS_TYPE_E2E_OPTIONS.put("Entertainment", "Entertainment");
        NATURE_OF_BUSINESS_TYPE_E2E_OPTIONS.put("Pharma/Healthcare/Medical", "Pharma/Healthcare/Medical");
    }

    public static final  List<Option>        NATURE_OF_BUSINESS_TYPE_E2E;

    static {
        List<Option> options = new ArrayList<>();
        for (Map.Entry<String, String> optionEntry : PinakaConstants.NATURE_OF_BUSINESS_TYPE_E2E_OPTIONS.entrySet()) {
            Option option = new Option();
            option.setId(optionEntry.getKey());
            option.setTitle(optionEntry.getValue());
            options.add(option);
        }
        NATURE_OF_BUSINESS_TYPE_E2E = options;
    }



    private static final Map<String, String> EMPLOYMENT_STATUS_OPTIONS = new LinkedHashMap<>() ;
    static {
        EMPLOYMENT_STATUS_OPTIONS.put("salaried", "Salaried");
        EMPLOYMENT_STATUS_OPTIONS.put("self_employed", "Self Employed");
        EMPLOYMENT_STATUS_OPTIONS.put("others", "Others");
    }

    public static List<Option> EMPLOYMENT_STATUSES;

    static {
        List<Option> options = new ArrayList<>();
        for (Map.Entry<String, String> optionEntry : PinakaConstants.EMPLOYMENT_STATUS_OPTIONS.entrySet()) {
            Option option = new Option();
            option.setId(optionEntry.getKey());
            option.setTitle(optionEntry.getValue());
            options.add(option);
        }
        EMPLOYMENT_STATUSES = options;
    }

    public static final String FLIPKART                     = "Flipkart";

    public static final Map<MandateState, MandateStatus> pinakaToPandoraMandateStateMapper = new HashMap<>();
    static {
        pinakaToPandoraMandateStateMapper.put(MandateState.SUCCESS, MandateStatus.SUCCESS);
        pinakaToPandoraMandateStateMapper.put(MandateState.FAILED, MandateStatus.FAILURE);
    }

    public static final Map<com.flipkart.affordability.collections.model.enums.mandate.MandateStatus, MandateState> collectionToPinakaMandateStateMapper = new HashMap<>();
    static {
        collectionToPinakaMandateStateMapper.put(com.flipkart.affordability.collections.model.enums.mandate.MandateStatus.CONFIRMED, MandateState.SUCCESS);
        collectionToPinakaMandateStateMapper.put(com.flipkart.affordability.collections.model.enums.mandate.MandateStatus.INITIATED, MandateState.FAILED);
        collectionToPinakaMandateStateMapper.put(com.flipkart.affordability.collections.model.enums.mandate.MandateStatus.FAILED, MandateState.FAILED);
    }

    public static final Map<STATUS, MandateState> pandoraToPinakaMandateStateMapper = new HashMap<>();
    static {
        pandoraToPinakaMandateStateMapper.put(STATUS.SUCCESS, MandateState.SUCCESS);
        pandoraToPinakaMandateStateMapper.put(STATUS.FAILURE, MandateState.FAILED);
    }

    public static final Map<MandateState, com.flipkart.affordability.collections.model.enums.mandate.MandateStatus> pinakaToCollectionsMandateStateMapper = new HashMap<>();
    static {
        pinakaToCollectionsMandateStateMapper.put(MandateState.SUCCESS, com.flipkart.affordability.collections.model.enums.mandate.MandateStatus.CONFIRMED);
        pinakaToCollectionsMandateStateMapper.put(MandateState.FAILED, com.flipkart.affordability.collections.model.enums.mandate.MandateStatus.FAILED);
    }


    public static final Map<String, String> CBC_SPEND_CATEGORY_MAP = new HashMap<>();
    static {
        CBC_SPEND_CATEGORY_MAP.put("AIRLINES","TRAVEL");
        CBC_SPEND_CATEGORY_MAP.put("AUTO SERVICES","COMMUTE");
        CBC_SPEND_CATEGORY_MAP.put("BOOKS AND STATIONERY","SHOPPING");
        CBC_SPEND_CATEGORY_MAP.put("CAR RENTALS","TRAVEL");
        CBC_SPEND_CATEGORY_MAP.put("CASH WITHDRAWAL","CASH");
        CBC_SPEND_CATEGORY_MAP.put("CLOTH STORES","SHOPPING");
        CBC_SPEND_CATEGORY_MAP.put("DEPT STORES","GROCERY");
        CBC_SPEND_CATEGORY_MAP.put("EDUCATION","MISC");
        CBC_SPEND_CATEGORY_MAP.put("ELECTRONICS","SHOPPING");
        CBC_SPEND_CATEGORY_MAP.put("ENTERTAINMENT","ENTERTAINMENT");
        CBC_SPEND_CATEGORY_MAP.put("MOBILE PHONES AND BILL PAYMENTS","BILL");
        CBC_SPEND_CATEGORY_MAP.put("FIN. INST","MISC");
        CBC_SPEND_CATEGORY_MAP.put("FOOD PRODUCTS","FOOD");
        CBC_SPEND_CATEGORY_MAP.put("FOOD STAMPS","FOOD");
        CBC_SPEND_CATEGORY_MAP.put("FUEL","FUEL");
        CBC_SPEND_CATEGORY_MAP.put("GIFT SHOPS","SHOPPING");
        CBC_SPEND_CATEGORY_MAP.put("GOVT SERVICES","BILL");
        CBC_SPEND_CATEGORY_MAP.put("HOME FURNISHING","SHOPPING");
        CBC_SPEND_CATEGORY_MAP.put("HOTELS","TRAVEL");
        CBC_SPEND_CATEGORY_MAP.put("JEWELLERY","SHOPPING");
        CBC_SPEND_CATEGORY_MAP.put("LEATHER GOODS","SHOPPING");
        CBC_SPEND_CATEGORY_MAP.put("MEDICAL","HEALTH");
        CBC_SPEND_CATEGORY_MAP.put("MISCELLANEOUS","MISC");
        CBC_SPEND_CATEGORY_MAP.put("MOTO","SHOPPING");
        CBC_SPEND_CATEGORY_MAP.put("RESTAURANTS","FOOD");
        CBC_SPEND_CATEGORY_MAP.put("RETAIL STORES","SHOPPING");
        CBC_SPEND_CATEGORY_MAP.put("TIME SHARES","TRAVEL");
        CBC_SPEND_CATEGORY_MAP.put("TRANSPORT","COMMUTE");
        CBC_SPEND_CATEGORY_MAP.put("TRAVEL","TRAVEL");
        CBC_SPEND_CATEGORY_MAP.put("VISA","MISC");
    }

    public static final Map<String, Integer> CBC_TRANSACTION_VALID_TO_SHOW_MAP = new HashMap<>();
    static {
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("CRADJ",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("05",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("CRDSP",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("15",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("35",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("PAYMT",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("TEXT ",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("TFEE ",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("1240",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("PFEE ",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("10",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("20",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("25",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("07",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("27",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("FEE",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("DBADJ",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("CASH",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("06",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("TX",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("SFEE",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("17",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("REDEM",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("DBINT",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("DBDSP",1);
        CBC_TRANSACTION_VALID_TO_SHOW_MAP.put("26",1);
    }

    public static final Map<String, Integer> CBC_MCC_TO_ICON_MAP = new HashMap<>();
    static {
        CBC_MCC_TO_ICON_MAP.put("FOOD",0);
        CBC_MCC_TO_ICON_MAP.put("TRAVEL",1);
        CBC_MCC_TO_ICON_MAP.put("HEALTH",2);
        CBC_MCC_TO_ICON_MAP.put("BILL",3);
        CBC_MCC_TO_ICON_MAP.put("GROCERY",4);
        CBC_MCC_TO_ICON_MAP.put("SHOPPING",5);
        CBC_MCC_TO_ICON_MAP.put("FUEL",6);
        CBC_MCC_TO_ICON_MAP.put("COMMUTE",7);
        CBC_MCC_TO_ICON_MAP.put("CREDIT BILL",8);
        CBC_MCC_TO_ICON_MAP.put("MISC",9);
        CBC_MCC_TO_ICON_MAP.put("CASH",10);
        CBC_MCC_TO_ICON_MAP.put("ENTERTAINMENT",11);
    }

    public static final List<String> TERMINAL_APPLICATION_STATES = new ArrayList<>();
    static {
        TERMINAL_APPLICATION_STATES.add(ApplicationStatus.APPROVED.name());
        TERMINAL_APPLICATION_STATES.add(ApplicationStatus.REJECTED.name());
        TERMINAL_APPLICATION_STATES.add(ApplicationStatus.DISCARDED.name());
        TERMINAL_APPLICATION_STATES.add(ApplicationStatus.COMPLETED.name());
    }

    public static final String CBC_REJECT_REASON_MESSAGE_NON_CONTACT = "We regret to inform you that your application has been declined as we were unable to contact you for address verification";
    public static final String CBC_REJECT_REASON_MESSAGE_NOT_INTERESTED = "We regret to inform you that your application has been declined as you are no longer interested";
    public static final String CBC_REJECT_REASON_MESSAGE_UNAVAILABLE = "We regret to inform you that your application has been declined as our services are currently not available in your location";
    public static final String CBC_REJECT_REASON_MESSAGE_POLICY = "We regret to inform you that your application has been declined as the information provided by you was insufficient to meet the policy norms";
    public static final String CBC_REJECT_REASON_MESSAGE_POLICY_NOT_MEET =  "We regret to inform you that your application has been declined as it does not meet the bank's policy norms";
    public static final String CBC_REJECT_REASON_MESSAGE_DUPLICATE  = "We regret to inform you that your application has been declined as it is a duplicate application as per our records";
    public static final Map<String, String> CBC_REJECT_REASON_MESSAGE_MAP = new HashMap<>();
    static {
        CBC_REJECT_REASON_MESSAGE_MAP.put("0101", CBC_REJECT_REASON_MESSAGE_NON_CONTACT);
        CBC_REJECT_REASON_MESSAGE_MAP.put("0102", CBC_REJECT_REASON_MESSAGE_NON_CONTACT);
        CBC_REJECT_REASON_MESSAGE_MAP.put("0103", CBC_REJECT_REASON_MESSAGE_NON_CONTACT);
        CBC_REJECT_REASON_MESSAGE_MAP.put("0104", CBC_REJECT_REASON_MESSAGE_POLICY_NOT_MEET);
        CBC_REJECT_REASON_MESSAGE_MAP.put("0105", CBC_REJECT_REASON_MESSAGE_NON_CONTACT);
        CBC_REJECT_REASON_MESSAGE_MAP.put("0116", CBC_REJECT_REASON_MESSAGE_NON_CONTACT);
        CBC_REJECT_REASON_MESSAGE_MAP.put("0117", CBC_REJECT_REASON_MESSAGE_NON_CONTACT);
        CBC_REJECT_REASON_MESSAGE_MAP.put("0200", CBC_REJECT_REASON_MESSAGE_NOT_INTERESTED);
        CBC_REJECT_REASON_MESSAGE_MAP.put("0201", CBC_REJECT_REASON_MESSAGE_NOT_INTERESTED);
        CBC_REJECT_REASON_MESSAGE_MAP.put("0202", CBC_REJECT_REASON_MESSAGE_NOT_INTERESTED);
        CBC_REJECT_REASON_MESSAGE_MAP.put("0300", CBC_REJECT_REASON_MESSAGE_UNAVAILABLE);
        CBC_REJECT_REASON_MESSAGE_MAP.put("0301", CBC_REJECT_REASON_MESSAGE_UNAVAILABLE);
        CBC_REJECT_REASON_MESSAGE_MAP.put("0400", CBC_REJECT_REASON_MESSAGE_POLICY);
        CBC_REJECT_REASON_MESSAGE_MAP.put("0500", CBC_REJECT_REASON_MESSAGE_POLICY);
        CBC_REJECT_REASON_MESSAGE_MAP.put("0501", CBC_REJECT_REASON_MESSAGE_POLICY);
        CBC_REJECT_REASON_MESSAGE_MAP.put("0503", CBC_REJECT_REASON_MESSAGE_POLICY);
        CBC_REJECT_REASON_MESSAGE_MAP.put("0506", CBC_REJECT_REASON_MESSAGE_POLICY);
        CBC_REJECT_REASON_MESSAGE_MAP.put("0526", CBC_REJECT_REASON_MESSAGE_POLICY);
        CBC_REJECT_REASON_MESSAGE_MAP.put("0701", CBC_REJECT_REASON_MESSAGE_DUPLICATE);
        CBC_REJECT_REASON_MESSAGE_MAP.put("0702", CBC_REJECT_REASON_MESSAGE_DUPLICATE);
        CBC_REJECT_REASON_MESSAGE_MAP.put("0800", CBC_REJECT_REASON_MESSAGE_POLICY_NOT_MEET);
        CBC_REJECT_REASON_MESSAGE_MAP.put("0801", CBC_REJECT_REASON_MESSAGE_POLICY_NOT_MEET);
        CBC_REJECT_REASON_MESSAGE_MAP.put("0900", CBC_REJECT_REASON_MESSAGE_POLICY_NOT_MEET);
        CBC_REJECT_REASON_MESSAGE_MAP.put("0902", CBC_REJECT_REASON_MESSAGE_POLICY_NOT_MEET);
        CBC_REJECT_REASON_MESSAGE_MAP.put("0904", CBC_REJECT_REASON_MESSAGE_POLICY_NOT_MEET);
        CBC_REJECT_REASON_MESSAGE_MAP.put("1001", CBC_REJECT_REASON_MESSAGE_NON_CONTACT);
        CBC_REJECT_REASON_MESSAGE_MAP.put("1003", CBC_REJECT_REASON_MESSAGE_POLICY_NOT_MEET);
        CBC_REJECT_REASON_MESSAGE_MAP.put("1502", CBC_REJECT_REASON_MESSAGE_POLICY);
        CBC_REJECT_REASON_MESSAGE_MAP.put("1601", CBC_REJECT_REASON_MESSAGE_POLICY_NOT_MEET);
        CBC_REJECT_REASON_MESSAGE_MAP.put("1602", CBC_REJECT_REASON_MESSAGE_POLICY_NOT_MEET);
        CBC_REJECT_REASON_MESSAGE_MAP.put("1603", CBC_REJECT_REASON_MESSAGE_POLICY_NOT_MEET);
        CBC_REJECT_REASON_MESSAGE_MAP.put("1604", CBC_REJECT_REASON_MESSAGE_POLICY_NOT_MEET);
        CBC_REJECT_REASON_MESSAGE_MAP.put("1605", CBC_REJECT_REASON_MESSAGE_POLICY_NOT_MEET);
        CBC_REJECT_REASON_MESSAGE_MAP.put("1606", CBC_REJECT_REASON_MESSAGE_POLICY_NOT_MEET);
        CBC_REJECT_REASON_MESSAGE_MAP.put("2001", CBC_REJECT_REASON_MESSAGE_POLICY_NOT_MEET);
        CBC_REJECT_REASON_MESSAGE_MAP.put("2002", CBC_REJECT_REASON_MESSAGE_POLICY_NOT_MEET);
        CBC_REJECT_REASON_MESSAGE_MAP.put("3001", CBC_REJECT_REASON_MESSAGE_POLICY_NOT_MEET);
    }
    public static final String EMPTY_STRING = "";

    public static class PLConstants {
        public static final String APPLICATION_ID  = "applicationId";
        public static final String TASK_ID  = "taskId";
        public static final String TASK_KEY  = "taskKey";
        public static final String PROCESS_INSTANCE_ID  = "processInstanceId";
        public static final String TOKEN = "token";
        public static final String PL_ENCRYPTION_KEY = "CCv7SRrJuatGmf3C";
        public static final String FINANCIAL_PROVIDER = "financial_provider";
    }


    public static class PLWorkflowVariable {
        public static final String EMI = "emi";
        public static final String GET_OFFER = "getOffer";
    }

    public static final Set<String> SANDBOX_LENDERS_SET = Sets.newHashSet("MONEYVIEW", "OMNI", "OMNIV2", "MONEYVIEWOPENMKT", "DMI", "SMARTCOIN", "FIBE", "FINNABLE", "RING", "FIBEV2", "MONEYVIEWV2", "ABFL","PFL", "MONEYVIEWMFI", "PREFR", "DMIV2");

}
