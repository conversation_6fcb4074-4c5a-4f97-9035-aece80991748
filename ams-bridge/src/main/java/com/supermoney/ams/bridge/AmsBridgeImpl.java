package com.supermoney.ams.bridge;

import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.MandateSubmitRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.VkycSubmitRequest;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.winterfell.api.request.PendingTask;
import com.flipkart.fintech.winterfell.api.request.ResumeApplicationRequest;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.dao.FileBasedConfigurationDao;
import com.supermoney.ams.bridge.handlers.InteractionHandler;
import com.supermoney.ams.bridge.handlers.InteractionHandlerFactory;
import com.supermoney.ams.bridge.interaction.InteractionResolver;
import com.supermoney.ams.bridge.lifecycle.LifecycleManager;
import com.supermoney.ams.bridge.models.ApplicationConfig;
import com.supermoney.ams.bridge.models.ApplicationState;
import com.supermoney.ams.bridge.models.InteractionConfig;
import com.supermoney.ams.bridge.models.interaction.MandateInteractionConfig;
import com.supermoney.ams.bridge.models.interaction.VkycInteractionConfig;
import com.supermoney.ams.bridge.request.builder.ResumeApplicationRequestBuilder;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.inject.Inject;

public class AmsBridgeImpl implements AmsBridge {

  private final InteractionResolver interactionResolver;
  private final ResumeApplicationRequestBuilder resumeApplicationRequestBuilder;
  private final InteractionHandlerFactory interactionHandlerFactory;

  private final LifecycleManager lifecycleManager;

  @Inject
  public AmsBridgeImpl(InteractionResolver interactionResolver, ResumeApplicationRequestBuilder resumeApplicationRequestBuilder, InteractionHandlerFactory interactionHandlerFactory,
      LifecycleManager lifecycleManager) {
    this.interactionResolver = interactionResolver;
    this.resumeApplicationRequestBuilder = resumeApplicationRequestBuilder;
    this.interactionHandlerFactory = interactionHandlerFactory;
    this.lifecycleManager = lifecycleManager;
  }

  @Override
  public ResumeApplicationRequest getResumeRequest(UserActionRequest userActionRequest, ApplicationDataResponse applicationDataResponse) {
    ApplicationState applicationState = ApplicationState.create(applicationDataResponse);
    InteractionConfig interactionConfig = interactionResolver.getInteraction(applicationState);
    return resumeApplicationRequestBuilder.build(userActionRequest, applicationDataResponse, interactionConfig);
  }

  @Override
  public PageActionResponse getPageActionResponse(ApplicationDataResponse applicationDataResponse,
      MerchantUser merchantUser) {
    InteractionHandler handler = getInteractionHandler(applicationDataResponse);
    return handler.getPageActionResponse(applicationDataResponse, merchantUser);
  }

  @Override
  public boolean isSameInteraction(ApplicationState state1, ApplicationState state2) {
    return interactionResolver.isSameInteraction(state1, state2);
  }

  @Override
  public String getInteractionKey(ApplicationState state) {
      FileBasedConfigurationDao fileBasedConfigurationDao = new FileBasedConfigurationDao();
      ApplicationConfig applicationConfig = fileBasedConfigurationDao.getConfig(state.getApplicationType());
      return applicationConfig.findInteractionKey(state).get();
  }

  @Override
  public boolean isResumable(UserActionRequest userActionRequest,
      ApplicationDataResponse applicationDataResponse) {
    /* MandateSubmitRequest does not have task info */
    if (userActionRequest instanceof MandateSubmitRequest) {
      ApplicationState applicationState = ApplicationState.create(applicationDataResponse);
      InteractionConfig interaction = interactionResolver.getInteraction(applicationState);
      return interaction instanceof MandateInteractionConfig;
    }

    /* VkycSubmitRequest does not have task info */
    if (userActionRequest instanceof VkycSubmitRequest) {
      ApplicationState applicationState = ApplicationState.create(applicationDataResponse);
      InteractionConfig interaction = interactionResolver.getInteraction(applicationState);
      return interaction instanceof VkycInteractionConfig;
    }
    List<PendingTask> pendingTasks = applicationDataResponse.getPendingTask();
    String taskId = userActionRequest.getTaskId();
    return CollectionUtils.isNotEmpty(pendingTasks) &&
        taskId.equals(pendingTasks.get(0).getTaskId());
  }

  @Override
  public boolean isUserDiscardAllowed(ApplicationDataResponse applicationDataResponse) {
    return getInteractionHandler(applicationDataResponse).checkUserDiscardAllowed(applicationDataResponse);
  }

  @Override
  public boolean isTerminalState(ApplicationDataResponse applicationDataResponse){
    return getInteractionHandler(applicationDataResponse).isTerminalState(applicationDataResponse);
  }

  @Override
  public boolean checkReadRepair(ApplicationDataResponse applicationDataResponse) {
    if(Objects.equals(applicationDataResponse.getApplicationData().get("financial_provider"), "AXIS")) {
      return false;
    }
    return getInteractionHandler(applicationDataResponse).checkReadRepair(applicationDataResponse);
  }

  @Override
  public ResumeApplicationRequest getRepairRequest(ApplicationDataResponse applicationDataResponse) {
    InteractionHandler handler = getInteractionHandler(applicationDataResponse);
    return getResumeRequest(handler.getDummyUserActionRepairRequest(applicationDataResponse), applicationDataResponse);
  }

  @Override
  public ResumeApplicationRequest getRepairRequest(ApplicationDataResponse applicationDataResponse, Map<String,String> additionalParams) {
      InteractionHandler handler = getInteractionHandler(applicationDataResponse);
    try {
      return getResumeRequest(handler.getDummyUserActionRepairRequest(applicationDataResponse, additionalParams), applicationDataResponse);
    }catch(Exception e){
      return getResumeRequest(handler.getDummyUserActionRepairRequest(applicationDataResponse), applicationDataResponse);
    }
  }

  @Override
  public boolean hasExpired(ApplicationDataResponse applicationDataResponse) {
    return lifecycleManager.hasExpired(applicationDataResponse);
  }

  private InteractionHandler getInteractionHandler(ApplicationDataResponse applicationDataResponse) {
    ApplicationState applicationState = ApplicationState.create(applicationDataResponse);
    InteractionConfig interactionConfig = interactionResolver.getInteraction(applicationState);
    return interactionHandlerFactory.getHandler(interactionConfig);
  }

}
