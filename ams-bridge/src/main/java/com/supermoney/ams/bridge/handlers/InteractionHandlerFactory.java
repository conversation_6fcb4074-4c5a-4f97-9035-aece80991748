package com.supermoney.ams.bridge.handlers;

import com.flipkart.fintech.pandora.client.PlClient;
import com.supermoney.ams.bridge.models.*;
import com.supermoney.ams.bridge.models.interaction.DocumentInteractionConfig;
import com.supermoney.ams.bridge.models.interaction.FormInteractionConfig;
import com.supermoney.ams.bridge.models.interaction.LenderInteractionConfig;
import com.supermoney.ams.bridge.models.interaction.MandateInteractionConfig;
import com.supermoney.ams.bridge.models.interaction.RepairableFormInteractionConfig;
import com.supermoney.ams.bridge.models.interaction.VkycInteractionConfig;

import javax.inject.Inject;

public class InteractionHandlerFactory {
  private final PlClient plClient;
  @Inject
  public InteractionHandlerFactory(PlClient plClient) {
    this.plClient = plClient;
  }

  //todo : see if this can be removed
  public InteractionHandler getHandler(InteractionConfig interactionConfig) {
    if (interactionConfig instanceof RepairableFormInteractionConfig) {
      return new RepairableFormInteractionHandler((RepairableFormInteractionConfig) interactionConfig);
    }
    if (interactionConfig instanceof FormInteractionConfig) {
      return new FormInteractionHandler((FormInteractionConfig) interactionConfig);
    }
    if (interactionConfig instanceof MandateInteractionConfig) {

      return new MandateInteractionHandler((MandateInteractionConfig) interactionConfig, plClient);
    }
    if (interactionConfig instanceof DocumentInteractionConfig) {
      return new DocumentInteractionHandler((DocumentInteractionConfig) interactionConfig);
    }
    if (interactionConfig instanceof VkycInteractionConfig) {
      return new VkycInteractionHandler((VkycInteractionConfig) interactionConfig);
    }
    if(interactionConfig instanceof LenderInteractionConfig){
      return LenderInteractionHandler.builder().interactionConfig((LenderInteractionConfig) interactionConfig).build();
    }
    throw new IllegalArgumentException("Unknown interaction config");
  }
}
