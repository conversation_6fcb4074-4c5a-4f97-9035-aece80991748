package com.supermoney.ams.bridge.request.builder;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.pinaka.api.request.v6.Token;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.winterfell.api.request.PendingTask;
import com.flipkart.fintech.winterfell.api.request.ResumeApplicationRequest;
import com.flipkart.fintech.winterfell.api.request.VariableData;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.exceptions.InvalidMerchantException;
import com.supermoney.ams.bridge.utils.EncryptionUtil;
import com.supermoney.ams.bridge.helpers.URLHelper;
import com.supermoney.ams.bridge.utils.PinakaConstants;
import com.supermoney.ams.bridge.validator.DataValidator;
import com.supermoney.ams.bridge.models.InteractionConfig;
import com.supermoney.ams.bridge.useraction.data.DataBuilder;
import com.supermoney.ams.bridge.useraction.data.DataBuilderFactory;
import lombok.CustomLog;
import org.apache.http.client.utils.URIBuilder;

import java.net.URISyntaxException;
import java.util.Collections;
import java.util.Map;
import javax.inject.Inject;

@CustomLog
public class ResumeApplicationRequestBuilder {
  private final DataBuilderFactory dataBuilderFactory;
  private final DataValidator validator;
  private final URLHelper plURLHelper;
  private final ObjectMapper objectMapper = new ObjectMapper();

  @Inject
  public ResumeApplicationRequestBuilder(DataBuilderFactory dataBuilderFactory, DataValidator validator,
                                         URLHelper plURLHelper) {
    this.dataBuilderFactory = dataBuilderFactory;
    this.validator = validator;
    this.plURLHelper = plURLHelper;
  }

  public final ResumeApplicationRequest build(UserActionRequest submitRequest, ApplicationDataResponse applicationDataResponse, InteractionConfig interactionConfig) {
    PendingTask pendingTask = applicationDataResponse.getPendingTask().get(0);
    DataBuilder dataBuilder = dataBuilderFactory.get(submitRequest);
    Map<String, Object> additionalData = dataBuilder.build();

    validator.validate(additionalData, interactionConfig);
    Map<String, Object> additionalApplicationData = additionalApplicationData(pendingTask, additionalData);
    Map<String, VariableData> additionalWorkflowData = additionalWorkflowData(pendingTask, additionalData);
    return ResumeApplicationRequest.builder()
        .pendingTask(pendingTask)
        .applicationData(additionalApplicationData)
        .workflowData(additionalWorkflowData)
        .smUserId(submitRequest.getSmUserId())
        .build();
  }

  public final ResumeApplicationRequest buildReadRepairData(UserActionRequest submitRequest, ApplicationDataResponse applicationDataResponse) {
    PendingTask pendingTask = applicationDataResponse.getPendingTask().get(0);
    DataBuilder dataBuilder = dataBuilderFactory.get(submitRequest);
    Map<String, Object> additionalData = dataBuilder.readRepairData(applicationDataResponse);
    Map<String, Object> additionalApplicationData = additionalApplicationRepairData(pendingTask, additionalData);
    Map<String, VariableData> additionalWorkflowData = additionalWorkflowRepairData(pendingTask, additionalData);
    return ResumeApplicationRequest.builder()
            .pendingTask(pendingTask)
            .applicationData(additionalApplicationData)
            .smUserId(submitRequest.getSmUserId())
            .workflowData(additionalWorkflowData)
            .build();
  }

  private Map<String, VariableData> additionalWorkflowRepairData(PendingTask pendingTask, Map<String, Object> additionalData) {
    VariableData variableData = new VariableData(false, additionalData);
    String nodeId = pendingTask.getTaskKey()+"readRepairData"; // storing data against a nodeId
    return Collections.singletonMap(nodeId, variableData);
  }

  private Map<String, Object> additionalApplicationRepairData(PendingTask pendingTask, Map<String, Object> additionalData) {
    String nodeId = pendingTask.getTaskKey()+"readRepairData";// storing data against a nodeId
    return Collections.singletonMap(nodeId, additionalData);
  }

  private Map<String, Object> additionalApplicationData(PendingTask pendingTask, Map<String, Object> additionalData) {
    String nodeId = pendingTask.getTaskKey(); // storing data against a nodeId
    return Collections.singletonMap(nodeId, additionalData);
  }

  private Map<String, VariableData> additionalWorkflowData(PendingTask pendingTask, Map<String, Object> additionalData) {
    VariableData variableData = new VariableData(false, additionalData);
    String nodeId = pendingTask.getTaskKey(); // storing data against a nodeId
    return Collections.singletonMap(nodeId, variableData);
  }
}
