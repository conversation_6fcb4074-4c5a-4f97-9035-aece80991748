package com.supermoney.ams.bridge.repairer;

import com.flipkart.fintech.pandora.api.model.pl.response.AutoDisbursalResponse;
import com.flipkart.fintech.pandora.api.model.pl.response.LoanOnboardingResponse;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.FormSubmitRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.google.common.collect.ImmutableMap;
import com.supermoney.ams.bridge.models.interaction.RepairableFormInteractionConfig;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import lombok.RequiredArgsConstructor;

import javax.inject.Inject;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import static com.supermoney.ams.bridge.repairer.FormRepairerUtil.getFormSubmitRequest;

@RequiredArgsConstructor(onConstructor = @__(@Inject))
public class KfsRepairer implements Repairer {

    private static final String REFRESH_KFS = "refreshKfs";
    private static final String ONBOARD_LOAN = "onboardLoan";
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm:ss");

    @Override
    public boolean isRepairNeeded(ApplicationDataResponse response, RepairableFormInteractionConfig config) {
        String lastKfsTime = getLastKfsTime(response);
        int lastKfsDate = LocalDate.parse(lastKfsTime, formatter).getDayOfMonth();
        int todaysDate = LocalDate. now().getDayOfMonth();
        if (todaysDate != lastKfsDate) {
            return true;
        }
        return false;
    }

    @Override
    public UserActionRequest getDummyUserActionRequest(ApplicationDataResponse response, RepairableFormInteractionConfig config) {
        FormSubmitRequest dummyFormSubmitRequest = getFormSubmitRequest(response);
        dummyFormSubmitRequest.setFormData(ImmutableMap.of("repair", true));
        return dummyFormSubmitRequest;
    }


    private String getLastKfsTime(ApplicationDataResponse applicationDataResponse) {
        if (applicationDataResponse.getApplicationData().containsKey(REFRESH_KFS)) {
            AutoDisbursalResponse response = ObjectMapperUtil.get().convertValue(
                    applicationDataResponse.getApplicationData().get(REFRESH_KFS), AutoDisbursalResponse.class);
            return response.getMetadata().getTime();
        } else {
            LoanOnboardingResponse response = ObjectMapperUtil.get().convertValue(
                    applicationDataResponse.getApplicationData().get(ONBOARD_LOAN), LoanOnboardingResponse.class);
            return response.getResult().getResponse().getKfsDetails().get(0).getAgreementDate();
        }
    }
}
