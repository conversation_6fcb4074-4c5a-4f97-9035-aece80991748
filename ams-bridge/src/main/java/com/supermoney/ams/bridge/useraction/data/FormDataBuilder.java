package com.supermoney.ams.bridge.useraction.data;

import com.flipkart.fintech.pinaka.api.request.v6.useraction.FormSubmitRequest;
import com.flipkart.rome.datatypes.response.fintech.supermoney.responseContext.consent.BaseConsentResponse;
import com.supermoney.ams.bridge.exceptions.InvalidPayloadException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

public class FormDataBuilder implements DataBuilder {

  private static final String CONSENT_DATA_KEY = "consentData";
  private static final String CONSENT_LIST_DATA_KEY = "consentListData";
  private final FormSubmitRequest formSubmitRequest;

  public FormDataBuilder(FormSubmitRequest formSubmitRequest) {
    this.formSubmitRequest = formSubmitRequest;
  }

  @Override
  public Map<String, Object> build() {
    Map<String, Object> formData = formSubmitRequest.getFormData();
    Map<String, Object> shallowCopy = shallowCopy(formData);

    if (shallowCopy.containsKey(CONSENT_DATA_KEY)){
      throw new InvalidPayloadException("Unable to create FormData. Reason: FormData already contains consentData");
    }

    if(Objects.nonNull(formSubmitRequest.getConsentData())){
      Map<String, Object> consentData = formSubmitRequest.getConsentData();
      shallowCopy.put(CONSENT_DATA_KEY, consentData);
    }
    if(Objects.nonNull(formSubmitRequest.getConsentListData())){
      List<BaseConsentResponse> consentListData = formSubmitRequest.getConsentListData();
      shallowCopy.put(CONSENT_LIST_DATA_KEY, consentListData);
    }
    return shallowCopy;
  }

  private static Map<String, Object> shallowCopy(Map<String, Object> map) {
    return map != null ? new HashMap<>(map) : new HashMap<>();
  }
}
