package com.supermoney.ams.bridge.models.application;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.supermoney.ams.bridge.models.ApplicationConfig;
import com.supermoney.ams.bridge.models.ApplicationState;
import com.supermoney.ams.bridge.models.InteractionConfig;
import com.supermoney.ams.bridge.models.LifecycleConfig;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * VOApplicationConfig is for Old ApplicationType like PERSONAL_LOAN (Axis).
 */
@Getter
public class V0ApplicationConfig implements ApplicationConfig {

  @JsonProperty("taskKeysList")
  private List<TaskKey> taskKeysList;
  @JsonProperty("applicationStatesList")
  private List<ApplicationStateKey> applicationStatesList;


  private final static IllegalStateException NOT_SUPPORTED_EXCEPTION;

  static {
    NOT_SUPPORTED_EXCEPTION = new IllegalStateException("API not support supported");
  }

  @Override
  public Optional<InteractionConfig> getUserInteraction(ApplicationState applicationState) {
    throw NOT_SUPPORTED_EXCEPTION;
  }

  @Override
  public Optional<String> findInteractionKey(ApplicationState applicationState) {
    throw NOT_SUPPORTED_EXCEPTION;
  }

  @Override
  public InteractionConfig getDefault() {
    throw NOT_SUPPORTED_EXCEPTION;
  }

  @Override
  public LifecycleConfig getLifecycle(ApplicationState applicationState) {
    String taskKey = applicationState.getTaskKey();
    if (StringUtils.isNotBlank(taskKey)) {
      return getTaskKeyBasedLifecycleConfig(taskKey);
    }
    String state = applicationState.getApplicationState();
    return getApplicationStateBasedLifecycleConfig(state);
  }

  private LifecycleConfig getTaskKeyBasedLifecycleConfig(String taskKey) {
    return taskKeysList.stream()
        .filter(e -> taskKey.equals(e.getTaskKey()))
        .map(TaskKey::getLifecycleConfig)
        .filter(Objects::nonNull)
        .findFirst()
        .orElse(new LifecycleConfig(TimeUnit.DAYS, 0));
  }

  private LifecycleConfig getApplicationStateBasedLifecycleConfig(String state) {
    return applicationStatesList.stream()
        .filter(e -> Objects.equals(state, e.getApplicationState()))
        .map(ApplicationStateKey::getLifecycleConfig)
        .filter(Objects::nonNull)
        .findFirst()
        .orElse(new LifecycleConfig(TimeUnit.DAYS, 0));
  }

}
