package com.supermoney.ams.bridge.validator;

import com.fasterxml.jackson.databind.JsonNode;
import com.flipkart.fintech.schema.validator.SchemaValidator;
import com.flipkart.fintech.schema.validator.SchemaValidatorResult;
import com.github.fge.jsonschema.exceptions.InvalidSchemaException;
import com.github.fge.jsonschema.exceptions.ProcessingException;
import com.supermoney.ams.bridge.exceptions.InvalidInteractionConfigException;
import com.supermoney.ams.bridge.exceptions.InvalidPayloadException;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import com.supermoney.ams.bridge.models.InteractionConfig;
import java.util.Map;
import javax.inject.Singleton;

@Singleton
public class DataValidator {

  public void validate(Map<String, Object> data, InteractionConfig interaction) {
    JsonNode schema = interaction.getSchema();
    JsonNode jsonNode = ObjectMapperUtil.get().convertValue(data, JsonNode.class);
    try {
      SchemaValidatorResult result = SchemaValidator.schemaApply(jsonNode, schema);
      if (result != SchemaValidatorResult.VALID) {
        throw new InvalidPayloadException(result.getMessage());
      }
    } catch (InvalidSchemaException e) {
      throw new InvalidInteractionConfigException(e, interaction);
    } catch (ProcessingException e) {
      throw new RuntimeException(e);
    }
  }
}
