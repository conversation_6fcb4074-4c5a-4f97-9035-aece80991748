package com.supermoney.ams.bridge.dao;

import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import com.supermoney.ams.bridge.models.ApplicationConfig;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.InputStream;

import static com.supermoney.ams.bridge.utils.PinakaConstants.SANDBOX_LENDERS_SET;

public class FileBasedConfigurationDao implements ConfigurationDao {
  private final static String FILE_FORMAT = "%s.json";

  public FileBasedConfigurationDao() {
  }

  @Override
  public ApplicationConfig getConfig(String applicationType) {
    String amsApplicationType = getAmsApplicationType(applicationType);
    String fileName = getFileName(amsApplicationType);
    return readFile(fileName);
  }

  private String getAmsApplicationType(String applicationType){

    String lender = StringUtils.substringAfterLast(applicationType, "_");
    if(SANDBOX_LENDERS_SET.contains(lender)){
      return "PERSONAL_LOAN_SANDBOX";
    }
    return applicationType;
  }

  private ApplicationConfig readFile(String fileName) {
    try {
      InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(fileName);
      return ObjectMapperUtil.get().readValue(inputStream, ApplicationConfig.class);
    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

  private String getFileName(String applicationType) {
    return String.format(FILE_FORMAT, applicationType);
  }
}
