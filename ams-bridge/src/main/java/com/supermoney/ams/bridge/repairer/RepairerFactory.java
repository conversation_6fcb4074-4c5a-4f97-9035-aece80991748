package com.supermoney.ams.bridge.repairer;

import com.supermoney.ams.bridge.models.ApplicationState;

public class RepairerFactory {

    public static Repairer getRepairer(ApplicationState applicationState) {
        switch (applicationState.getTaskKey()) {
            case "kfsScreen":
                return new KfsRepairer();
            case "applicationStatus":
                return new ApplicationStateRepairer();
            case "offerDetails":
                return new OfferDetailsRepairer();
            default:
                throw new RuntimeException(String.format("Unable to get repairer for state: %s", applicationState));
        }
    }
}
