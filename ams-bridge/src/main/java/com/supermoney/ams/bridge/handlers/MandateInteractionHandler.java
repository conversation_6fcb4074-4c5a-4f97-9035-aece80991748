package com.supermoney.ams.bridge.handlers;


import com.flipkart.fintech.pandora.api.model.idfc.EmandateLenderRequest;
import com.flipkart.fintech.pandora.api.model.idfc.EmandateLenderResponse;
import com.flipkart.fintech.pandora.api.model.idfc.EmandatePayload;
import com.flipkart.fintech.pandora.api.model.idfc.EmandateResponse;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.MandateSubmitRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserRequestActionType;
import com.flipkart.fintech.pinaka.api.response.v6.FormAutoSubmitResponse;
import com.flipkart.fintech.pinaka.api.response.v6.PageActionResponse;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.flipkart.rome.datatypes.response.common.Action;
import com.flipkart.rome.datatypes.response.page.action.v1.calm.AutoSubmitForm;
import com.flipkart.rome.datatypes.response.page.action.v1.calm.FormInput;
import com.supermoney.ams.bridge.models.interaction.MandateInteractionConfig;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import com.flipkart.fintech.pandora.api.model.pl.request.EmandateVerificationRequest;
import java.util.*;
import java.util.Map.Entry;
import com.flipkart.fintech.pandora.client.PlClient;
import com.flipkart.fintech.pandora.api.model.pl.response.EmandateVerificationResponse;
import lombok.CustomLog;

@CustomLog
public class MandateInteractionHandler implements InteractionHandler {

  private final MandateInteractionConfig interactionConfig;
  private final PlClient plClient;

  public MandateInteractionHandler(MandateInteractionConfig interactionConfig, PlClient plClient) {
    this.interactionConfig = interactionConfig;
    this.plClient = plClient;
  }

  @Override
  public PageActionResponse getPageActionResponse(ApplicationDataResponse applicationDataResponse,
      MerchantUser merchantUser) {
    Object data = applicationDataResponse.getApplicationData()
        .get(interactionConfig.getDataKey());
    EmandateResponse response = ObjectMapperUtil.get().convertValue(data, EmandateResponse.class);
    FormAutoSubmitResponse actionResponseContext = getFormAutoSubmitResponseContext(response);
    return new PageActionResponse(null, true, null, null, actionResponseContext);
  }

  public boolean checkReadRepair(ApplicationDataResponse applicationDataResponse){
    String lastTransactionId  = getLastTransactionId(applicationDataResponse);
    String currTransactionId = getTransactionId(applicationDataResponse);
    if(Objects.isNull(lastTransactionId) || Objects.isNull(currTransactionId) || (!Objects.isNull(lastTransactionId) && !lastTransactionId.equals(currTransactionId))){
      return false;
    }
      EmandateVerificationRequest emandateVerificationRequest = new EmandateVerificationRequest();
      emandateVerificationRequest.setMerchantTxnId(currTransactionId);
      emandateVerificationRequest.setApplicationId((String) applicationDataResponse.getApplicationData().get("offer_id"));
      EmandateVerificationResponse emandateVerificationResponse = plClient.getEmandateVerificationDetails("idfc", emandateVerificationRequest);
      return "retry".equals(emandateVerificationResponse.getEMandateStatus());
  }

  private String getLastTransactionId(ApplicationDataResponse applicationDataResponse) {
    if(applicationDataResponse.getApplicationData().containsKey("emandateRedirectionreadRepairData")){
      LinkedHashMap<String,Object> emandateTxnId = (LinkedHashMap<String, Object>) applicationDataResponse.getApplicationData().get("emandateRedirectionreadRepairData");
      if(emandateTxnId.containsKey("mandateId")){
        return (String) emandateTxnId.get("mandateId");
      }
    }
    return null;
  }

  private String getTransactionId(ApplicationDataResponse applicationDataResponse) {
    if(applicationDataResponse.getApplicationData().containsKey("generateEmandateUrl")) {
      EmandateResponse emandateResponse = ObjectMapperUtil.get().convertValue(applicationDataResponse.getApplicationData().get("generateEmandateUrl"), EmandateResponse.class);
      EmandatePayload emandatePayload = emandateResponse.getPayload();
      if(emandatePayload.getProperties().containsKey("DATA")) {
        String emandateData = emandatePayload.getProperties().get("DATA");
        EmandateLenderRequest emandateLenderRequest = new EmandateLenderRequest();
        emandateLenderRequest.setResponse(emandateData);
        EmandateLenderResponse emandateLenderResponse = plClient.decryptEmanadateUrl("idfc", emandateLenderRequest);
        if(emandateLenderResponse.getProperties().containsKey("MERCHENTTXNID")) {
          return emandateLenderResponse.getProperties().get("MERCHENTTXNID");
        }
      }
    }
    return null;
  }


  public UserActionRequest getDummyUserActionRepairRequest(ApplicationDataResponse applicationDataResponse) {
    MandateSubmitRequest dummyFormSubmitRequest = new MandateSubmitRequest();
    dummyFormSubmitRequest.setType(UserRequestActionType.MANDATE);
    dummyFormSubmitRequest.setMandateResponseString("readRepair");
    return dummyFormSubmitRequest;
  }
  private static FormAutoSubmitResponse getFormAutoSubmitResponseContext(EmandateResponse response) {
    FormAutoSubmitResponse actionResponseContext = new FormAutoSubmitResponse();
    actionResponseContext.setAction(getAction(response));
    actionResponseContext.setForm(getAutoSubmitForm(response));
    return actionResponseContext;
  }

  private static AutoSubmitForm getAutoSubmitForm(EmandateResponse response) {
    AutoSubmitForm form = new AutoSubmitForm();
    form.setFormFields(getFormInputs(response));
    return form;
  }

  private static Action getAction(EmandateResponse response) {
    Action action = new Action();
    action.setUrl(response.getUrl());
    action.setScreenType("NAVIGATION");
    return action;
  }

  private static List<FormInput> getFormInputs(EmandateResponse response) {
    List<FormInput> formInputs = new ArrayList<>();
    Map<String, String> properties = response.getPayload().getProperties();
    for (Entry<String, String> property : properties.entrySet()) {
      FormInput formInput = createFormInput(property);
      formInputs.add(formInput);
    }
    return formInputs;
  }

  private static FormInput createFormInput(Entry<String, String> property) {
    FormInput formInput = new FormInput();
    formInput.setName(property.getKey());
    formInput.setType("TEXT");
    formInput.setValue(property.getValue());
    return formInput;
  }
}
