package com.supermoney.ams.bridge.models.application;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.supermoney.ams.bridge.exceptions.MissingConfigException;
import com.supermoney.ams.bridge.models.ApplicationConfig;
import com.supermoney.ams.bridge.models.ApplicationState;
import com.supermoney.ams.bridge.models.InteractionConfig;
import com.supermoney.ams.bridge.models.LifecycleConfig;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * V1ApplicationConfig is for Current/New ApplicationType like PERSONAL_LOAN_IDFC, LEAD etc.
 */
@Getter
public class V1ApplicationConfig implements ApplicationConfig {
  @JsonProperty
  private InteractionConfig defaultInteraction;
  @JsonProperty("taskKeysList")
  private List<TaskKey> taskKeysList;
  @JsonProperty("applicationStatesList")
  private List<ApplicationStateKey> applicationStatesList;
  @JsonProperty
  private Map<String, InteractionConfig> interactionKeyMap;

  @Override
  public Optional<InteractionConfig> getUserInteraction(ApplicationState applicationState) {
    Optional<String> interactionKey = findInteractionKey(applicationState);
    return interactionKey
        .filter(e -> interactionKeyMap.containsKey(e))
        .map(e -> interactionKeyMap.get(e));
  }

  @Override
  public Optional<String> findInteractionKey(ApplicationState applicationState) {
    String taskKey = applicationState.getTaskKey();
    if (StringUtils.isNotBlank(taskKey)) {
      return getTaskKeyBasedInteractionKey(taskKey);
    }
    String state = applicationState.getApplicationState();
    return getApplicationStateBasedInteractionKey(state);
  }

  private Optional<String> getApplicationStateBasedInteractionKey(String state) {
    return applicationStatesList.stream()
        .filter(e -> state.equals(e.getApplicationState()))
        .map(ApplicationStateKey::getInteractionKey)
        .findAny();
  }

  private Optional<String> getTaskKeyBasedInteractionKey(String taskKey) {
    return taskKeysList.stream()
        .filter(e -> taskKey.equals(e.getTaskKey()))
        .map(TaskKey::getInteractionKey)
        .findAny();
  }

  @Override
  public InteractionConfig getDefault() {
    if (defaultInteraction == null) {
        throw MissingConfigException.forState("default");
    }
    return defaultInteraction;
  }

  @Override
  public LifecycleConfig getLifecycle(ApplicationState applicationState) {
    String taskKey = applicationState.getTaskKey();
    if (StringUtils.isNotBlank(taskKey)) {
      return getTaskKeyBasedLifecycleConfig(taskKey);
    }
    String state = applicationState.getApplicationState();
    return getApplicationStateBasedLifecycleConfig(state);
  }

  private LifecycleConfig getTaskKeyBasedLifecycleConfig(String taskKey) {
    return taskKeysList.stream()
        .filter(e -> taskKey.equals(e.getTaskKey()))
        .map(TaskKey::getLifecycleConfig)
        .filter(Objects::nonNull)
        .findFirst()
        .orElse(new LifecycleConfig(TimeUnit.DAYS, 0));
  }

  private LifecycleConfig getApplicationStateBasedLifecycleConfig(String state) {
    return applicationStatesList.stream()
        .filter(e -> Objects.equals(state, e.getApplicationState()))
        .map(ApplicationStateKey::getLifecycleConfig)
        .filter(Objects::nonNull)
        .findFirst()
        .orElse(new LifecycleConfig(TimeUnit.DAYS, 0));
  }

}
