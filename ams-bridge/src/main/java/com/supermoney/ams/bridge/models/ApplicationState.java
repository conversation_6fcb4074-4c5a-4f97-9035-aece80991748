package com.supermoney.ams.bridge.models;

import com.flipkart.fintech.winterfell.api.request.PendingTask;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import java.util.List;
import lombok.Builder;
import lombok.Getter;
import lombok.NonNull;
import lombok.ToString;
import org.apache.commons.collections4.CollectionUtils;

@Getter
@Builder
@ToString
public class ApplicationState {
  @NonNull
  private String applicationType;
  @NonNull
  private String applicationState;
  private String taskKey;
  private String applicationId;
  private String accountId;

  public static ApplicationState create(ApplicationDataResponse applicationDataResponse) {
    String taskKey = extractTaskKey(applicationDataResponse);
    return ApplicationState.builder()
        .accountId(applicationDataResponse.getExternalUserId())
        .applicationId(applicationDataResponse.getApplicationId())
        .applicationState(applicationDataResponse.getApplicationState())
        .applicationType(applicationDataResponse.getApplicationType())
        .taskKey(taskKey)
        .build();
  }

  private static String extractTaskKey(ApplicationDataResponse applicationDataResponse) {
    List<PendingTask> pendingTask = applicationDataResponse.getPendingTask();
    if (!CollectionUtils.isEmpty(pendingTask)) {
      return pendingTask.get(0).getTaskKey();
    }
    return "";
  }
}
