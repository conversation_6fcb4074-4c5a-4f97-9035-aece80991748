package com.supermoney.ams.bridge.repairer;

import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.models.interaction.RepairableFormInteractionConfig;

public interface Repairer {

    boolean isRepairNeeded(ApplicationDataResponse response, RepairableFormInteractionConfig config);
    UserActionRequest getDummyUserActionRequest(ApplicationDataResponse response, RepairableFormInteractionConfig config);
}
