package com.supermoney.ams.bridge.repairer;

import com.flipkart.fintech.pandora.api.model.pl.response.LoanUtilityResponse;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.FormSubmitRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserActionRequest;
import com.flipkart.fintech.pinaka.api.request.v6.useraction.UserRequestActionType;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.google.common.collect.ImmutableMap;
import com.supermoney.ams.bridge.models.interaction.RepairableFormInteractionConfig;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;

import static com.supermoney.ams.bridge.repairer.FormRepairerUtil.getFormSubmitRequest;

public class ApplicationStateRepairer implements Repairer {

    private static final String GET_PAYMENT_DETAILS = "getPaymentDetails";

    @Override
    public boolean isRepairNeeded(ApplicationDataResponse response, RepairableFormInteractionConfig config) {
        if (response.getApplicationData().containsKey(GET_PAYMENT_DETAILS)) {
            LoanUtilityResponse loanUtilityResponse = ObjectMapperUtil.get()
                    .convertValue(response.getApplicationData().get(GET_PAYMENT_DETAILS), LoanUtilityResponse.class);
            if (loanUtilityResponse.getResourceData().getPaymentStatus().equals("SUCCESS")) {
                return false;
            }
        }
        return true;
    }

    @Override
    public UserActionRequest getDummyUserActionRequest(ApplicationDataResponse response, RepairableFormInteractionConfig config) {
        FormSubmitRequest dummyFormSubmitRequest = getFormSubmitRequest(response);
        dummyFormSubmitRequest.setFormData(ImmutableMap.of("refreshPaymentDetails", true));
        return dummyFormSubmitRequest;
        
    }
}
