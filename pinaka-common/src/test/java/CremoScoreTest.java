import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.cri.alfred.api.response.v3.UserProfileResponseV3;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.common.alfredclient.AlfredClient;
import com.flipkart.fintech.pinaka.common.alfredclient.AlfredClientConfig;
import com.flipkart.fintech.pinaka.common.alfredclient.AlfredClientImpl;
import com.flipkart.fintech.pinaka.common.constants.DexterConstants;
import com.flipkart.fintech.pinaka.common.dexterClient.DexterClient;
import com.flipkart.fintech.pinaka.common.dexterClient.DexterClientConfig;
import com.flipkart.fintech.pinaka.common.dexterClient.DexterClientImpl;
import com.flipkart.fintech.pinaka.common.userprofilescores.UserProfileCremoScore;
import com.flipkart.fintech.pinaka.common.userprofilescores.UserProfileCremoScoreImpl;
import com.flipkart.fintech.pinaka.common.userprofilescores.UserProfileScores;
import com.flipkart.fintech.pinaka.common.userprofilescores.UserProfileScoresImpl;
import com.flipkart.kloud.authn.AuthToken;
import com.flipkart.kloud.authn.AuthTokenService;
import com.flipkart.kloud.config.DynamicBucket;
import com.sumo.crisys.client.CrisysClient;
import org.glassfish.jersey.client.JerseyClientBuilder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import javax.ws.rs.client.Client;

import static org.testng.AssertJUnit.assertNotNull;

@RunWith(MockitoJUnitRunner.class)
public class CremoScoreTest {


    private UserProfileCremoScore userProfileCremoScore;

    @Mock
    private UserProfileScores userProfileScores;
    @Mock
    private AlfredClient alfredClient;
    @Mock
    private DexterClient dexterClient;
    @Mock
    private CrisysClient crisysClient;
    @Mock
    private DynamicBucket dynamicBucket;

    @Before
    public void setUp() throws Exception {
        Client client = new JerseyClientBuilder().build();
        alfredClient  = new AlfredClientImpl(client,new AlfredClientConfig("","",""));
        AuthTokenService authTokenService = AuthTokenService.getInstance();
        if(!authTokenService.isInitialized()) {
            AuthTokenService.init("https://service.authn-prod.fkcloud.in/v3", "pinaka-service", "VdsO8BjanfTADqp3RNaaDm0bkm14iyVGJ3lUuGdaSTS+7Ba0");
        }
        dexterClient = new DexterClientImpl(client, AuthTokenService.getInstance(), new DexterClientConfig(){{setUrl("http://10.83.36.36:80");setTargetClientId("dexter-prod");}});
        userProfileScores = new UserProfileScoresImpl(alfredClient, dexterClient, crisysClient, dynamicBucket);
        userProfileCremoScore = new UserProfileCremoScoreImpl(userProfileScores, dynamicBucket);
    }

    // todo: rohan
    @Test
    public void getCremoScore() {
        Mockito.when(dynamicBucket.getBoolean(DexterConstants.DEXTER_FLAG)).thenReturn(Boolean.FALSE);
        Double cremoScore = userProfileCremoScore.getUserCremoScore(MerchantUser.getMerchantUser("FLIPKART",
                "ACC6722F15B35D14D36B0D46FA3C930AD8BO", "ACC6722F15B35D14D36B0D46FA3C930AD8BO"), "requestId123");
        System.out.println(cremoScore);
//        assertNotNull(cremoScore);
    }

    @Test
    public void getCremoScoreByUserProfile() {
        Mockito.when(dynamicBucket.getBoolean(DexterConstants.DEXTER_FLAG)).thenReturn(Boolean.FALSE);
        UserProfileResponseV3 userProfile = userProfileScores.getUserProfile(MerchantUser.getMerchantUser("FLIPKART",
                "ACC6722F15B35D14D36B0D46FA3C930AD8BO", "ACC6722F15B35D14D36B0D46FA3C930AD8BO"));
        Double cremoScore = userProfileCremoScore.getCremoScoreByUserProfile(userProfile, null);
        System.out.println(cremoScore);
//        assertNotNull(cremoScore);
    }
}
