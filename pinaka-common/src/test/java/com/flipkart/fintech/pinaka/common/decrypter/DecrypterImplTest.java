package com.flipkart.fintech.pinaka.common.decrypter;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.Test;

public class DecrypterImplTest {

  DecrypterImpl decrypter = new DecrypterImpl();;

  @Test
  public void testDecryptString() {
    String encryptedString = "";
    String decryptedString = decrypter.decryptString(encryptedString);
    assertEquals("", decryptedString);
  }

}