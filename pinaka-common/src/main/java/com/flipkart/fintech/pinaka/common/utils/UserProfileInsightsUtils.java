package com.flipkart.fintech.pinaka.common.utils;

import com.flipkart.affordability.underwriting.model.dexter.DataPoint;
import com.flipkart.affordability.underwriting.model.dexter.FetchInsightSet;
import com.flipkart.affordability.underwriting.model.dexter.FetchUserProfileResponse;
import com.flipkart.cri.alfred.api.model.v3.insight.Feature;
import com.flipkart.cri.alfred.api.model.v3.insight.MLModelScore;
import com.flipkart.cri.alfred.api.model.v3.userprofile.UserCBCProfileV3;
import com.flipkart.cri.alfred.api.response.v3.UserProfileResponseV3;
import com.flipkart.fintech.pinaka.common.constants.AlfredConstants;
import com.flipkart.fintech.pinaka.common.constants.DexterConstants;
import com.flipkart.kloud.config.DynamicBucket;
import com.sumo.crisys.api.CreditProfile;
import com.sumo.crisys.api.RiskModel;
import com.sumo.crisys.api.Score;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import java.util.*;

import static org.reflections.Reflections.log;

public class UserProfileInsightsUtils {


    public static Double getBinScore(UserProfileResponseV3 userProfileResponseV3, FetchUserProfileResponse fetchUserProfileResponse, DynamicBucket dynamicBucket) {
        Double binScore = null;

        Boolean dexterFlag = dynamicBucket.getBoolean(DexterConstants.DEXTER_FLAG);
        if(dexterFlag){
            List<FetchInsightSet> fetchInsightSetList = fetchUserProfileResponse.getInsightSets();
            Optional<List<DataPoint>> scoresList = getJRMV3MLModelScoresV2(fetchInsightSetList);

            if(scoresList.isPresent()){
                Optional<DataPoint> scoreModel = fetchBinScoreV2(scoresList.get());
                if(scoreModel.isPresent()){
                    DataPoint score = scoreModel.get();
                    binScore = Double.valueOf(score.getValue());
                    log.info("Value of binScore SCORE in JRMV3 in Dexter block of code:{}", binScore);
                }
            }
        } else {
            Optional<List<MLModelScore>> modelScoreList = getJRMV2MLModelScores(userProfileResponseV3);

            if (modelScoreList.isPresent()) {
                Optional<MLModelScore> mlModelScore = fetchBinScore(modelScoreList.get());
                if (mlModelScore.isPresent()) {
                    MLModelScore modelScore = mlModelScore.get();
                    binScore = Double.valueOf(modelScore.getScoreValue());
                    log.info("Value of binScore SCORE in JRMV3 in Alfred block of code:{}", binScore);
                }
            }
        }

        return binScore;
    }

    public static Double getBin(UserProfileResponseV3 userProfileResponseV3, FetchUserProfileResponse fetchUserProfileResponse, DynamicBucket dynamicBucket) {
        Double binScore = null;

        Boolean dexterFlag = dynamicBucket.getBoolean(DexterConstants.DEXTER_FLAG);
        if(dexterFlag){
            Optional<List<DataPoint>> optionalScoreListJRMV3 = getJRMV3MLModelScoresV2(fetchUserProfileResponse.getInsightSets());
            if(optionalScoreListJRMV3.isPresent()){
                Optional<DataPoint> optionalScoreJRMV3 = fetchBinScoreModel(optionalScoreListJRMV3.get());
                if(optionalScoreJRMV3.isPresent()){
                    DataPoint score = optionalScoreJRMV3.get();
                    binScore = Double.valueOf(score.getValue());
                    log.info("Value of EQUAL_BIN in JRMV3 Dexter block of code:{}",binScore);
                }
            }
        } else {
            Optional<List<MLModelScore>> modelScoreList = getJRMV2MLModelScores(userProfileResponseV3);
            if (modelScoreList.isPresent()) {
                Optional<MLModelScore> mlModelScore = fetchBin(modelScoreList.get());
                if (mlModelScore.isPresent()) {
                    MLModelScore modelScore = mlModelScore.get();
                    binScore = Double.valueOf(modelScore.getScoreValue());
                    log.info("Value of EQUAL_BIN in JRMV3 Alfred block of code:{}",binScore);
                }
            }
        }

        return binScore;
    }

    public static String getShippingAddressId(UserProfileResponseV3 userProfileResponseV3, FetchUserProfileResponse fetchUserProfileResponse, DynamicBucket dynamicBucket) {
        String shippingAddressId = null;
        Boolean dexterFlag = dynamicBucket.getBoolean(DexterConstants.DEXTER_FLAG);
        if(dexterFlag){
            List<FetchInsightSet> insightSetList = fetchUserProfileResponse.getInsightSets();
            Optional<FetchInsightSet> optional = fetchUserProfileAddressV2(insightSetList);

            if (optional.isPresent()) {
                FetchInsightSet fetchInsightSet = optional.get();
                List<DataPoint> featureList = fetchInsightSet.getFeatures();
                Optional<DataPoint> optionalFeature = fetchMostUsedAddressIdV2(featureList);
                if (optionalFeature.isPresent()) {
                    DataPoint feature = optionalFeature.get();
                    shippingAddressId = feature.getValue();
                    log.info("Value of shippingAddressId - li in Dexter block of code:{}", shippingAddressId);
                }
            }
            if (Objects.isNull(shippingAddressId)) {
                shippingAddressId = getFPGShippingAddressIdV2(insightSetList);
                log.info("Value of shippingAddressId -FPG in Dexter block of code:{}", shippingAddressId);
            }
        }else {
            List<UserCBCProfileV3> userCBCProfileV3List = userProfileResponseV3.getUserProfileV3();
            Optional<UserCBCProfileV3> optional = fetchUserProfileAddress(userCBCProfileV3List);

            if (optional.isPresent()) {
                UserCBCProfileV3 userCBCProfileV3 = optional.get();
                List<Feature> featureList = userCBCProfileV3.getFeatures();
                Optional<Feature> optionalFeature = fetchMostUsedAddressId(featureList);
                if (optionalFeature.isPresent()) {
                    Feature feature = optionalFeature.get();
                    shippingAddressId = feature.getFeatureValue();
                }
            }
            if (Objects.isNull(shippingAddressId)) {
                shippingAddressId = getFPGShippingAddressId(userCBCProfileV3List);
            }
            log.info("Value of shippingAddressId Alfred block of code:{}", shippingAddressId);
        }
        return shippingAddressId;
    }

    private static String getFPGShippingAddressId(List<UserCBCProfileV3> userCBCProfileV3List) {
        String shippingAddressId = null;
        Optional<UserCBCProfileV3> optionalFPG = fetchFPGUserProfileAddress(userCBCProfileV3List);
        if (optionalFPG.isPresent()) {
            UserCBCProfileV3 userCBCProfileV3 = optionalFPG.get();
            List<Feature> featureList = userCBCProfileV3.getFeatures();
            Optional<Feature> optionalFeatureAAC = fetchAacMostUsedAddressId(featureList);
            if (optionalFeatureAAC.isPresent()) {
                Feature feature = optionalFeatureAAC.get();
                shippingAddressId = feature.getFeatureValue();
            }
            if(Objects.isNull(shippingAddressId)){
                Optional<Feature> optionalFeatureNONAAC = fetchNonAacMostUsedAddressId(featureList);
                if (optionalFeatureNONAAC.isPresent()) {
                    Feature feature = optionalFeatureNONAAC.get();
                    shippingAddressId = feature.getFeatureValue();
                }
            }
        }
        return shippingAddressId;
    }

    private static String getFPGShippingAddressIdV2(List<FetchInsightSet> insightSetList) {
        String shippingAddressId = null;
        Optional<FetchInsightSet> optionalFPG = fetchFPGUserProfileAddressV2(insightSetList);
        if (optionalFPG.isPresent()) {
            FetchInsightSet fetchInsightSet = optionalFPG.get();
            List<DataPoint> featureList = fetchInsightSet.getFeatures();
            Optional<DataPoint> optionalFeatureAAC = fetchAacMostUsedAddressIdV2(featureList);
            if (optionalFeatureAAC.isPresent()) {
                DataPoint feature = optionalFeatureAAC.get();
                shippingAddressId = feature.getValue();
            }
            if(Objects.isNull(shippingAddressId)){
                Optional<DataPoint> optionalFeatureNONAAC = fetchNonAacMostUsedAddressIdV2(featureList);
                if (optionalFeatureNONAAC.isPresent()) {
                    DataPoint feature = optionalFeatureNONAAC.get();
                    shippingAddressId = feature.getValue();
                }
            }
        }
        log.info("Value of shippingAddressId in Dexter block of code:{}", shippingAddressId);
        return shippingAddressId;
    }

    private static Optional<List<MLModelScore>> getJRMV2MLModelScores(UserProfileResponseV3 userProfileResponseV3) {
        List<UserCBCProfileV3> userCBCProfileV3List = userProfileResponseV3.getUserProfileV3();
        Optional<UserCBCProfileV3> optional = fetchUserProfileJRMV2(userCBCProfileV3List); // this is v3
        if (optional.isPresent()) {
            UserCBCProfileV3 userCBCProfileV3 = optional.get();
            return Optional.of(userCBCProfileV3.getScores());
        }

        return Optional.empty();
    }

    private static Optional<List<DataPoint>> getJRMV3MLModelScoresV2(List<FetchInsightSet> fetchInsightSetList) {
        Optional<FetchInsightSet> optional = fetchUserProfileJRMV3(fetchInsightSetList); // this is v3
        if (optional.isPresent()) {
            FetchInsightSet fetchInsightSet = optional.get();
            return Optional.of(fetchInsightSet.getScores());
        }

        return Optional.empty();
    }

    private static Optional<UserCBCProfileV3> fetchUserProfileJRMV2(List<UserCBCProfileV3> userCBCProfileV3List) {
        Optional<UserCBCProfileV3> optional = Optional.empty();
        if (!Objects.isNull(userCBCProfileV3List)) {
            optional = userCBCProfileV3List.stream().filter(userCBCProfileV3 ->
                    userCBCProfileV3.getVersion().equals(AlfredConstants.CBC_JRM_V3)).findFirst();
        }

        return optional;
    }

    private static Optional<FetchInsightSet> fetchUserProfileJRMV3(List<FetchInsightSet> fetchInsightSetList) {
        Optional<FetchInsightSet> optional = Optional.empty();
        if (!Objects.isNull(fetchInsightSetList)) {
            optional = fetchInsightSetList.stream().filter(fetchInsightSet ->
                    fetchInsightSet.getName().equals(DexterConstants.CBC_JRM) && fetchInsightSet.getVersion().equals(DexterConstants.CBC_JRM_V3_VERSION))
                    .findFirst();
            log.info("Value of CBC_JRM CBC_JRM_V3_VERSION optional FetchInsightSet in Dexter block of code:{}", (optional.isPresent())?optional.get():null);
        }

        return optional;
    }

    private static Optional<MLModelScore> fetchBinScore(List<MLModelScore> modelScoreList) {
        Optional<MLModelScore> optional = Optional.empty();
        if (!Objects.isNull(modelScoreList)) {
            optional = modelScoreList.stream().filter(score ->
                    score.getScoreName().equals(AlfredConstants.SCORE)).findFirst();
        }

        return optional;
    }
    private static Optional<DataPoint> fetchBinScoreV2(List<DataPoint> scoresList) {
        Optional<DataPoint> optional = Optional.empty();
        if (Objects.nonNull(scoresList)) {
            optional = scoresList.stream().filter(score ->
                    score.getName().equals(DexterConstants.SCORE)).findFirst();
            log.info("Value of SCORE in JRMV3 in Dexter block of code:{}", (optional.isPresent())?optional.get():null);
        }

        return optional;
    }

    private static Optional<MLModelScore> fetchBin(List<MLModelScore> modelScoreList) {
        Optional<MLModelScore> optional = Optional.empty();
        if (!Objects.isNull(modelScoreList)) {
            optional = modelScoreList.stream().filter(score ->
                    score.getScoreName().equals(AlfredConstants.EQUAL_BIN)).findFirst();
        }

        return optional;
    }

    private static Optional<DataPoint> fetchBinScoreModel(List<DataPoint> scoresList){
        Optional<DataPoint> optional = Optional.empty();
        if(Objects.nonNull(scoresList)){
            optional = scoresList.stream().filter(score ->
                    score.getName().equals(DexterConstants.EQUAL_BIN)).findFirst();
            log.info("Value of EQUAL_BIN in CBC_JRM CBC_JRM_V3_VERSION optional ScoresList in Dexter block of code:{}", (optional.isPresent())?optional.get():null);
        }
        return optional;
    }

    private static Optional<UserCBCProfileV3> fetchUserProfileAddress(List<UserCBCProfileV3> userCBCProfileV3List) {
        Optional<UserCBCProfileV3> optional = Optional.empty();
        if (!Objects.isNull(userCBCProfileV3List)) {
            optional = userCBCProfileV3List.stream().filter(userCBCProfileV3 ->
                    userCBCProfileV3.getVersion().equals(AlfredConstants.ADDRESS_CONFIDENCE_V1)).findFirst();
        }

        return optional;
    }
    private static Optional<FetchInsightSet> fetchUserProfileAddressV2(List<FetchInsightSet> fetchInsightSetList) {
        Optional<FetchInsightSet> optional = Optional.empty();
        if (!Objects.isNull(fetchInsightSetList)) {
            optional = fetchInsightSetList.stream().filter(fetchInsightSet ->
                    fetchInsightSet.getName().equals(DexterConstants.CBC_LI_DEXTER_MODEL_NAME)).findFirst();
            log.info("Value of CBC_LI_DEXTER_MODEL_NAME list in Dexter block of code:{}", (optional.isPresent())?optional.get():null);
        }

        return optional;
    }

    private static Optional<UserCBCProfileV3> fetchFPGUserProfileAddress(List<UserCBCProfileV3> userCBCProfileV3List) {
        Optional<UserCBCProfileV3> optional = Optional.empty();
        if (!Objects.isNull(userCBCProfileV3List)) {
            optional = userCBCProfileV3List.stream()
                    .filter(Objects::nonNull)
                    .filter(userCBCProfileV3 -> AlfredConstants.FPG_INSIGHTS_V1.equals(userCBCProfileV3.getVersion()))
                    .findFirst();
        }

        return optional;
    }

    private static Optional<FetchInsightSet> fetchFPGUserProfileAddressV2(List<FetchInsightSet> insightSetList) {
        Optional<FetchInsightSet> optional = Optional.empty();
        if (!Objects.isNull(insightSetList)) {
            optional = insightSetList.stream()
                    .filter(Objects::nonNull)
                    .filter(insightSet -> DexterConstants.CBC_FPG_SM_INSIGHTS_DEXTER_MODEL_NAME.equals(insightSet.getName()))
                    .findFirst();
            log.info("Value of CBC_FPG_SM_INSIGHTS_DEXTER_MODEL_NAME in Dexter block of code:{}", (optional.isPresent())?optional.get():null);
        }

        return optional;
    }

    private static Optional<Feature> fetchMostUsedAddressId(List<Feature> featureList) {
        Optional<Feature> optional = Optional.empty();
        if (!Objects.isNull(featureList)) {
            optional = featureList.stream()
                    .filter(Objects::nonNull)
                    .filter(feature -> AlfredConstants.MOST_USED_ADDRESS_ID.equals(feature.getFeatureName()))
                    .findFirst();
        }

        return optional;
    }
    private static Optional<DataPoint> fetchMostUsedAddressIdV2(List<DataPoint> featureList) {
        Optional<DataPoint> optional = Optional.empty();
        if (!Objects.isNull(featureList)) {
            optional = featureList.stream()
                    .filter(Objects::nonNull)
                    .filter(feature -> DexterConstants.MOST_USED_ADDRESS_ID.equals(feature.getName()))
                    .findFirst();
        }

        return optional;
    }

    private static Optional<Feature> fetchNonAacMostUsedAddressId(List<Feature> featureList) {
        Optional<Feature> optional = Optional.empty();
        if (!Objects.isNull(featureList)) {
            optional = featureList.stream()
                    .filter(Objects::nonNull)
                    .filter(feature -> AlfredConstants.MOST_USER_ADDRESS_ID_NON_AAC.equals(feature.getFeatureName()))
                    .findFirst();
        }

        return optional;
    }

    private static Optional<DataPoint> fetchNonAacMostUsedAddressIdV2(List<DataPoint> featureList) {
        Optional<DataPoint> optional = Optional.empty();
        if (!Objects.isNull(featureList)) {
            optional = featureList.stream()
                    .filter(Objects::nonNull)
                    .filter(feature -> DexterConstants.MOST_USER_ADDRESS_ID_NON_AAC.equals(feature.getName()))
                    .findFirst();
            log.info("Value of MOST_USER_ADDRESS_ID_NON_AAC in Dexter block of code:{}", (optional.isPresent())?optional.get():null);
        }

        return optional;
    }

    private static Optional<Feature> fetchAacMostUsedAddressId(List<Feature> featureList) {
        Optional<Feature> optional = Optional.empty();
        if (!Objects.isNull(featureList)) {
            optional = featureList.stream()
                    .filter(Objects::nonNull)
                    .filter(feature -> AlfredConstants.MOST_USER_ADDRESS_ID_AAC.equals(feature.getFeatureName()))
                    .findFirst();
        }

        return optional;
    }

    private static Optional<DataPoint> fetchAacMostUsedAddressIdV2(List<DataPoint> featureList) {
        Optional<DataPoint> optional = Optional.empty();
        if (!Objects.isNull(featureList)) {
            optional = featureList.stream()
                    .filter(Objects::nonNull)
                    .filter(feature -> DexterConstants.MOST_USER_ADDRESS_ID_AAC.equals(feature.getName()))
                    .findFirst();
            log.info("Value of MOST_USER_ADDRESS_ID_AAC in Dexter block of code:{}", (optional.isPresent())?optional.get():null);
        }

        return optional;
    }

    public static Double getBinScore(CreditProfile creditProfile, String modelVersion) {
        Map<String, List<RiskModel>> modelScoreList = creditProfile.riskModels();
        Double binScore = null;
        if (MapUtils.isNotEmpty(modelScoreList) && CollectionUtils.isNotEmpty(modelScoreList.get(DexterConstants.AXIS_V2))) {
            Optional<RiskModel> model = getVersionedAxisRiskModel(modelScoreList.get(DexterConstants.AXIS_V2), modelVersion);
            if (model.isPresent()) {
                Score score = Optional.ofNullable(model.get().scores()).orElse(new HashMap<>()).get(DexterConstants.BIN_SCORE);
                binScore = Double.valueOf(score.value());
            }
        }

        return binScore;
    }

    public static Double getBin(CreditProfile creditProfile, String modelVersion) {
        Map<String, List<RiskModel>> modelScoreList = creditProfile.riskModels();
        Double bin = null;
        if (MapUtils.isNotEmpty(modelScoreList) && CollectionUtils.isNotEmpty(modelScoreList.get(DexterConstants.AXIS_V2))) {
            Optional<RiskModel> model = getVersionedAxisRiskModel(modelScoreList.get(DexterConstants.AXIS_V2), modelVersion);
            if (model.isPresent()) {
                Score score = Optional.ofNullable(model.get().scores()).orElse(new HashMap<>()).get(DexterConstants.EQUAL_BIN);
                bin = Double.valueOf(score.value());
            }
        }

        return bin;
    }

    private static Optional<RiskModel> getVersionedAxisRiskModel(List<RiskModel> riskModels, String version) {
        Optional<RiskModel> optional = Optional.empty();

        if (CollectionUtils.isNotEmpty(riskModels)) {
            optional = riskModels.stream().filter(model ->
                    version.equals(model.version())).findFirst();
        }

        return optional;
    }
}
