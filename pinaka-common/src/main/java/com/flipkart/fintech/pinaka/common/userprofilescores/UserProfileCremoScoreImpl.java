package com.flipkart.fintech.pinaka.common.userprofilescores;

import com.flipkart.affordability.underwriting.model.dexter.DataPoint;
import com.flipkart.affordability.underwriting.model.dexter.FetchCohort;
import com.flipkart.affordability.underwriting.model.dexter.FetchUserProfileResponse;
import com.flipkart.cri.alfred.api.model.v3.Cohort;
import com.flipkart.cri.alfred.api.model.v3.userprofile.UserCBCProfileV3;
import com.flipkart.cri.alfred.api.response.v3.UserProfileResponseV3;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.common.constants.AlfredConstants;
import com.flipkart.fintech.pinaka.common.constants.DexterConstants;
import com.flipkart.kloud.config.DynamicBucket;
import com.google.inject.Inject;
import lombok.CustomLog;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static org.reflections.Reflections.log;

@CustomLog
public class UserProfileCremoScoreImpl implements UserProfileCremoScore {

    private final UserProfileScores userProfileScores;
    private final DynamicBucket dynamicBucket;

    @Inject
    public UserProfileCremoScoreImpl(UserProfileScores userProfileScores, DynamicBucket dynamicBucket) {
        this.userProfileScores = userProfileScores;
        this.dynamicBucket = dynamicBucket;
    }

    public Double getUserCremoScore(MerchantUser merchantUser, String requestId) {
        try {
            log.info("Getting cremeScore for user_id : {}", merchantUser);
            FetchUserProfileResponse fetchUserProfile = new FetchUserProfileResponse();
            UserProfileResponseV3 userProfile = new UserProfileResponseV3();
            Boolean dexterFlag = dynamicBucket.getBoolean(DexterConstants.DEXTER_FLAG);
            if(dexterFlag){
                fetchUserProfile = userProfileScores.getUserProfileByDexter(requestId, merchantUser);
                if(Objects.isNull(fetchUserProfile)){ return null;}
            }else{
                userProfile = userProfileScores.getUserProfile(merchantUser);
                if (userProfile == null) {
                    return null;
                }
            }
            return getCremoScoreByUserProfile(userProfile, fetchUserProfile);
        } catch (Exception e) {
            log.error("Failed to fetch cremoScore for user id : {} due to : {}  ", merchantUser, e);
        }
        return null;
    }

    @Override
    public Double getCremoScoreByUserProfile(UserProfileResponseV3 userProfile, FetchUserProfileResponse userProfileV2) {
        try {
            Double cremoScore = null;
            Boolean dexterFlag = dynamicBucket.getBoolean(DexterConstants.DEXTER_FLAG);
            if(dexterFlag){
                List<FetchCohort> cohortsList = userProfileV2.getCohorts();
                if(!cohortsList.isEmpty()){
                    Optional<FetchCohort> cremoScoreCohort = fetchCremoScoreV2(cohortsList);
                    if (cremoScoreCohort.isPresent()) {
                        FetchCohort modelScore = cremoScoreCohort.get();
                        if(!modelScore.getCohortSegment().equals(DexterConstants.REST)){
                            cremoScore = Double.valueOf(modelScore.getCohortSegment());
                            log.info("Value of IDFC_SEG cremoScore in Dexter block of code:{}", cremoScore);
                        }
                    }
                }
            } else {
                List<Cohort> modelScoreList = getModelScore(userProfile);

                if (!modelScoreList.isEmpty()) {
                    Optional<Cohort> cremoScoreCohort = fetchCremoScore(modelScoreList);
                    if (cremoScoreCohort.isPresent()) {
                        Cohort modelScore = cremoScoreCohort.get();
                        cremoScore = Double.valueOf(modelScore.getValue());
                        log.info("Value of IDFC_SEG cremoScore in Alfred block of code:{}", cremoScore);
                    }
                }
            }
            log.info("CremeScoreByUserProfile is :{}", cremoScore);
            return cremoScore;
        } catch (Exception e) {
            log.error("Failed to fetch cremoScore from the userProfile due to: {} ", e);
        }
        return null;
    }

    private List<Cohort> getModelScore(UserProfileResponseV3 userProfileResponseV3) {
        List<UserCBCProfileV3> userCBCProfileV3List = userProfileResponseV3.getUserProfileV3();
        Optional<UserCBCProfileV3> optional = fetchNode(userCBCProfileV3List);
        if (optional.isPresent()) {
            UserCBCProfileV3 userCBCProfileV3 = optional.get();
            return userCBCProfileV3.getCohorts();
        }
        return null;
    }

    private Optional<Cohort> fetchCremoScore(List<Cohort> modelScoreList) {
        Optional<Cohort> optional = Optional.empty();
        if (!Objects.isNull(modelScoreList)) {
            optional = modelScoreList.stream().filter(cohort ->
                    cohort.getName().equals(AlfredConstants.IDFC_SEG)).findFirst();
        }

        return optional;
    }

    private Optional<FetchCohort> fetchCremoScoreV2(List<FetchCohort> cohortList) {
        Optional<FetchCohort> optional = Optional.empty();
        if (Objects.nonNull(cohortList)) {
            optional = cohortList.stream().filter(cohort ->
                    DexterConstants.IDFC_SEG.equals(cohort.getName())).findFirst();
            log.info("Value of IDFC_SEG optionalCremoScoreModel in Dexter block of code:{}", (optional.isPresent())?optional.get():null);
        }

        return optional;
    }

    private Optional<UserCBCProfileV3> fetchNode(List<UserCBCProfileV3> userCBCProfileV3List) {
        Optional<UserCBCProfileV3> optional = Optional.empty();
        if (!Objects.isNull(userCBCProfileV3List)) {
            optional = userCBCProfileV3List.stream().filter(userCBCProfileV3 ->
                    userCBCProfileV3.getVersion().equals(AlfredConstants.BNPL_UNDERWRITING)).findFirst();
        }
        return optional;
    }
}
