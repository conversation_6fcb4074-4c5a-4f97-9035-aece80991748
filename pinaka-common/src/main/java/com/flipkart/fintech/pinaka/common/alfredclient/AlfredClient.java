package com.flipkart.fintech.pinaka.common.alfredclient;

import com.flipkart.cri.alfred.api.request.FetchCohortRequests;
import com.flipkart.cri.alfred.api.request.UserConsentVerificationRequest;
import com.flipkart.cri.alfred.api.request.UserDataCollectRequest;
import com.flipkart.cri.alfred.api.request.UserResendConsentRequest;
import com.flipkart.cri.alfred.api.request.UserProfileFetchRequest;
import com.flipkart.cri.alfred.api.request.v3.UserProfileFetchRequestV3;
import com.flipkart.cri.alfred.api.response.UserConsentVerificationResponse;
import com.flipkart.cri.alfred.api.response.UserDataCollectResponse;
import com.flipkart.cri.alfred.api.response.UserProfileResponse;
import com.flipkart.cri.alfred.api.response.UserResendConsentResponse;
import com.flipkart.cri.alfred.api.response.CohortResponse;
import com.flipkart.cri.alfred.api.response.v3.UserProfileResponseV3;
import com.flipkart.fintech.pinaka.common.alfredclient.AlfredClientImpl;
import com.google.inject.ImplementedBy;

@ImplementedBy(AlfredClientImpl.class)
public interface AlfredClient {
    UserProfileResponseV3 fetchUserProfile(UserProfileFetchRequestV3 userProfileFetchRequestV3, String merchantId,
                                           String version) throws RuntimeException;
}
