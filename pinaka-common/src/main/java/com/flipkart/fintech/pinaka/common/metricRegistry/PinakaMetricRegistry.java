package com.flipkart.fintech.pinaka.common.metricRegistry;

import com.codahale.metrics.MetricRegistry;

public class PinakaMetricRegistry {

    private static MetricRegistry metricRegistry;


    private PinakaMetricRegistry(MetricRegistry metricRegistry) {
        PinakaMetricRegistry.metricRegistry = metricRegistry;
    }

    public static synchronized MetricRegistry getMetricRegistry() {
        if (metricRegistry == null) {
            // if instance is null, initialize
            metricRegistry = new MetricRegistry();
        }
        return metricRegistry;
    }
}
