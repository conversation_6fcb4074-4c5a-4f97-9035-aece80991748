package com.flipkart.fintech.pinaka.web.v1;

import com.codahale.metrics.annotation.ExceptionMetered;
import com.codahale.metrics.annotation.Timed;
import com.flipkart.affordability.underwriting.model.dexter.FetchUserProfileRequest;
import com.flipkart.affordability.underwriting.model.dexter.FetchUserProfileResponse;
import com.flipkart.fintech.pinaka.client.PinakaClientException;
import com.flipkart.fintech.pinaka.client.VaradhiClentConfig;
import com.flipkart.fintech.pinaka.common.constants.DexterConstants;
import com.flipkart.fintech.pinaka.common.dexterClient.DexterClient;
import com.flipkart.fintech.pinaka.common.dexterClient.DexterClientConfig;
import com.flipkart.fintech.pinaka.common.dexterClient.DexterClientImpl;
import com.flipkart.fintech.pinaka.common.varadhi.RobinhoodEventRequest;
import com.flipkart.fintech.pinaka.common.varadhi.VaradhiClient;
import com.flipkart.fintech.pinaka.common.varadhi.VaradhiClientImpl;
import com.flipkart.kloud.authn.AuthTokenService;
import com.google.inject.Inject;
import io.dropwizard.hibernate.UnitOfWork;
import io.swagger.annotations.ApiOperation;
import lombok.CustomLog;
import org.glassfish.jersey.client.ClientProperties;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Null;
import javax.ws.rs.*;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.ClientBuilder;
import javax.ws.rs.core.MediaType;

@CustomLog
@Consumes(MediaType.APPLICATION_JSON)
@Produces(MediaType.APPLICATION_JSON)
@Path("1/common/")
public class PinakaCommonResource {

    private VaradhiClient varadhiClient;
    private DexterClient dexterClient;
    private final AuthTokenService authTokenService;
    private final DexterClientConfig dexterClientConfig;
    @Inject
    public PinakaCommonResource(VaradhiClentConfig varadhiClentConfig, AuthTokenService authTokenService, DexterClientConfig dexterClientConfig){
        this.authTokenService = authTokenService;
        this.dexterClientConfig = dexterClientConfig;
        Client client = getClient();
        this.varadhiClient = new VaradhiClientImpl(varadhiClentConfig, client);
        this.dexterClient = new DexterClientImpl(client,authTokenService,dexterClientConfig);
    }


    private Client getClient() {
        Client client = ClientBuilder.newClient();
        client.property(ClientProperties.CONNECT_TIMEOUT, 60000);
        client.property(ClientProperties.READ_TIMEOUT, 60000);
        return client;
    }

    @POST
    @Timed
    @ExceptionMetered
    @ApiOperation("Push Robinhood Event")
    @Path("/event")
    @UnitOfWork
    public void pushEvent(@Valid RobinhoodEventRequest eventRequest,
                          @NotNull @HeaderParam("X-Request-Id") String requestId) {
        try {
            this.varadhiClient.pushEvent(eventRequest);
        } catch (PinakaClientException e) {
            log.error("Error while sending robinhood event : {}",e.getMessage());
            throw new RuntimeException(e);
        }
    }

    @POST
    @Timed
    @ExceptionMetered
    @ApiOperation("Get dexter score")
    @Path("/dexter")
    @UnitOfWork
    public FetchUserProfileResponse getDexterResponse(@Valid String accountId,
                                              @NotNull @HeaderParam("X-Request-Id") String requestId) {
        FetchUserProfileRequest fetchUserProfileRequest  = new FetchUserProfileRequest();
        fetchUserProfileRequest.setAccountId(accountId);
        fetchUserProfileRequest.setAddressIds(null);
        return dexterClient.fetchUserProfile(fetchUserProfileRequest, DexterConstants.DEXTER_CLIENT_ID, requestId);
    }

}
