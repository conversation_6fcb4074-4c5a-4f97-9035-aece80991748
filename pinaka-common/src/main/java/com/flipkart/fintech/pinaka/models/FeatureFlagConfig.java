package com.flipkart.fintech.pinaka.models;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;

import java.util.Set;

@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
public class FeatureFlagConfig {
    private static final String IOS = "ios";
    private static final String ANDROID = "android";

    private boolean enabledForAll;
    private boolean enabledForCUG;
    private Set<String> cugAccountIds;
    @JsonProperty("iosAppVersionStr")
    private AppVersion iosAppVersion;
    @JsonProperty("androidAppVersionStr")
    private AppVersion androidAppVersion;

    public boolean isFeatureEnabledForUser(String channel, String userAppVersion, String accountId) {
        if (StringUtils.isNotEmpty(channel)) {
            if (IOS.equalsIgnoreCase(channel) && (new AppVersion(userAppVersion)).compareTo(getIosAppVersion()) < 0) {
                return false;
            }
            if (ANDROID.equalsIgnoreCase(channel) && (new AppVersion(userAppVersion)).compareTo(getAndroidAppVersion()) < 0) {
                return false;
            }
        }
        if (isEnabledForAll()) return true;
        return isEnabledForCUG() && cugAccountIds.contains(accountId);
    }
}
