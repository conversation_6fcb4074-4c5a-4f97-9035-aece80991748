package com.flipkart.fintech.pinaka.common.alfredclient;

import com.flipkart.cri.alfred.api.request.v3.UserProfileFetchRequestV3;
import com.flipkart.cri.alfred.api.response.v3.UserProfileResponseV3;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import lombok.CustomLog;

import javax.inject.Inject;
import javax.ws.rs.client.Client;
import javax.ws.rs.client.Entity;
import javax.ws.rs.client.Invocation;
import javax.ws.rs.client.WebTarget;
import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.UUID;

@CustomLog
public class AlfredClientImpl implements AlfredClient {
    public static final String FETCH_USER_PROFILE_PATH = "/alfred/v3/user/profile/fetch";
    public static final String X_CLIENT_ID = "X-Client-Id";
    public static final String X_REQUEST_ID = "X-Request-Id";
    public static final String PL_ALFRED_CLIENT_ID = "CALM";
    private final WebTarget fkWebTarget;

    @Inject
    public AlfredClientImpl(Client client, AlfredClientConfig alfredClientConfig) {
        this.fkWebTarget = client.target("http://************:80");
    }

    @Override
    public UserProfileResponseV3 fetchUserProfile(UserProfileFetchRequestV3 userProfileFetchRequestV3, String merchantId,
                                                  String version) throws RuntimeException {
        // TODO: Myntra: Abstract it once crysis systems is ready
        if (MerchantUser.MerchantKeys.MYNTRA_MERCHANT_KEY.equals(merchantId)) {
            return new UserProfileResponseV3();
        }

        Response response = null;
        UserProfileResponseV3 userProfileResponseV3;
        String path = String.format(FETCH_USER_PROFILE_PATH);
        try {
            Invocation.Builder invocationBuilder = fkWebTarget.path(path)
                    .request(MediaType.APPLICATION_JSON_TYPE);
            invocationBuilder.header(X_CLIENT_ID, PL_ALFRED_CLIENT_ID);
            invocationBuilder.header(X_REQUEST_ID, UUID.randomUUID().toString());
            String clientUserId = userProfileFetchRequestV3.getAccountId();
            response = invocationBuilder.post(Entity.json(userProfileFetchRequestV3));
            if (response.getStatus() != 200) {
                log.error("User profile response for acc id {} is {}", clientUserId,
                        response.readEntity(String.class));
                throw new RuntimeException(String.format("User profile response for account id %s",
                        clientUserId));
            }
            userProfileResponseV3 = response.readEntity(UserProfileResponseV3.class);
        } catch (Exception exp) {
            log.error("Error while fetching profile from alfred. account id: {}", userProfileFetchRequestV3.getAccountId(), exp);
            throw new RuntimeException(exp);
        } finally {
            if (response != null) {
                response.close();
            }
        }

        return userProfileResponseV3;
    }
}
