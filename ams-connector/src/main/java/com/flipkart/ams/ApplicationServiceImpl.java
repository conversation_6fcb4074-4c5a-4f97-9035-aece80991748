package com.flipkart.ams;

import com.flipkart.fintech.citadel.api.enums.ApplicationEntityState;
import com.flipkart.fintech.citadel.api.models.*;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.api.request.v6.AlmDiscardRequest;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.winterfell.api.request.ApplicationResponse;
import com.flipkart.fintech.winterfell.api.request.CreateApplicationRequest;
import com.flipkart.fintech.winterfell.api.request.ResumeApplicationRequest;
import com.flipkart.fintech.winterfell.api.request.UpdatePartnerStateRequest;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.supermoney.ams.bridge.AmsBridge;
import lombok.CustomLog;
import org.jetbrains.annotations.NotNull;

import javax.inject.Inject;
import java.util.*;

@CustomLog
public class ApplicationServiceImpl implements ApplicationService {
    private final WinterfellUtils winterfellUtils;
    private final AmsBridge amsBridge;

    @Inject
    public ApplicationServiceImpl(WinterfellUtils winterfellUtils, AmsBridge amsBridge) {
        this.winterfellUtils = winterfellUtils;
        this.amsBridge = amsBridge;
    }

    @Override
    public ApplicationResponse resumeApplication(MerchantUser merchantUser, String applicationId,
                                                 ResumeApplicationRequest resumeApplicationRequest) throws PinakaException {
        return winterfellUtils.resumeApplication(applicationId, merchantUser, resumeApplicationRequest);
    }

    @Override
    //todo: use state based orchestration here
    public ApplicationDataResponse fetchApplicationData(String applicationId, MerchantUser merchantUser) throws PinakaException {
        return winterfellUtils.fetchApplicationData(applicationId, merchantUser);
    }

    @Override
    public ApplicationDataResponse fetchApplicationData(MerchantUser merchantUser, String applicationId) throws PinakaException {
        return fetchApplicationData(applicationId, merchantUser);
    }

    @Override
    public Optional<ApplicationDataResponse> findActiveApplication(MerchantUser merchantUser,
                                                                   ProductType productType) throws PinakaException {
        String applicationId = winterfellUtils.findActiveApplicationId(merchantUser, productType);
        if (applicationId == null) return Optional.empty();
        return getApplicationDataResponseAndDiscardIfExpired(merchantUser, productType, applicationId);
    }

    @Override
    public ApplicationDataResponse fetchActiveApplicationData(MerchantUser merchantUser,
        String applicationId) throws PinakaException {
        ApplicationDataResponse applicationDataResponse = fetchApplicationData(applicationId,
            merchantUser);
        boolean applicationExpired = checkIfApplicationExpiredAndDiscard(merchantUser,
            applicationDataResponse);
        if (applicationExpired) {
            throw new PinakaException("Application not found.");
        }
        return applicationDataResponse;
    }

    @Override
    public void updatePartnerState(UpdatePartnerStateRequest updatePartnerStateRequest) throws PinakaException {
        winterfellUtils.updatePartnerState(updatePartnerStateRequest);
    }

    @Override
    public Optional<ApplicationDataResponse> findLatestActiveApplicationV2(MerchantUser merchantUser, ProductType productType) throws PinakaException {
        ActiveApplicationsResponse activeApplicationsResponses = winterfellUtils.getActiveApplicationsResponseV2(merchantUser.getSmUserId(), productType.name());
        List<ApplicationData> applications = activeApplicationsResponses.getApplications();
        return getLatestActiveApplicationFromList(applications, merchantUser, productType);
    }

    public Optional<ApplicationDataResponse> getLatestActiveApplicationFromList(List<ApplicationData> applications, MerchantUser merchantUser, ProductType productType) throws PinakaException {
        if(applications.isEmpty()) return Optional.empty();
        String applicationId;
        if(applications.size()>1){
            applicationId = applications.stream().max(Comparator.comparingLong(ApplicationData::getCreatedAt).thenComparing(ApplicationData::getLspApplicationId)).get().getLspApplicationId();
        } else {
            applicationId = applications.get(0).getLspApplicationId();
        }
        return getApplicationDataResponseAndDiscardIfExpired(merchantUser, productType, applicationId);
    }

    @Override
    public ActiveApplicationsResponse findActiveApplicationsForProductTypeV2(MerchantUser merchantUser, ProductType productType) throws PinakaException {
        String product_type = Objects.isNull(productType)?null:productType.name();
        return  winterfellUtils.getActiveApplicationsResponseV2(merchantUser.getSmUserId(), product_type);
    }

    @Override
    public boolean almDiscard(AlmDiscardRequest almDiscardRequest) throws PinakaException {
        return getApplicationDataResponseAndDiscardIfExpired(MerchantUser.getMerchantUser("FLIPKART",
            almDiscardRequest.getSmUserId(), almDiscardRequest.getSmUserId()),
            ProductType.valueOf(almDiscardRequest.getProductType()),almDiscardRequest.getApplicationId()).isPresent();
    }

    @NotNull
    private Optional<ApplicationDataResponse> getApplicationDataResponseAndDiscardIfExpired(MerchantUser merchantUser, ProductType productType, String applicationId) throws PinakaException {
        ApplicationDataResponse applicationDataResponse = winterfellUtils.fetchApplicationData(
                applicationId, merchantUser);
        boolean hasApplicationExpired = amsBridge.hasExpired(applicationDataResponse);
        if (!hasApplicationExpired) {
            return Optional.ofNullable(applicationDataResponse);
        }
        log.info("Application expired for {}", applicationDataResponse.getApplicationId());
        discardApplicationWithLead(merchantUser, applicationDataResponse);
        return Optional.empty(); // this can be made to discard multiple application
    }

    private boolean checkIfApplicationExpiredAndDiscard(MerchantUser merchantUser, ApplicationDataResponse applicationDataResponse) throws PinakaException {
        boolean hasApplicationExpired = amsBridge.hasExpired(applicationDataResponse);
        if (hasApplicationExpired) {
            log.info("Application expired for {}", applicationDataResponse.getApplicationId());
            discardApplicationWithLead(merchantUser, applicationDataResponse);
        }
        return hasApplicationExpired;
    }

    private void discardApplicationWithLead(MerchantUser merchantUser, ApplicationDataResponse applicationDataResponse) throws PinakaException {
        if (applicationDataResponse.getApplicationData().containsKey("leadId")){
            String leadAppId = applicationDataResponse.getApplicationData().get("leadId").toString();
            log.info("Found Lead {} attached to application {}", leadAppId, applicationDataResponse.getApplicationId());
            winterfellUtils.discardApplication(merchantUser, leadAppId);
        }
        winterfellUtils.discardApplication(merchantUser, applicationDataResponse.getApplicationId());
    }

    @Override
    public ActiveApplicationResponse getActiveApplications(MerchantUser merchantUser,
                                                           String applicationType) throws PinakaException {
        return winterfellUtils.getActiveApplications(merchantUser, applicationType);
    }

    @Override
    public ApplicationStateResponse getDiscardedApplications(MerchantUser merchantUser)
            throws PinakaException {
        List<ApplicationEntityState> applicationEntityStates = new ArrayList<>();
        applicationEntityStates.add(ApplicationEntityState.DISCARDED);
        WorkflowStatesRequest request = new WorkflowStatesRequest(merchantUser.getMerchantUserId(), merchantUser.getSmUserId(),
                null, applicationEntityStates);
        return winterfellUtils.getApplications(request);
    }


    @Override
    public Boolean discardApplicationAndLeads(MerchantUser user, String applicationId) {
      try {
        winterfellUtils.discardApplication(user, applicationId);
        ActiveApplicationResponse activeApplications = getActiveApplications(user, null);
        List<String> leadIds = activeApplications.getApplicationList().getOrDefault("LEAD", Collections.emptyList());
        for (String leadId : leadIds) {
          winterfellUtils.discardApplication(user, leadId);
        }
        return Boolean.TRUE;
      } catch (PinakaException e) {
        log.error("Error while discarding applications for user: {}, applicationId : {}", user.getSmUserId(),applicationId);
        throw new RuntimeException(e);
      }
    }

    @Override
    public Boolean discardApplication(MerchantUser user, String applicationId) {
      try {
        winterfellUtils.discardApplication(user, applicationId);
        return Boolean.TRUE;
      } catch (PinakaException e) {
        log.error("Error while discarding application for user: {}, applicationId : {}", user.getSmUserId(),applicationId);
        throw new RuntimeException(e);
      }
    }
}
