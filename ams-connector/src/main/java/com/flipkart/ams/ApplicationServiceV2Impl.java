package com.flipkart.ams;

import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.winterfell.api.request.CreateApplicationRequest;
import com.flipkart.fintech.winterfell.api.request.ResumeApplicationRequest;
import com.flipkart.fintech.winterfell.api.response.ApplicationDataResponse;
import com.google.inject.Inject;
import lombok.CustomLog;

/**
 * Implementation of ApplicationServiceV2 that provides enhanced methods with consistent response
 * types across operations.
 */
@CustomLog
public class ApplicationServiceV2Impl implements ApplicationServiceV2 {

  private final WinterfellUtilsV2 winterfellUtilsV2;

  @Inject
  public ApplicationServiceV2Impl(WinterfellUtilsV2 winterfellUtilsV2) {
    this.winterfellUtilsV2 = winterfellUtilsV2;
  }

  @Override
  public ApplicationDataResponse resumeApplication(
      MerchantUser merchantUser,
      String applicationId,
      ResumeApplicationRequest resumeApplicationRequest)
      throws PinakaException {
    log.info("Resume application for applicationId: {}, user: {}", applicationId, resumeApplicationRequest.getSmUserId());
    try {
      return winterfellUtilsV2.resumeApplication(
          applicationId, merchantUser, resumeApplicationRequest);
    } catch (WinterfellServiceException e) {
      throw new PinakaException(e.getMessage(), e);
    }
  }

  @Override
  public ApplicationDataResponse createApplication(
      CreateApplicationRequest createApplicationRequest) throws PinakaException {
    log.info(
        "Creating application with enhanced response: {}, user: {}",
        createApplicationRequest.getApplicationType(), createApplicationRequest.getSmUserId());

    try {
      return winterfellUtilsV2.createApplication(createApplicationRequest);
    } catch (WinterfellServiceException e) {
      throw new PinakaException(e.getMessage(), e);
    }
  }
}
