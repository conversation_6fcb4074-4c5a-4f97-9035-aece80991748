server:
  type: default
  applicationContextPath: /pinaka
  adminContextPath: /admin
  applicationConnectors:
    - type: http
      port: 9090
  adminConnectors:
    - type: http
      port: 9091
  requestLog:
      # Note:
      # 1. We do not specify a logFormat because the default log format is Common Log Format (CLF), which is good
      # 2. If we specify a logFormat, note that the format specifiers for access logs are different
      # 3. Access log accepts ONLY ONE appender - we cannot get access logs on both console and file simultaneously
      appenders:
        - type: file
          currentLogFilename: /var/log/pinaka/access.log
          threshold: ALL
          archive: true
          archivedLogFilenamePattern: /var/log/pinaka/access-%d{yyyy-MM-dd-HH}.log.gz
          archivedFileCount: 24
          timeZone: IST

eventPublishEnvironment: "PREPROD"

rateLimitingConfig:
  [
      {"limiterKey" : "FETCH_UI",  "timeoutInMs":5},
      {"limiterKey" : "PAN_SUBMIT",  "timeoutInMs":5},
      {"limiterKey" : "AADHAR_VERIFICATION",  "timeoutInMs":5},
      {"limiterKey" : "GENERATE_OTP",  "timeoutInMs":5},
      {"limiterKey" : "CREATE_APPLICATION",  "timeoutInMs":5},
      { "limiterKey": "PENNY_DROP_SUBMIT_DETAILS",  "timeoutInMs":5},
      { "limiterKey": "FETCH_LOADER",  "timeoutInMs":5},
      { "limiterKey": "VERIFY_OTP", "timeoutInMs":5 },
      { "limiterKey": "GEO_LOCATION", "timeoutInMs":5 }

  ]

logging:
  level: INFO
  loggers:
      "org.hibernate": ERROR
      "com.flipkart.affordability.clients.oauth": ERROR
      "com.flipkart.restbus.client.shards": ERROR
      "com.flipkart.abservice.resources": ERROR
      "org.hibernate.SQL":
        level: INFO
        additive: false
        appenders:
          - type: file
            currentLogFilename: /var/log/pinaka/pinaka-sql.log
            archivedLogFilenamePattern: /var/log/pinaka/pinaka-sql-%i.log.gz
            archivedFileCount: 5
            maxFileSize: 20MB
      "com.flipkart.fintech.pinaka.service.viesti": ERROR

  appenders:
    - type: file
      threshold: ALL
      logFormat: "[%d{dd-MM-yyyy HH:mm:ss.SSS}] %highlight([%level]) [%thread] [%X{trace_id}] [%X{X-Request-ID}] [%cyan(%logger{36})]: %message %replace(%exception){'\n',' | '} %n %nopex"
      currentLogFilename: /var/log/pinaka/pinaka.log
      archivedLogFilenamePattern: /var/log/pinaka/pinaka-%d{yyyy-MM-dd-HH}.log.gz
      archivedFileCount: 24
      timeZone: IST
    - type: file
      threshold: ERROR
      logFormat: "[%d{dd-MM-yyyy HH:mm:ss.SSS}] %highlight([%level]) [%thread] [%X{trace_id}] [%X{X-Request-ID}] [%cyan(%logger{36})]: %message %replace(%exception){'\n',' | '} %n %nopex"
      currentLogFilename: /var/log/pinaka/pinaka-error.log
      archivedLogFilenamePattern: /var/log/pinaka/pinaka-error-%d{yyyy-MM-dd-HH}.log.gz
      archivedFileCount: 24
      timeZone: IST
    - type: console
      threshold: ALL
      logFormat: "[%d{dd-MM-yyyy HH:mm:ss.SSS}] %highlight([%level]) [%thread] [%X{trace_id}] [%X{X-Request-ID}] [%cyan(%logger{36})]: %message%n"
      timeZone: IST
      target: stdout

#healthCheckName: PinakaHealth

swagger:
  title: Pinaka Service
  description: APIs for Pinaka Service
  contact: <EMAIL>
  resourcePackage: com.flipkart.fintech.pinaka.service.web

#database config is read from the config bucket
databaseConfig:
  encryptedUrl: eyJkYXRhIjoidmF1bHQ6djE6clhsZG9meXZ2bGVHSGZEMXZrdFhoVFpucmo2SEVwOGg0UTAwb0NXNkZxa2lVeVNybTgwTG8ya0YveWFUcmhYM01pOXBTUU1yUk1rQ3lHYVZYcDdrRTFJU2hWR0UzR3hzVkJXZUFZUWhCcjdLUDJxSERXWDlDb0lDUGZUWmNLWjlkYnZzOHNLZmp4TGd5Tkl1SlZuQ0FxYWQzc21xalFtTmRsVThuYXNibFArTnMyYXhnd254VUs3NVpHWWg1V2J2Zkg1WmY3a2FXbjE5Q0w5dDRDNEZ6UT09Iiwia2V5SWQiOiJMZW5kaW5nX0xPUzo6ZGVmYXVsdCJ9
  encryptedUser: eyJkYXRhIjoidmF1bHQ6djE6NWNrQncranBWMFlKVXQ5MHp1eWtHVFZ5QnNMRVowb0YyT29zc1dHbFU4bDVvRXpDSzhlZDRGdGx4R0k9Iiwia2V5SWQiOiJMZW5kaW5nX0xPUzo6ZGVmYXVsdCJ9
  encryptedPassword: eyJkYXRhIjoidmF1bHQ6djE6SC9BOERCczZBaEkvMlluSm9jbU5peWwxNUNTcEdlZkcyditObUVTWVFhM1ZzOFMwT3BuS1FIamM4aFlkZlF5MCIsImtleUlkIjoiTGVuZGluZ19MT1M6OmRlZmF1bHQifQ==
  driverClass: com.mysql.cj.jdbc.Driver
  properties:
    charSet: UTF-8
    hibernate.dialect: org.hibernate.dialect.MySQL57Dialect
    hibernate.generate_statistics: false
    hibernate.session.events.log: false
    hibernate.show_sql: false
    hibernate.format_sql: false
    hibernate.id.new_generator_mappings: true
    # the maximum amount of time to wait on an empty pool before throwing an exception
    maxWaitForConnection: 10s
    # the SQL query to run when validating a connection's liveness
    validationQuery: "/* MyService Health Check */ SELECT 1"
    initialSize: 8
    # the minimum number of connections to keep open
    minSize: 8
    # the maximum number of connections to keep open
    maxSize: 10000
    # whether or not idle connections should be validated
    checkConnectionWhileIdle: true
    checkConnectionOnReturn: true
    checkConnectionOnBorrow: true
    # how long a connection must be held before it can be validated
    validationInterval: 5s
    # the maximum lifetime of an idle connection
    minIdleTime: 1 minute

profileServiceConfig:
  experianConfig:
    ttl : 360000
    showRefreshButton: 1
  experianMocked: true
  smUserServiceClientConfig:
    url: "http://usersvc-kaas-v1.sm-user-service-prod.fkcloud.in"
    clientId: "supermoney"
  publisherConfig:
    projectId: sm-personal-loans
    topicId: preapproved-blackbox-offers

profileClientConfiguration:
  url: http://localhost:9090
  client: profile-client

databaseSlaveConfig:
  slaveEncryptedUrl: eyJkYXRhIjoidmF1bHQ6djE6ZkNGTHByWVN1MTZhMTdybzVCZ2VicFBtTUswYmpUUFFoakE3cFNMb1NKemJIVGlVNUNXNVYyK0JLOFNIdzRzR2tMVUk0cldUNmJkR1oyMXlSZjFVbzdoczFCdXFoQk4ra2c1cWxGYWJ0Zmx0eTdiZXBvOFRwc2o2QjhSYW55dDdxcCt3amJPNXBZRjlzOHVoTU5OR1hMY1hNNFZRMVF3RXdvOW54RTNKQ0l2aGU1OW1uZlNITHBvVzN3YlFWNmxkUWRKeEpYQVRRbU1FSkY2aVJxeHpYY0psVHJhcCIsImtleUlkIjoiTGVuZGluZ19MT1M6OmRlZmF1bHQifQ==
  slaveEncryptedUser: eyJkYXRhIjoidmF1bHQ6djE6NWNrQncranBWMFlKVXQ5MHp1eWtHVFZ5QnNMRVowb0YyT29zc1dHbFU4bDVvRXpDSzhlZDRGdGx4R0k9Iiwia2V5SWQiOiJMZW5kaW5nX0xPUzo6ZGVmYXVsdCJ9
  slaveEncryptedPassword: eyJkYXRhIjoidmF1bHQ6djE6SC9BOERCczZBaEkvMlluSm9jbU5peWwxNUNTcEdlZkcyditObUVTWVFhM1ZzOFMwT3BuS1FIamM4aFlkZlF5MCIsImtleUlkIjoiTGVuZGluZ19MT1M6OmRlZmF1bHQifQ==
  driverClass: com.mysql.cj.jdbc.Driver
  properties:
    charSet: UTF-8
    hibernate.dialect: org.hibernate.dialect.MySQL57Dialect
    hibernate.generate_statistics: false
    hibernate.session.events.log: false
    hibernate.show_sql: false
    hibernate.format_sql: false
    hibernate.id.new_generator_mappings: true
    # the maximum amount of time to wait on an empty pool before throwing an exception
    maxWaitForConnection: 10s
    # the SQL query to run when validating a connection's liveness
    validationQuery: "/* MyService Health Check */ SELECT 1"
    initialSize: 2
    # the minimum number of connections to keep open
    minSize: 2
    # the maximum number of connections to keep open
    maxSize: 1000
    # whether or not idle connections should be validated
    checkConnectionWhileIdle: true
    checkConnectionOnReturn: true
    checkConnectionOnBorrow: true
    # how long a connection must be held before it can be validated
    validationInterval: 5s
    # the maximum lifetime of an idle connection
    minIdleTime: 1 minute

profileDatabaseConfig:
  encryptedUrl: ***************************************************************************************
  encryptedUser: sm_admin
  encryptedPassword: G3XWFs29c8
  driverClass: com.mysql.cj.jdbc.Driver
  properties:
    charSet: UTF-8
    hibernate.generate_statistics: false
    hibernate.session.events.log: false
    hibernate.show_sql: false
    hibernate.format_sql: false
    hibernate.id.new_generator_mappings: true
    # the maximum amount of time to wait on an empty pool before throwing an exception
    maxWaitForConnection: 10s
    # the SQL query to run when validating a connection's liveness
    validationQuery: "/* MyService Health Check */ SELECT 1"
    initialSize: 8
    # the minimum number of connections to keep open
    minSize: 8
    # the maximum number of connections to keep open
    maxSize: 10000
    # whether or not idle connections should be validated
    checkConnectionWhileIdle: true
    checkConnectionOnReturn: true
    checkConnectionOnBorrow: true
    # how long a connection must be held before it can be validated
    validationInterval: 5s
    # the maximum lifetime of an idle connection
    minIdleTime: 1 minute

cryptexConfiguration:
  cryptexBundleEnabled: true
  authNClientConfig:
    url: https://service.authn-prod.fkcloud.in
    clientId: pinaka-service
    clientSecret: VdsO8BjanfTADqp3RNaaDm0bkm14iyVGJ3lUuGdaSTS+7Ba0

  cryptexClientConfig:
    endpoint: https://service.cryptex-prod.fkcloud.in
    maxConnections: 5
    connectTimeOut: 1500
    readTimeOut: 1500

  dynamicBucketConfig:
    bucketName: sm-pinaka-preprod
    enableLocalDynamicBucket: false

pinakaClientConfig:
  url: http://pinaka-service-next.sm-pinaka-prod.fkcloud.in
  client: pinaka

ardourClientConfig:
  url: http://10.83.37.172:80
  client: pinaka

userServiceClientConfig:
  usUrl: http://10.83.37.92:80
  usClientId: affordability

oAuthServiceClientConfig:
  oAuthUrl: http://************:80
  oAuthClientID : c2c95e0075c04b2e9b83e1bc8a09f57e
  oAuthClientSecret : FVMWGlJ22AoWfQ+ithjYn+M0tjr8xgakuHl06J5A+DELtVtc
  cachedAccessTokenTTL : 86400

loginServiceClientConfig:
  loginServiceUrl: http://***********:80
  loginServiceClientId: affordability

fluxAsyncClientConfig:
  url: dummy
  clientId: dummy
  exchangeName: dummy

connektClientConfig:
  exchangeName: fintech_los_connekt
  callbackUrl: http://************/pinaka/communications
  domain: flipkart
  emailUrl: http://************
  pnUrl: http://************
  smsUrl: http://************
  inAppUrl: http://************
  emailApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
  pnApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
  smsApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
  inAppApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
  emailAppName: flipkart
  pnAppName: RetailApp
  smsAppName: flipkart
  inAppName: flipkart
  emailBucket: alerts
  emailSubBucket: pay
  inAppBucket: alerts
  inAppSubBucket: pay
  pnBucket: alerts
  pnSubBucket: pay
  smsBucket: alerts
  smsSubBucket: pay
  pnChannelId: fk_channel_order_payments
  transactionalEmail: true
  transactionalPN: true
  transactionalSMS: true
  transactionalInApp: true

robinhoodAsyncClientConfig:
  url: http://************
  clientId: pinaka
  exchangeName: "%s_onboarding_2"

pandoraClientConfig:
  url: http://pandora-service-next.sm-pandora-prod.fkcloud.in/pandora
  client: pinaka

varadhiClientConfig:
  url: http://************/
  topicName: sm_pl_application_state

pandoraLiteClientConfig:
  url: http://**********/pandoralite
  client: pinaka

fluxConfiguration:
  fluxRuntimeUrl: dummy
  connectionTimeout: 10000
  socketTimeout: 10000

onboardingClientConfig:
  url: http://***********:8080
  client: pinaka

gibraltarConfig:
  maxPublicKeyCount: 100
  keySpaceName: onboarding
  clientName: efa-onboarding-client
  trustStorePath: "/etc/gibraltar/prod-client-truststore.jks"
  trustStorePass: password
  keyStorePath: "/etc/gibraltar/prod-client-keystore.jks"
  keyStorePass: password
  certificateAlias: gibraltarSelfSigned
  url: https://10.32.110.181:8443,https://10.34.29.80:8443
  generateKeysOnStartUp: true
  connectionTimeoutInMs: 150
  socketTimeoutInMs: 300
  httpRequestRetryCount: 5

creditModelConfig:
  url: http://10.47.5.159:80
  enabled: true
  testUsers: ["ACQ23LU0JFDB2RR6K07NG1IJ7ZL5NQYI","AC5PD5WPWNURTFQ1D8NA9MP85WUXB2UI","AC3AS99QZ0B0WTYTVXWANJ7SOSHYURH5","ACCE36E0BCF906848FC8DA046725F3954F7X","ACC13503736928883162","AC8V9HRPRXZK4P1DHU230F7EWRY3C930","ACC3866C2ECD3AF419CA924DB63E2E253B68","ACC14118057732574013","ACC14118057734112458","ACC14118057737672892","ACC14118057740584334","ACC14118057745198279","ACC14118057748088971","ACC14118057749539093","ACC1411805775098578","ACC14118057752414711","ACC1411805775385602","ACC14118057755278085","ACC14118057756619072","ACC14118057758129027","ACC14118057759482854","ACC14118057760838683","ACC14118057762178372","ACC14118057763505419","ACC1411805776503286","ACC1411805776648290","ACC14164940558211150","ACC798D3CC5AC4447869C1B1DD645A7128AZ","ACC14215635411148470","ACCA17D4EA4D70F43388C6EC80E73A69180R","ACC05D577873443449FA9E165A662D03B961"]

bnplOnboardingConfig:
  optionalPermissions:
    - CONTACT
    - LOCATION
  mandatoryPermissions:
    - SMS
    - DEVICE
  tncUrl: https://www.flipkart.com/pages/pay-later-tnc
  privaryPolicyUrl: https://www.flipkart.com/pages/fapl-privacy-policy

efaOnboardingConfig:
  ceEnabled: true
  ceTestUsers: ["ACQ23LU0JFDB2RR6K07NG1IJ7ZL5NQYI","AC5PD5WPWNURTFQ1D8NA9MP85WUXB2UI","AC3AS99QZ0B0WTYTVXWANJ7SOSHYURH5","ACCE36E0BCF906848FC8DA046725F3954F7X","ACC798D3CC5AC4447869C1B1DD645A7128AZ","ACC14215635411148470","ACCA17D4EA4D70F43388C6EC80E73A69180R","ACC05D577873443449FA9E165A662D03B961"]
  testAccountPhones: [{"<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********","<EMAIL>":"**********"}]
  optionalPermissions:
    - CONTACT
    - LOCATION
    - DEVICE
  mandatoryPermissions:
    - SMS
  tncUrl: https://www.flipkart.com/pages/efa-preapproval-tnc
  privaryPolicyUrl: https://www.flipkart.com/m/st/policies
  formFields:
      CITI:
      - fieldType: TEXT
        label: Company Name
        name: Company
      - fieldType: NUMBER
        label: Monthly Income
        name: Income
      - fieldType: DROPDOWN
        label: Employment
        name: Employment
      - fieldType: DROPDOWN
        label: Occupation
        name: Occupation
      - fieldType: DROPDOWN
        label: Qualification
        name: Qualification
      - fieldType: ADDRESS
        label: Current Address
        name: Address
      KISSHT: []
  lenderWiseFormFields:
      KISSHT:
      - PAN_NUMBER
      - DOB
      - GENDER
      - ADDRESS
      INDIA_BULLS:
      - PAN_NUMBER
      - DOB
      - GENDER
      - EMPLOYMENT_STATUS
      - MONTHLY_SALARY
      - ADDRESS
  underwritingCallouts:
      KISSHT: [{"callout":"Repay by 15th of next month at 0% interest","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q={@quality}"}},{"callout":"Repay in 6-12 EMI's at 25% per annum","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/dfcd50c7-4c29-404e-91da-dab3c7861872.png?q={@quality}"}},{"callout":"No preclosure charges","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/93ab33b6-f57b-433a-a336-745b63c4d7a3.png?q={@quality}"}},{"callout":"Late fee - 3% of the unpaid bill* (Min ?200)","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/8e307790-3518-446f-b545-9e22243bcfeb.png?q={@quality}"}}]
      INDIA_BULLS: [{"callout":"Repay by 15th of next month at 0% interest","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q={@quality}"}},{"callout":"Repay in 3-12 EMIs starting at 20% per annum","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/dfcd50c7-4c29-404e-91da-dab3c7861872.png?q={@quality}"}},{"callout":"No preclosure charges","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/93ab33b6-f57b-433a-a336-745b63c4d7a3.png?q={@quality}"}},{"callout":"Late fee - 3% of the unpaid bill* (Min ?200)","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/8e307790-3518-446f-b545-9e22243bcfeb.png?q={@quality}"}}]
  approvalCallouts:
      KISSHT: [{"callout":"Start shopping, select cardless credit as your payment option.","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/d388836b-a541-4f36-95df-49818d720bb7.png?q={@quality}"}},{"callout":"Repay by 15th of the month using debit card/netbanking.","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/d4534a52-a197-4463-9530-90ac5a25b4aa.png?q={@quality}"}}]
      INDIA_BULLS: [{"callout":"Start shopping, select cardless credit as your payment option.","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/d388836b-a541-4f36-95df-49818d720bb7.png?q={@quality}"}},{"callout":"Repay by 15th of the month using debit card/netbanking.","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/d4534a52-a197-4463-9530-90ac5a25b4aa.png?q={@quality}"}}]


cryptoConfig:
  algo: AES
  secretKey: "Cd1/SXipT)So3=19"
  cbcTelesaleSecretKey: "cbc/Tele$@les(=3"

underwritingConfig:
  endPoint: "http://**********/fintech-underwriting"

citiConfig:
  enabled: false
  productConfig:
      productCode: PC400
      sourceCode: AAFKMNET
      organization: 730
      logo: 400
      embossName: "Matthew Hyden"
  supportedPincodes: ["148023","110001","110002","110003","110004","110005","110006","110007","110008","110009","110011","110012","110013","110014","110015","110016","110017","110018","110020","110021","110022","110023","110010","110019","110024","110026","110027","110028","110029","110030","110031","110032","110033","110034","110035","110036","110037","110038","110039","110040","110041","110042","110043","110045","110046","110047","110049","110050","110051","110048","110025","110044","110052","110053","110054","110055","110056","110057","110058","110059","110060","110061","110062","110063","110065","110066","110069","110070","110071","110072","110073","110074","110075","110076","110077","110078","110079","110064","110067","110068","110080","110081","110082","110083","110084","110085","110086","110087","110088","110089","110090","110091","110093","110094","110095","110096","110097","110098","110099","110092","160105","160106","160107","160134","509823","382001","382002","382003","382004","382005","382037","120002","605009","121001","121002","121003","121004","121005","121006","121007","121008","121009","121011","121014","121010","122003","122007","122008","122009","122010","122011","122004","122001","122002","122012","122018","122017","122015","122016","122022","122103","123504","134107","134109","134113","134111","134105","134108","134115","134114","134102","134112","134118","134116","141001","141002","141003","141004","141005","141006","141007","141008","140301","140603","140103","140604","141009","141010","144001","144002","144003","144004","144005","144006","144007","144008","148023","160001","160002","160003","160004","160005","160006","160007","160008","160009","160010","160011","160012","160013","160014","160015","160016","160017","160018","160019","160020","160021","160022","160023","160024","160025","160026","160027","160028","160029","160030","160031","160032","160033","160034","160035","160036","160037","160038","160039","160040","160041","160042","160043","160044","160045","160046","160047","160051","160052","160053","160054","160055","160056","160057","160058","160059","160063","160064","160065","160067","160068","160069","160070","160062","160061","160060","160050","160048","160049","160071","160066","160101","160104","160109","173205","201001","201003","201004","201005","201006","201007","201008","201009","201010","201013","201014","201016","201002","201012","201011","201203","201303","201304","201309","201301","201308","201305","201310","201307","201302","201306","226001","226002","226004","226005","226006","226007","226008","226009","226010","226011","226012","226013","226014","226015","226016","226017","226018","226019","226020","226021","226022","226023","226024","302001","302002","302003","302004","302005","302006","302007","302008","302009","302010","302011","302012","302013","302014","302015","302016","302017","302018","302019","302020","302021","302022","302023","302024","302025","302026","302027","302028","302029","302030","302031","302032","302033","303007","303101","303902","303905","380003","380004","380005","380007","380008","380013","380014","380015","380016","380023","380024","380025","380009","380006","380019","380001","380050","380051","380052","380053","380054","380027","380058","380059","380060","380063","380061","382021","382025","382029","382028","382027","382026","382030","382022","382023","382024","382009","382017","382018","382019","382020","382016","382014","382015","382006","382007","382008","382010","382011","382012","382013","382045","382170","382031","382041","382042","382043","382044","382038","382039","382040","382035","382036","382032","382033","382034","382210","382340","382350","382355","382405","382410","382345","382330","382440","382415","382424","382470","382481","382729","382445","382480","388001","388110","388120","388121","389330","390001","390002","390003","390004","390005","390006","390008","390010","390011","390012","390013","390014","390015","390016","390017","390018","390019","390007","390025","390022","390023","390024","390020","390021","389350","390009","391310","391101","391345","391346","391347","391410","391440","391770","391350","391740","394221","394230","394270","394510","394515","394516","394550","396191","396193","396195","400002","400004","400005","400006","400007","400008","400009","400010","400011","400012","400013","400014","400015","400016","400017","400018","400019","400020","400021","400022","400023","400024","400025","400001","396192","396194","400026","400027","400028","400029","400030","400031","400032","400033","400034","400035","400036","400037","400038","400039","400040","400041","400042","400043","400044","400045","400046","400047","400048","400049","400050","400051","400052","400053","400054","400055","400056","400057","400058","400060","400061","400062","400064","400065","400066","400067","400068","400069","400070","400071","400072","400073","400074","400075","400076","400077","400078","400079","400080","400081","400063","400059","400082","400083","400084","400085","400086","400087","400088","400089","400090","400091","400092","400094","400095","400096","400097","400098","400099","400101","400102","400103","400104","400093","400601","400602","400603","400604","400605","400607","400608","400610","400611","400613","400606","400614","400615","400701","400702","400703","400704","400705","400706","400707","400708","400709","400710","401101","401105","401107","401104","401207","401208","401209","401210","401303","401202","410204","410206","410207","410209","410216","410218","410301","410210","410501","410419","411002","411003","411004","411005","411006","411007","411008","411009","411010","411011","411012","411015","411016","411017","411018","411019","411020","411021","411022","411026","411027","411028","411014","411001","411013","411029","411030","411031","411032","411033","411034","411035","411036","411037","411038","411039","411040","411041","411042","411043","411044","411046","411047","411048","411049","411050","411051","411052","411053","411054","411055","411056","411045","411058","411059","411060","411061","411062","411063","411064","411065","411066","411067","411068","411069","411070","411071","411072","411073","411074","411075","411076","411077","411078","411079","411080","411081","411082","411083","411084","411057","411085","411086","411087","411088","411089","411090","411091","411092","411093","411094","411095","411096","411097","411098","411099","411100","411101","411102","411103","411104","411105","411106","411107","411108","411109","411110","411111","411112","411113","411114","411115","411116","411117","411118","411119","411120","411121","411122","411123","411124","411125","411126","411127","411128","411129","411130","411131","411132","411133","411134","411135","411136","411137","411138","411139","411140","411141","411142","411143","411144","411145","411146","411147","411148","411149","411150","411151","411152","411153","411154","411155","411156","411157","411158","411159","411160","411161","411162","411163","411164","411165","411166","411167","411168","411169","411170","411171","411172","411173","411174","411175","411176","411177","411178","411179","411180","411181","411182","411183","411184","411185","411186","411187","411188","411189","411190","411191","411192","411193","411194","411195","411196","411197","411198","411199","411200","411201","411202","411203","411204","411205","411206","411207","411208","411209","411210","411211","411212","411213","411214","411215","411216","411217","411218","411219","411220","411221","411222","411223","411224","411225","411226","411227","411228","411229","411230","411231","411232","411233","411234","411235","411236","411237","411238","411239","411240","411241","411242","411243","411244","411245","411246","411247","411248","411249","411250","411251","411252","411253","411254","411255","411256","411257","411258","411259","411260","411261","411262","411263","411264","411265","411266","411267","411268","411269","411270","411271","411272","411273","411274","411275","411276","411277","411278","411279","411280","411281","411282","411283","411284","411285","411286","411287","411288","411289","411290","411291","411292","411293","411294","411295","411296","411297","411298","411299","411300","411301","411302","411303","411304","411305","411306","411307","411308","411309","411310","411311","411312","411313","411314","411315","411316","411317","411318","411319","411320","411321","411322","411323","411324","411325","411326","411327","411328","411329","411330","411331","411332","411333","411334","411335","411336","411337","411338","411339","411340","411341","411342","411343","411344","411345","411346","411347","411348","411349","411350","411351","411352","411353","411354","411355","411356","411357","411358","411359","411360","411361","411362","411363","411364","411365","411366","411367","411368","411369","411370","411371","411372","411373","411374","411375","411376","411377","411378","411379","411380","411381","411382","411383","411384","411385","411386","411387","411388","411389","411390","411391","411392","411393","411394","411395","411396","411397","411398","411399","411400","411401","411402","411403","411404","411405","411406","411407","411408","411409","411410","411411","411412","411413","411414","411415","411416","411417","411418","411419","411420","411421","411422","411423","411424","411425","411426","411427","411428","411429","411430","411431","411432","411433","411434","411435","411436","411437","411438","411439","411440","411441","411442","411443","411444","411445","411446","411447","411448","411449","411450","411451","411452","411453","411454","411455","411456","411457","411458","411459","411460","411461","411462","411463","411464","411465","411466","411467","411468","411469","411470","411471","411472","411473","411474","411475","411476","411477","411478","411479","411480","411481","411482","411483","411484","411485","411486","411487","411488","411489","411490","411491","411492","411493","411494","411495","411496","411497","411498","411499","411500","411501","411502","411503","411504","411505","411506","411507","411508","411509","411510","411511","411512","411513","411514","411515","411516","411517","411518","411519","411520","411521","411522","411523","411524","411525","411526","411527","411528","411529","411530","411531","411532","411533","411534","411535","411536","411537","411538","411539","411540","411541","411542","411543","411544","411545","411546","411547","411548","411549","411550","411551","411552","411553","411554","411555","411556","411557","411558","411559","411560","411561","411562","411563","411564","411565","411566","411567","411568","411569","411570","411571","411572","411573","411574","411575","411576","411577","411578","411579","411580","411581","411582","411583","411584","411585","411586","411587","411588","411589","411590","411591","411592","411593","411594","411595","411596","411597","411598","411599","411600","411601","411602","411603","411604","411605","411606","411607","411608","411609","411610","411611","411612","411613","411614","411615","411616","411617","411618","411619","411620","411621","411622","411623","411624","411625","411626","411627","411628","411629","411630","411631","411632","411633","411634","411635","411636","411637","411638","411639","411640","411641","411642","411643","411644","411645","411646","411647","411648","411649","411650","411651","411652","411653","411654","411655","411656","411657","411658","411659","411660","411661","411662","411663","411664","411665","411666","411667","411668","411669","411670","411671","411672","411673","411674","411675","411676","411677","411678","411679","411680","411681","411682","411683","411684","411685","411686","411687","411688","411689","411690","411691","411692","411693","411694","411695","411696","411697","411698","411699","411700","411701","411702","411703","411704","411705","411706","411707","411708","411709","411710","411711","411712","411713","411714","411715","411716","411717","411718","411719","411720","411721","411722","411723","411724","411725","411726","411727","411728","411729","411730","411731","411732","411733","411734","411735","411736","411737","411738","411739","411740","411741","411742","411743","411744","411745","411746","411747","411748","411749","411750","411751","411752","411753","411754","411755","411756","411757","411758","411759","411760","411761","411762","411763","411764","411765","411766","411767","411768","411769","411770","411771","411772","411773","411774","411775","411776","411777","411778","411779","411780","411781","411782","411783","411784","411785","411786","411787","411788","411789","411790","411791","411792","411793","411794","411795","411796","411797","411798","411799","411800","411801","411802","411803","411804","411805","411806","411807","411808","411809","411810","411811","411812","411813","411814","411815","411816","411817","411818","411819","411820","411821","411822","411823","411824","411825","411826","411827","411828","411829","411830","411831","411832","411833","411834","411835","411836","411837","411838","411839","411840","411841","411842","411843","411844","411845","411846","411847","411848","411849","411850","411851","411852","411853","411854","411855","411856","411857","411858","411859","411860","411861","411862","411863","411864","411865","411866","411867","411868","411869","411870","411871","411872","411873","411874","411875","411876","411877","411878","411879","411880","411881","411882","411883","411884","411885","411886","411887","411888","411889","411890","411891","411892","411893","411894","411895","411896","411897","411898","411899","411900","411901","411902","411903","411904","411905","411906","411907","411908","411909","411910","411911","411912","411913","411914","411915","411916","411917","411918","411919","411920","411921","411922","411923","411924","411925","411926","411927","411928","411929","411930","411931","411932","411933","411934","411935","411936","411937","411938","411939","411940","411941","411942","411943","411944","411945","411946","411947","411948","411949","411950","411951","411952","411953","411954","411955","411956","411957","411958","411959","411960","411961","411962","411963","411964","411965","411966","411967","411968","411969","411970","411971","411972","411973","411974","411975","411976","411977","411978","411979","411980","411981","411982","411983","411984","411985","411986","411987","411988","411989","411990","411991","411992","411993","411994","411995","411996","411997","411998","411999","412105","412108","412114","412201","412302","412220","412207","412308","421201","421202","421203","421301","421306","422001","422002","422004","422005","422006","422007","422009","422010","422011","422012","422013","422101","422102","422105","422206","422207","422402","431001","431002","431003","431004","431005","431105","431110","431131","431133","431136","431201","431210","431601","431602","431603","431604","431605","444001","444002","444003","444005","444004","452001","445203","452002","452003","452004","452006","452007","452008","452009","452010","462002","462003","462004","462007","462011","462012","462013","462016","462021","462022","462023","462024","462026","462027","462030","462031","462032","462036","462039","462041","462046","463107","463108","463109","463110","463111","463120","500001","500002","500003","500004","500005","500006","500007","500008","500009","500010","500012","500013","500014","500015","500016","500017","500018","500019","500020","500021","500022","500023","500024","500025","500026","500027","500028","500029","500030","500031","500033","500036","500037","500038","500034","500035","500032","500039","500040","500041","500042","500043","500044","500045","500046","500047","500048","500049","500050","500051","500052","500053","500054","500055","500056","500057","500058","500061","500062","500063","500064","500065","500066","500059","500060","500011","500067","500068","500069","500070","500071","500072","500073","500074","500075","500076","500077","500078","500079","500080","500083","500084","500085","500086","500087","500088","500089","500090","500091","500092","500093","500094","500081","500082","500095","500096","500097","500098","500409","501218","501401","501510","501511","502001","502032","501301","502123","502205","502220","502278","502307","502319","502291","502309","502324","502325","502329","518001","518002","518003","518005","518301","518360","518501","518502","518503","535128","560002","560003","560004","560005","560006","560007","560008","560009","560010","560011","560012","560013","560014","560015","560016","560018","560019","560020","560022","560001","560017","560021","560023","560024","560025","560026","560027","560028","560029","560030","560031","560032","560034","560035","560036","560038","560039","560040","560041","560042","560044","560046","560048","560049","560050","560033","560043","560037","560047","560045","560051","560052","560053","560054","560055","560056","560058","560059","560060","560061","560062","560063","560064","560065","560067","560068","560069","560070","560071","560072","560073","560074","560075","560076","560077","560078","560057","560066","560079","560080","560081","560082","560083","560084","560085","560086","560087","560088","560089","560090","560091","560092","560094","560096","560098","560099","560102","560103","560104","560093","560095","560106","560097","560100","560107","560108","560300","561229","562106","562114","562109","562158","562125","600001","600002","600003","600004","600005","600006","600007","600008","600009","600010","600011","600012","600013","600014","600015","600016","600017","600018","600019","600020","600021","600022","600023","600024","600025","600026","600027","600028","600029","600030","600031","600033","600034","600035","600036","600038","600039","600040","600041","600042","600043","600044","600037","600032","600045","600046","600047","600048","600049","600050","600051","600052","600053","600055","600056","600058","600059","600060","600061","600062","600063","600064","600065","600066","600067","600068","600069","600070","600071","600072","600057","600054","600074","600075","600076","600077","600078","600079","600080","600081","600082","600083","600084","600085","600086","600087","600088","600090","600091","600092","600093","600094","600095","600097","600098","600099","600100","600073","600089","600096","600101","600102","600103","600104","600105","600106","600107","600108","600109","600110","600111","600112","600114","600115","600116","600117","600118","600122","600126","600125","600113","600119","603002","602105","603102","603203","603202","603204","603110","603210","603103","603209","605001","605002","605003","605004","605005","605006","605007","605008","605010","605011","605012","605013","605014","605015","605016","605017","605018","605019","605020","605021","605022","605023","605024","605025","605026","605027","605028","605029","605030","605031","605032","605033","605034","605035","605036","605037","605038","605039","605040","605041","605042","605043","605044","605045","605046","605047","605048","605049","605050","605051","605052","605053","605054","605055","605056","605057","605058","605059","605060","605061","605062","605063","605064","605065","605066","605067","605068","605069","605070","605071","605072","605073","605074","605075","605076","605077","605078","605079","605080","605081","605082","605083","605084","605085","605086","605087","605088","605089","605090","605091","605092","605093","605094","605095","605096","605097","605098","605099","605100","605101","605102","605103","605104","605105","605106","605107","605108","605109","605110","605111","607402","641001","641002","641003","641004","641005","641006","641007","641008","641009","641010","641011","641012","641013","641014","641015","641016","641017","641018","641019","641020","641021","641022","641023","641024","641025","641026","641027","641028","641029","641030","641031","641032","641033","641034","641035","641036","641037","641038","641039","641040","641041","641042","641043","641044","641045","641046","641047","641062","641104","641105","641103","641049","641107","641048","641114","641301","641401","641402","641406","641111","641659","682001","682002","682003","682005","682006","682008","682009","682010","682011","682012","682013","682014","682015","682016","682017","682018","682019","682020","682021","682022","682023","682024","682025","682026","682027","682028","682029","682030","682031","682032","682033","682034","682035","682036","682037","682301","682302","682303","682304","682305","682306","682307","682308","682309","682310","682311","682312","682314","682317","682506","683101","683102","683103","683104","683105","683107","683108","683109","683501","683502","683503","683513","683518","683519","683525","683562","700001","700002","700003","700004","700005","700006","700008","700009","700010","700011","700012","700014","700007","700013","700015","700016","700017","700018","700019","700020","700021","700022","700023","700024","700025","700026","700027","700028","700029","700030","700031","700032","700033","700034","700035","700036","700038","700040","700041","700042","700037","700039","700043","700044","700045","700046","700047","700048","700049","700050","700051","700052","700053","700054","700055","700056","700057","700058","700059","700060","700061","700062","700063","700065","700066","700067","700068","700069","700070","700064","700071","700072","700073","700074","700075","700076","700077","700079","700080","700081","700082","700083","700084","700085","700086","700087","700089","700090","700092","700093","700094","700095","700096","700097","700098","700091","700088","700078","700099","700101","700102","700106","700108","700109","700110","700111","700115","700116","700117","700118","700120","700121","700122","700124","700100","700104","700103","700107","700105","700125","700113","700126","700114","700129","700130","700131","700127","700143","700145","700148","700150","700152","700128","700135","700136","700149","700132","700133","700142","700147","711101","700157","700156","700159","700160","711102","711103","711104","711106","711109","711111","711201","711202","711204","711205","711224","711227","711105","711107","711203","711302","711108","712101","712102","712103","712104","712105","712123","712124","712132","712137","712201","712202","712203","712204","712205","712222","712223","712232","712233","712136","712235","712248","712249","712258","712301","712310","712503","712504","712513","721601","721602","721603","721604","721605","721606","721607","721635","721657","743101","743102","743108","743121","743122","743127","743128","743134","743144","743145","743155","743165","743166","743175","743176","743177","743178","743179","743185","743186","743201","743203","743204","743205","743250","743275","743276","743296","743298","743302","743313","743318","743319","743330","743352","743358","743359","743361","743398","743413","743423","743424","743508","743510","743512","743515","743518","743612","743700","751001","751002","751003","751004","751005","751007","751008","751009","751011","751012","751013","751014","751015","751017","751019","751020","751021","751022","751023","751024","751025","751026","751027","751028","751029","751032","751033","751034","751035","751036","751037","751038","751039","751040","751041","751042","751043","751044","751045","751046","751047","751048","751049","751051","751052","751053","751056","751057","751058","751059","751060","751061","751062","751063","751064","751065","751066","751067","751068","751069","751070","751071","751072","751073","751074","751075","751076","751077","751078","751079","751080","751081","751082","751083","751084","751085","751086","751087","751088","751089","751090","751091","751092","751093","751094","751095","751096","751097","751098","751099","751100","638157","821108","382443","641407","590012","382427","382430","382475","391330","501505","560109","562130","700112","641109","700154","641201","641654","515214","682000"]

lenderConfiguration:
  configurations:
    EFA:
    -
      consentTextKey: efa-citi-consent-text
      hasAdditionalForm: true
      interestRate: 12
      emiSupported: true
      paylaterSupported: false
      lateFees: 15
      lender: CITI
      postApprovalTncKey: efa-citi-postapproval-tnc
      preApprovalTncKey: efa-citi-preapproval-tnc
      showKycDetails: false
      primaryDataEditable: false
    -
      consentTextKey: efa-consent-text
      hasAdditionalForm: false
      interestRate: 20
      emiSupported: true
      paylaterSupported: true
      lateFees: 20
      lender: KISSHT
      postApprovalTncKey: efa-postapproval-tnc
      preApprovalTncKey: efa-preapproval-tnc
      showKycDetails: true
      primaryDataEditable: true
    -
      consentTextKey: efa-consent-text
      hasAdditionalForm: false
      interestRate: 20
      emiSupported: true
      paylaterSupported: true
      lateFees: 20
      lender: INDIA_BULLS
      postApprovalTncKey: efa-postapproval-tnc
      preApprovalTncKey: efa-preapproval-tnc
      showKycDetails: true
      primaryDataEditable: true

    BNPL:
    -
      consentTextKey: dummy
      hasAdditionalForm: false
      interestRate: 0
      emiSupported: false
      paylaterSupported: true
      lateFees: 0
      lender: KISSHT
      postApprovalTncKey: dummy
      preApprovalTncKey: dummy
      showKycDetails: false
      primaryDataEditable: false
      minimumAgeRequired: 0
    -
      hasAdditionalForm: false
      lender: IDFC
      primaryDataEditable: true
      nameMatchThreshold: 65
      nameValidationEnabled: true
      panValidationRequired: false
      postApprovalTncKey: dummy
      preApprovalTncKey: dummy
      consentTextKey: dummy

    FLIPKART_ADVANZ:
    -
      hasAdditionalForm: false
      lender: IDFC
      primaryDataEditable: true
      nameMatchThreshold: 70
      nameValidationEnabled: true
      panValidationRequired: false
      postApprovalTncKey: dummy
      preApprovalTncKey: dummy
      consentTextKey: dummy
      minimumAgeRequired: 10

    CBC:
    -
      lender: AXIS

upgradationConfig:
  upgradationEnabled: true

tijoriConfig:
  url: http://************:80
  clientName: robinhood

tijoriAsyncClientConfig:
  merchant: mp_flipkart
  exchangeName: lms_onboarding_queue
  tijoriUrl: http://************:80

external_client_config:
  page_service_config:
    host: ***********
    port: 80

cbcOnboardingConfig:
  fkpayTriggerOtpUrl: https://pay.payzippy.com/fkpay/api/v3/fintech/otp/send
  fkpayDisplayCardEtbUrl: https://pay.payzippy.com/fkpay/api/v1/fintech/paymentInstrument/CBC_AXIS_APP_SERNO
  fkpayDisplayCardEtccUrl: https://pay.payzippy.com/fkpay/api/v1/fintech/paymentInstrument/CBC_AXIS_CARD_SERNO
  fkpayDisplayCardConsoleUrl: https://pay.payzippy.com/fkpay/api/v3/fintech/paymentInstrument/CBC_AXIS_CARD_CONSOLE
  etbApprovalCallouts: [{"callout":"Access your card details","description":"Account Summary - Details of dues, transactions","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q={@quality}"}},{"callout":"Reward Points Summary","description":"Manage/Redeem reward points or Set Goals","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q={@quality}"}},{"callout":"Create Requests","description":"Subscribe for E-statement or duplicate statement, Block Lost/Stolen card, Active international usage...","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q={@quality}"}}]
  ntbApprovalCallouts: [{"callout":"You will need to provide an identity proof from the list of accepted documents","icon":{"url":"https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/29/01/2019/1943fa0b-a414-4ccf-95ec-2dcfd8026613.png?q={@quality}"}}]
  cbcComingSoonBannerUrl: https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/12/09/2019/9afe3704-98b0-4faf-9f0b-0391069cfde3.jpg?q={@quality}
  cbcApplyNowBannerUrl: https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/12/09/2019/052efd3d-a219-4350-b1f5-0ce8911e5a1b.jpg?q={@quality}
  cbcContinueApplicationBannerUrl: https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/12/09/2019/71652a80-3aad-4104-b156-e3f5fd05e51f.jpg?q={@quality}
  cbcViewCardDetailsBannerUrl: https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/10/07/2019/8f863e90-5ebe-49f9-a23f-d2e0fb438f18.jpg?q={@quality}
  cbcApplicationEtccBuzzBannerUrl: https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/16/09/2019/70b693fa-4fc8-416a-bed7-fb4f7e1c8c77.png?q={@quality}
  cbcApplicationEtccNonBuzzBannerUrl: https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/26/06/2019/8f52e838-3942-458c-ba93-c274c5de9df0.png?q={@quality}
  cbcApplicationEtccBuzzBannerAspectRatio: 312:44
  cbcApplicationEtccNonBuzzBannerAspectRatio: 312:80
  externalMerchnats: ["phonepe","offers","myntra","offers_FYFemployee","myntra_employee","mastercard","wakefit","referral","PVR_OTA","Cred_OTA","Jeeves_OTA","Ekart_OTA","PhonePe_OTA","MyGate_OTA","BookMyShow_OTA","Nobroker_OTA","Truecaller_OTA","Shopsy_OTA"]
  plusWhitelistIdMap:
    phonepe : 120
    offers : 142
    offers_FYFemployee : 142
    myntra: 145
    myntra_employee : 142
    mastercard : 142
    wakefit : 142
    referral: 142
    PVR_OTA: 175
    Cred_OTA: 175
    Jeeves_OTA: 175
    Ekart_OTA: 175
    PhonePe_OTA: 175
    MyGate_OTA : 175
    BookMyShow_OTA : 175
    Nobroker_OTA : 175
    Truecaller_OTA : 175
    Shopsy_OTA : 175
  whitelistIdMap:
    phonepe : 119
    offers : 142
    offers_FYFemployee : 142
    myntra: 144
    myntra_employee : 142
    mastercard : 142
    wakefit : 142
    referral: 142
    PVR_OTA: 175
    Cred_OTA: 175
    Jeeves_OTA: 175
    Ekart_OTA: 175
    PhonePe_OTA: 175
    MyGate_OTA: 175
    BookMyShow_OTA: 175
    Nobroker_OTA: 175
    Truecaller_OTA: 175
    Shopsy_OTA: 175

  ntbFormSubTypeWiseFormList:
      PERSONAL_DETAILS:
        - personal_details
      CONTACT_DETAILS:
        - contact_details
        - address_form
      PROFESSIONAL_DETAILS:
        - professional_details
      CREDIT_CARD_FEATURES:
        - credit_card_features
  etsFormSubTypeWiseFormList:
      PERSONAL_DETAILS:
        - personal_details
      CONTACT_DETAILS:
        - contact_details
        - address_form
      PROFESSIONAL_DETAILS:
        - professional_details
      CREDIT_CARD_FEATURES:
        - credit_card_features
  accountsEnabledForCbcSplit: []
  accountsEnabledForConsoleCug: []
  accountsEnabledForCbcNtbSplitForm: ["ACC1A75EB1E55584AE89225A4B9CC7232FCT","ACC5131090496944D149296C47B98F07A99A"]
  channelsEnabledForCbcNtbSplitForm: ["ANDROID","iOS"]
  whitelistIdsEnabledForCbcNtbSplitForm: ["107","108"]
  cbcNtbSplitEnabled: true
  cohortWiseFormList:
      ETB_PRE_APPROVED:
      - personal_details
      - address_form
      - credit_card_features
      ETB_NON_PRE_APPROVED:
      - personal_details
      - address_form
      - professional_details
      - credit_card_features
      ETB_PRE_APPROVED_AUTODEBIT:
      - personal_details
      - autodebit_details
      - address_form
      - credit_card_features
      ETB_NON_PRE_APPROVED_AUTODEBIT:
      - personal_details
      - address_form
      - professional_details
      - autodebit_details
      - credit_card_features
      ETCC:
      - personal_details
      - address_form
      NTB:
      - personal_details
      - contact_details
      - professional_details
      - address_form
      - credit_card_features
  axisThinCremoScoreThreshold: 0
  autoRejectThinEnabled: N
  encryptionKey: "53EACE72CD83D6B60754C2F3959168EA"
  dobSubtextEnabled: Y
  kycSchedulerTtl: 2880
  accountsEnabledForCheckStatusV3: ["ACC1A75EB1E55584AE89225A4B9CC7232FCT"]
  accountEnabledForKycV2: []
  firstYearFreeStartDate: 16/08/2020
  firstYearFreeEndDate: 30/09/2020
  accountsEnabledForStoreMergeExp: []
  storeMergeEnabled: false
  cbcVkycEnabled: true
  emiUserList: [ "ACC14269179088889030","ACC4BBD119E3A394E2D99818EB471201D53A" ]
  whitelistIdForOfflineCustomers: 110
  accountsEnabledForEtccAlternateCug: ["ACC128501F10D4E49E1A4211D1B5FC8F4D6B","ACC14098205345153250","ACC14020001643342490"]
  accountsEnabledForTransactionSummary: []
  externalWhitelistIds : [111,112,135]
  incomeBinThreshold: 474.1
  cbcHyperlinkPhoneEnabled: false
  checkApplyNowVisibilty: true
  cbcNewRejectPageEnabled: false
  ntbCugUserList: ["ACC39537669976E4CDF95F265AC514076EF6"]
  cbcNtbRevampValue: true
  ntbCbcWhitelistIds: [ "66" ]
  etccCbcWhitelistIds: [ "42" ]
  etbpaCbcWhitelistIds: [ "136" ]
  etbnpaCbcWhitelistIds: [ "137" ]
  ntbSupercoinWhitelistIds: [ "142" ]
  etccSupercoinWhitelistIds: [ "143" ]
  etbpaSupercoinWhitelistIds: [ "144" ]
  etbnpaSupercoinWhitelistIds: [ "145" ]
  emiBillingEnabled: true
  applicationRetryDuration: 500
  applicationSyncCutoffDate: "2022-12-22 20:00:00"

collectionsClientConfiguration:
  scheme: http
  host: ************
  port: 7980

contextWiseWhitelistConfig:
  contextMap:
    NUDGING-EFA-INDIA_BULLS:
      isEnabledForEveryone: true
      whitelistedAccountIds: ["ACC798D3CC5AC4447869C1B1DD645A7128AZ"]
    NUDGING-CBC-AXIS:
          isEnabledForEveryone: true
          whitelistedAccountIds: []
    NUDGING-CBC_SC_ELITE-AXIS:
      isEnabledForEveryone: true
      whitelistedAccountIds: []
    NPS-CBC-AXIS:
          isEnabledForEveryone: true
          whitelistedAccountIds: ["ACC1A75EB1E55584AE89225A4B9CC7232FCT1"]
    NUDGING-FLIPKART_ADVANZ-IDFC:
        isEnabledForEveryone: false
        whitelistedAccountIds: []

contextWiseCacheBuilderConfig:
  contextMap:
    PAN_RETRY:
      maximumSize: 10000
      duration: 30
      durationUnit: DAYS

schedulerClientConfig:
  host: ***********
  port: 80
  poolSize: 20
  clientId: fintech_cf

heliosProxyConfig:
  maxConnections: 1024
  maxConnectionsPerHost: 1024
  pooledConnectionIdleTimeoutInMS: 6000
  executorThreadsCount: 32
  executionIsolationSemaphoreMaxConcurrentRequests: 1024
  executionTimeoutInMS: 10000
  hystrixCommandKey: PinakaServiceHystrixCommand
  hystrixGroupKey: PinakaServiceHystrixGroup
  rewriteProtocal: http
  rewriteHostName: 127.0.0.1
  rewritePortNumber: 80
  enableProxy: false


fldgConfiguration:
  enableFldg: false

abConfiguration:
  endpoint: ***********
  layerList: "Affordability"
  clientId: "Mapi.affordability"
  testAccounts: ["ACC14125653191935118","ACC2DEDB3BC47054F818FAA8E680072E011R","ACC13751787976165289","ACCFGSS5XIRIFCRNJN4ESU8U0C16CYFD","ACC4C5070D22EBD4F26A017B71167444CFCW","ACC8B2427BE8D354EAF9D94046347FDA13ES","ACC3BC9610ECF294B9399033BCA62E50D49T","ACC3A7D2C09FBFF4C8CB551019C061301586","ACC209807A5FBBD42A6866DD717E9F63190V","ACC2DEDB3BC47054F818FAA8E680072E011R","ACXQ0MEGDT4TMQQK2L6MKVNC1EF4Z5LG","ACCF152B52D9AD444E0B7BFF9781CF15672U","ACCE73189B3192B406AA7A5EF89A31CAC51M","ACCACC54415130E4D5196C215FF591E6C28Q","ACC22419809054D4F0FB0AA22890A1A99E0C","ACC123","ACC1","ACCCBB9FF24B9B849CB9A9F221C22767BDCF","ACC1379260720753381","ACC0","ACC13503955483876275"]
  unlayeredAbEnabled: true
  clientSecretKey: fintech-pinaka-prod-ee4b7347a0ee409f88cccb70d54238c1

kycConfig:
  xmlKycAndroidAppVersion: 1080100
  xmlKycChannels: ["ANDROID"]
  activeKycModes: ["EKYC","AADHAAR_XML_EKYC"]
  createNewKycApplicationForOldUserEnabled: false
  defaultMethodForTestAccount: CKYC

onboardingConfig:
  upgradeJourneyLimitReductionAllowed: false
  genericFlowEnabled: true
  whitelistChangeEntityEnabled: false
  genericFlowAccountIds: ["ACC8B2427BE8D354EAF9D94046347FDA13ES","ACC798D3CC5AC4447869C1B1DD645A7128AZ"]
  plusWhitelistId: 136


alfredClientConfig:
  url: "http://************"
  secretKey: "123rewedrgrvcz"

neoCrmClientConfig:
  exchangeName: "fintech_cbc_production"
  authToken: "LVNKY7IAABXXAAZB"
  clientId: "Fintech"
  url: "http://***********"
  path: "/v1/crm/publish"

uiConfiguration:
  payLaterCohortPriorityList: ["CASH","PAY_LATER","EMI","FSUP"]
  fsupCohortPriorityList: ["CASH","FSUP","PAY_LATER","EMI"]
  cohorts:
    CASH:
      name: "Cash Loan"
      full_description: "For withdrawal upto "
    EMI:
      name: "EMIs"
      full_description: "For purchases upto ?%s"
    PAY_LATER:
      name: "Pay Later"
      full_description: "Pay next month for shopping upto ?%s"
      minimized_description: "Flipkart Pay Later enabled"
      logo_url: "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/09/08/2021/2f5f8386-099c-4350-9d98-de485532c49e.png?q={@quality}"
    FSUP:
      name: "Smart Upgrade"
      full_description: "Get your phone by paying just a fraction of amount upfront"
      minimized_description: "Flipkart Smart Upgrade enabled"
      logo_url: "https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/20/08/2021/76e1349c-dc49-4cd7-bc04-c4aa36b2a035.png?q={@quality}"
  uiConfigMap:
    APPROVED:
      title: "Congratulations"
      nonUpgradedTitle: "We are sorry!"
      nonUpgradedSubtitle : "Thank you for your interest in Flipkart Paylater. However, with the present information you've given, we are unable to increase your credit limit for Flipkart Paylater. Please continue shopping to your heart's content on Flipkart. We'll let you know when we are able to increase your credit limit. "
      emiSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost, or pay in easy EMIs of upto 12 months"
      payLaterSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost. Continue using Flipkart Pay Later and unlock more features in future"
      imageUrl: ""
      nonUpgradedImageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"
      logoUrl: "https://rukminim1.flixcart.com/www/568/112/promos/24/09/2020/7db826c0-8301-45fb-8b04-a787a6ac294d.png?q=100"
      knowMoreText: "Learn more about Pay Later"
      knowMoreLandingUrl: "https://www.flipkart.com/pages/paylater-main-tnc"
      payLaterSubmitButtonText: "Go to Home Page"
      payLaterSubmitButtonUrl: "https://www.flipkart.com/"
      fsupSubmitButtonText: "Back to Product Page"
      creditInfoTitle: "Your Benefits"
      creditInfoLateFeeText: "Your bill will be generated on 1st of every month. Due date will be the 5th of that month. After 5th, Late Fee will be applied."
      howToUseInfo:
        title: "How to use Flipkart Pay Later"
        stepsList:
          -
            description: "Choose Flipkart Pay Later or Flipkart Pay Later (EMI) when you land on Payments option page"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/59f18654-e0d6-4440-a955-13a33268689f.png"
            title: "STEP 1"
          -
            description: "Place the order and view your transactions in Flipkart Pay Later dashboard on My Account"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/8f627fd4-8d21-4d78-a9f5-b7f9c54e341d.png"
            title: "STEP 2"
          -
            description: "Pay your bills by 5th of every month to avoid any late fee charges"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/dad56c4e-6a89-40b2-b028-f088273acc27.png"
            title: "STEP 3"
      taglinePayLater: "You can now shop using Flipkart Pay Later, and pay next month. Continue using Flipkart Pay Later and unlock more features in future."
      taglinePartialKyc: "You can now shop using Flipkart Pay Later, and pay next month. To unlock more features, verify your bank account now."
    LOAN_CREATION_IN_PROGRESS:
      title: "Congratulations"
      nonUpgradedTitle: "We are sorry!"
      nonUpgradedSubtitle: "Thank you for your interest in EMI’s. However, with the present information you’ve given, we are unable to activate Flipkart EMI’s for you. Please continue using Pay Later and we will soon let you know when we can activate EMI’s for you!"
      emiSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost, or pay in easy EMIs of up-to 12 months"
      payLaterSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost. Continue using Flipkart Pay Later and unlock more features in future"
      imageUrl: ""
      nonUpgradedImageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"
      logoUrl: "https://rukminim1.flixcart.com/www/568/112/promos/17/03/2020/********-619b-44d2-9933-e6c6039ef351.png"
      knowMoreText: "knowMoreText"
      knowMoreLandingUrl: "https://www.flipkart.com/pages/advanz-service-tnc"
      payLaterSubmitButtonText: "Continue Shopping"
      payLaterSubmitButtonUrl: "www.flipkart.com"
      fsupSubmitButtonText: "Back to product page"
      creditInfoTitle: "Your Benefits"
      creditInfoLateFeeText: "Your bill will be generated on 1st of every month. Due date will be the 10th of that month. After 10th, Late Fee will be applied."
      howToUseInfo:
        title: "How to use Flipkart Pay Later"
        stepsList:
          - description: "Choose Flipkart Pay Later or Flipkart Pay Later (EMI) when you land on Payments option page"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/59f18654-e0d6-4440-a955-13a33268689f.png"
            title: "STEP 1"
          - description: "Place the order and view your transactions in Flipkart Pay Later dashboard on My Account"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/8f627fd4-8d21-4d78-a9f5-b7f9c54e341d.png"
            title: "STEP 2"
          - description: "Pay your bills by 10th of every month to avoid any late fee charges"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/dad56c4e-6a89-40b2-b028-f088273acc27.png"
            title: "STEP 3"
      taglinePayLater: "You can now shop using Flipkart Pay Later, and pay next month. Continue using Flipkart Pay Later and unlock more features in future."
      taglinePartialKyc: "You can now shop using Flipkart Pay Later, and pay next month. To unlock more features, verify your bank account now."
    POST_PROCESSING_PENDING:
      title: "Congratulations"
      nonUpgradedTitle: "We are sorry!"
      nonUpgradedSubtitle: "Thank you for your interest in EMI’s. However, with the present information you’ve given, we are unable to activate Flipkart EMI’s for you. Please continue using Pay Later and we will soon let you know when we can activate EMI’s for you!"
      emiSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost, or pay in easy EMIs of up-to 12 months"
      payLaterSubtitle: "You can now shop using Flipkart Pay Later, and pay next month at no extra cost. Continue using Flipkart Pay Later and unlock more features in future"
      imageUrl: ""
      nonUpgradedImageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"
      logoUrl: "https://rukminim1.flixcart.com/www/568/112/promos/17/03/2020/********-619b-44d2-9933-e6c6039ef351.png"
      knowMoreText: "knowMoreText"
      knowMoreLandingUrl: "https://www.flipkart.com/pages/advanz-service-tnc"
      payLaterSubmitButtonText: "Continue Shopping"
      payLaterSubmitButtonUrl: "www.flipkart.com"
      fsupSubmitButtonText: "Back to product page"
      creditInfoTitle: "Your Benefits"
      creditInfoLateFeeText: "Your bill will be generated on 1st of every month. Due date will be the 10th of that month. After 10th, Late Fee will be applied."
      howToUseInfo:
        title: "How to use Flipkart Pay Later"
        stepsList:
          - description: "Choose Flipkart Pay Later or Flipkart Pay Later (EMI) when you land on Payments option page"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/59f18654-e0d6-4440-a955-13a33268689f.png"
            title: "STEP 1"
          - description: "Place the order and view your transactions in Flipkart Pay Later dashboard on My Account"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/8f627fd4-8d21-4d78-a9f5-b7f9c54e341d.png"
            title: "STEP 2"
          - description: "Pay your bills by 10th of every month to avoid any late fee charges"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/dad56c4e-6a89-40b2-b028-f088273acc27.png"
            title: "STEP 3"
      taglinePayLater: "You can now shop using Flipkart Pay Later, and pay next month. Continue using Flipkart Pay Later and unlock more features in future."
      taglinePartialKyc: "You can now shop using Flipkart Pay Later, and pay next month. To unlock more features, verify your bank account now."
    CONDITIONALLY_APPROVED:
      title: "Congratulations"
      nonUpgradedTitle: "Congratulations"
      nonUpgradedSubtitle : "Based on your details, here are some exciting benefits for you!"
      emiSubtitle: "Based on your details, here are some exciting benefits for you!"
      payLaterSubtitle: "Based on your details, here are some exciting benefits for you!"
      imageUrl: ""
      nonUpgradedImageUrl: ""
      logoUrl: "https://rukminim1.flixcart.com/www/568/112/promos/24/09/2020/7db826c0-8301-45fb-8b04-a787a6ac294d.png?q=100"
      knowMoreText: "Learn more about Pay Later"
      knowMoreLandingUrl: "https://www.flipkart.com/pages/paylater-main-tnc"
      payLaterSubmitButtonText: "Go to Home Page"
      payLaterSubmitButtonUrl: "https://www.flipkart.com/"
      creditInfoTitle: "Your Benefits"
      creditInfoLateFeeText: "Your bill will be generated on 1st of every month. Due date will be the 5th of that month. After 5th, Late Fee will be applied."
      howToUseInfo:
        title: "How to use Flipkart Pay Later"
        stepsList:
          -
            description: "Choose Flipkart Pay Later or Flipkart Pay Later (EMI) when you land on Payments option page"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/59f18654-e0d6-4440-a955-13a33268689f.png"
            title: "STEP 1"
          -
            description: "Place the order and view your transactions in Flipkart Pay Later dashboard on My Account"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/8f627fd4-8d21-4d78-a9f5-b7f9c54e341d.png"
            title: "STEP 2"
          -
            description: "Pay your bills by 5th of every month to avoid any late fee charges"
            imageUrl: "https://rukminim1.flixcart.com/www/224/224/promos/17/03/2020/dad56c4e-6a89-40b2-b028-f088273acc27.png"
            title: "STEP 3"
    REJECTED:
      title: "Thank you for your interest in %s"
      subtitle: "Due to our financial partner policies, we are not able to provide a credit line at this moment"
      payLaterSubmitButtonText: "Go to Home Page"
      payLaterSubmitButtonUrl: "https://www.flipkart.com/"
      fsupSubmitButtonText: "Back to Product Page"
      imageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"
      nonUpgradedTitle: "We are sorry!"
      nonUpgradedImageUrl: "https://rukminim1.flixcart.com/www/212/220/promos/17/03/2020/9df6d1ec-6aa9-4152-a2ef-9bf7cd08c283.png"
      nonUpgradedSubtitle : "Thank you for your interest in Flipkart Paylater. However, with the present information you've given, we are unable to increase your credit limit for Flipkart Paylater. Please continue shopping to your heart's content on Flipkart. We'll let you know when we are able to increase your credit limit. "



mysqlLockConfig:
  prefix: ""
  timeout: 0

dataPointConfiguration:
  enableDataPoint:
    TS:
      enable: true
      testAccountList: ["ACC8B2427BE8D354EAF9D94046347FDA13ES"]

redisConfig:
  masterName: mymaster
  timeout: 5000
  sentinelAddresses: ["redis://***********:26379","redis://***********:26379","redis://***********:26379"]
  password: "password"
  masterConnectionMinimumIdleSize: 3
  masterConnectionPoolSize: 100
  lockWaitTime: 10
  lockReleaseTime: 3

sourceAttributionConfig:
  enabled: false
  redisTtlInSeconds: 43200

rotation:
  enableRotator: true
  defaultRotationStatus: true

winterfellClientConfig:
  host: http://winterfell-service-next.sm-winterfell-prod.fkcloud.in/fintech-winterfell
  clientId: pinaka
  connectionTimeout: 30000
  readTimeout: 30000

citadelClientConfig:
  host: http://citadel-service-next.sm-citadel-prod.fkcloud.in
  clientId: pinaka
  connectionTimeout: 60000
  readTimeout: 60000
  merchantId: flipkart

lockinClientConfig:
  url: http://************
  clientId: Fintech

robinhoodAsyncCbcConfig:
  url: http://*************
  exchangeName: "cbc_robinhood_queue"

robinhoodAsyncPlConfig:
  url: http://***********
  exchangeName: "pl_robinhood_topic"

robinhoodCfaClientConfig:
  url: http://*************
  exchangeName: "cfa_onboarding_queue"

coreLogisticsClientConfig:
  url: http://**********
  referer: "http://www.fintech.com"
  key: "79a271ce-b870-c03b-cdbb-a52861a90ea9"

pandoraAsyncClientConfig:
  url: http://pandora-service.sm-pandora-playground.fkcloud.in/pandora
  exchangeName: fintech_wintefell_chronos_calback_preprod
  exchangeType: queue


ffbConfig:
  url: http://***********/v1/ffb/event/process
  endPoint: /
  exchangeName : fintech_wintefell_chronos_calback_preprod
  environment: PREPROD
  businessToLenderDocumentTypeMapping:
    BUSINESS_PROOF: BUSINESS_PROOF
    SHOP1: OTHER_1
    SHOP2: OTHER_2
    SHOP3: OTHER_3

kafkaConsumerConfiguration:
  enableConsumer: false
  kafkaConsumerProps:
    AMS:
      bootStrapServers: 10.32.11.89:9092,10.33.183.253:9092,10.34.109.213:9092
      groupId: ams-app-pinaka-preprod-group
      sessionTimeoutMs: 30000
      noOfThreads: 3
      topic: fintech_los_ffb_topic
      pollTimeOut: 100
      enableDlq: false
      dlqTopicName: fintech_los_ffb_topic_dlq
    CALM:
      bootStrapServers: 10.32.11.89:9092,10.33.183.253:9092,10.34.109.213:9092 #these don't exist
      groupId: calm-pinaka-preprod-group
      sessionTimeoutMs: 30000
      noOfThreads: 1
      topic: fintech_calm_topic
      pollTimeOut: 100
      enableDlq: true
      dlqTopicName: fintech_calm_topic_dlq

encryptionKeys:
  kycKey: eyJkYXRhIjoidmF1bHQ6djE6aTJPemo0TjZoVUpSdEpGbjNQWk5KU2NvL1lORGJhS1BUWXVyV2hLeDdZU0lkTlZIMlQ1alpSQ0RuV0lBOXRjSk9PU3JsU2ZaWEMrbGlUR2wiLCJrZXlJZCI6ImNmZy1zdmM6OmluLWh5ZGVyYWJhZC0xLXByZXByb2Q6cGluYWthLXByZXByb2QifQ==
  cbcKey: 59AC6C2B95DEFC3EC76C56CF232AF829
  default: 59AC6C2B95DEFC3EC76C56CF232AF829

securityKeyConfiguration:
  keys:
    keyRef0:
      privateKey: ${REF0_PRIVATE_KEY}
      publicKey: ${REF0_PUBLIC_KEY}
    keyRef1:
      privateKey: ${REF1_PRIVATE_KEY}
      publicKey: ${REF1_PUBLIC_KEY}

pinakaAsyncClientConfig:
  url: http://************
  exchangeName: advanz_onboarding_queue
  ebcExchangeName: production.pandora.ebc.callback
  changeEntityEventTopic: fintech.change.entity
  cfaExchangeName: cfa_onboarding_queue
  advanzDedupeExchangeName: efa_onboarding_2
  cbcCardSerNoExchangeName: cbc_card_ser_queue
  cbcCardSerNoHostUrl: http://***********:9090
  cbcCardSerNoPath: /pinaka/5/applications/process-application

plutusClientConfig:
  url: http://***********:80

fintechUserServiceClientConfig:
  uslUrl: http://***********
  exchangeName: fintech_usl_ingestion_queue

caishenClientConfig:
  url: http://*************
  targetClientId: caishen_flipkart

khaataAsyncClientConfig:
  url: ************
  exchangeName: queue

skylerAsyncClientConfig:
  url: hhtp://localhost:11000
  exchangeName: cbc_bill_initiation_queue

fkPayClientConfig:
  url: http://***********
  clientId: Fintech
  clientSecret: ypg37lwetecaj96pih96
  encryptionKey: FINTECH_AES_KEYS

stratumD42Configuration:
  accessKey: GOOG1E42KDDZR4BDJO4RXCEMLX2RMZTTE7EUXMDNW4ENC2VJSLNFXPYKBLZU6
  secretKey: 9qDg6wG5wW0s/BuznjennwsoxUL0vnpi2qg0ZYow
  endPoint: https://storage.googleapis.com
  maxConnections: 10
  connectionTimeout: 2000

outboundBundleConfiguration:
  defaultDaysToKeep: 15
  shardNames: ["default","queues"]
  databaseName: pinaka

uslClientConfig:
  host: http://***********
  clientId: pinaka
  connectionTimeout: 2000
  readTimeout: 2000

consentClientConfig:
  host: http://***********
  clientId: pinaka
  connectionTimeout: 2000
  readTimeout: 2000

consentConfig:
  consentVersionMap:
    KYC-PAGE-CONSENT-V0: 1677721157835
    EXPERIAN-BUREAU-PULL-CONSENT-V0: 1677721190710
    DEVICE-DETAILS-UPLOAD-CONSENT-V0: 1677721215208
  consentGroups:
    ONBOARDING_CONSENTS:
      - KYC-PAGE-CONSENT-V0
      - EXPERIAN-BUREAU-PULL-CONSENT-V0
      - DEVICE-DETAILS-UPLOAD-CONSENT-V0

audienceManagerConfiguration:
    kafkaProducerConfiguration:
        bootstrapServer: *************:9092,************:9092,************:9092,*************:9092
        acks: 1
        retries: 3
        batchSize: 20
        lingerInMilliSecond: 5
        maxInFlightRequestsPerConnection: 1
    realtimeSegmentConfiguration:
        enabled: true
        testAccounts: []
        topicName: perf_rtsm_tagger
        enabledJourneyList: ["LOAN_APPLICATION_JOURNEY"]
    redisTtlInSeconds: 7776000

financialProviderConfig:
  kycMethodConfig:
    AADHAAR_XML_EKYC:
      metadata:
        IDFC:
            tncUrl: https://www.flipkart.com/rv/fintech/khaata/terms-v2
            tollFreeNo: 1860 500 9900
            email: <EMAIL>
            xmlKycTncUrl: https://www.flipkart.com/rv/fintech/khaata/terms-v2
            xmlKycDisclaimer: Alternatively, you can submit your KYC documents at the nearest IDFC FIRST Bank for verification.
            reviewAndSubmitDisclaimer:
            reviewAndSubmitTncUrl: https://www.flipkart.com/rv/fintech/khaata/terms-v2
            reviewAndSubmitTncText: Terms & Conditions
            consentContext: XML-KYC-PAGE-CONSENT-V0
            kycType: UPGRADABLE
      validation:
          IDFC:
            nameMatchThreshold: 50
            nameValidationEnabled: true
            panValidationRequired: true
      validityInYears: 10
      validityExtensionPeriod : 9
      supportedAndroidAppVersion: 1080100
      supportedChannelList: ["ANDROID"]
    EKYC:
      metadata:
        IDFC:
          consentContext: KYC-PAGE-CONSENT-V0
          kycType: PARTIAL
      validation:
        IDFC:
          nameMatchThreshold: 50
          nameValidationEnabled: true
          panValidationRequired: true
      validityInYears: 1
      validityExtensionPeriod : 0
    CKYC:
      metadata:
        IDFC:
          tncUrl: https://www.flipkart.com/rv/fintech/khaata/terms-v2
          tollFreeNo: 1860 500 9900
          email: <EMAIL>
          disclaimer:
          reviewAndSubmitDisclaimer:
          reviewAndSubmitTncUrl: https://www.flipkart.com/rv/fintech/khaata/terms-v2
          reviewAndSubmitTncText: Terms & Conditions
          consentContext: /idfcCkycConsentContext
          kycType: UPGRADABLE
          incorrectDetailsPopupHeading: Complete KYC using another method
          incorrectDetailsPopupText: Since you noticed incorrect information - we recommend you to restart KYC using another method.
          incorrectDetailsPopupImageUrl: https://rukminim1.flixcart.com/www/{@width}/{@height}/promos/22/05/2020/dceac1c1-435e-42b1-87f9-a99abb2a43c8.png?q={@quality}
          incorrectDetailsPopupButtonText: Continue to complete KYC
      validation:
        IDFC:
          nameMatchThreshold: 50
          nameValidationEnabled: true
          panValidationRequired: true
      validityInYears: -1
      validityExtensionPeriod : 0
      fallbackMethods: ["AADHAAR_XML_EKYC","EKYC"]

ffbProxyConfiguration:
  maxConnections: 1024
  maxConnectionsPerHost: 1024
  pooledConnectionIdleTimeoutInMS: 6000
  executorThreadsCount: 32
  executionIsolationSemaphoreMaxConcurrentRequests: 1024
  executionTimeoutInMS: 10000
  hystrixCommandKey: PinakaServiceHystrixCommand
  hystrixGroupKey: PinakaServiceHystrixGroup
  rewriteProtocal: http
  rewriteHostName: ************
  rewritePortNumber: 80
  enableProxy: false

orchestratorClientConfig:
  url: http://**********:80

hystrixModuleConfiguration:
  hystrixPropertiesFileName: hystrix.properties
  resourcesFilePath: /etc/config/

multiTenantOAuthClientConfig:
  cachedAccessTokenTTL: 120
  oAuthUrl: http://************:80
  oAuthServiceClientConfigMap:
    FK_CONSUMER_CREDIT:
      oAuthClientID: c2c95e0075c04b2e9b83e1bc8a09f57e
      oAuthClientSecret: FVMWGlJ22AoWfQ+ithjYn+M0tjr8xgakuHl06J5A+DELtVtc
      cachedAccessTokenTTL: 120
      oAuthUrl: http://************:80

multiTenantConnektClientConfig:
  tenantConnektClientConfigMap:
    FK_CONSUMER_CREDIT:
      exchangeName: fintech_los_connekt
      callbackUrl: http://************/pinaka/communications
      domain: flipkart
      emailUrl: http://************
      pnUrl: http://************
      smsUrl: http://************
      inAppUrl: http://************
      emailApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
      pnApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
      smsApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
      inAppApiKey: Je8t73sq5ECkkrB6tSErMXbFKyCiaB5ip77WH3ydEoWcKpEK
      emailAppName: flipkart
      pnAppName: RetailApp
      smsAppName: flipkart
      inAppName: flipkart
      emailBucket: alerts
      emailSubBucket: pay
      inAppBucket: alerts
      inAppSubBucket: pay
      pnBucket: alerts
      pnSubBucket: pay
      smsBucket: alerts
      smsSubBucket: pay
      pnChannelId: fk_channel_order_payments
      transactionalEmail: true
      transactionalPN: true
      transactionalSMS: true
      transactionalInApp: true

turboConfig:
  singleDbWriteEnabled: false
  multiDbWriteEnabled: true
  turboOutboundWithoutTrxEnabled: false
  sharding: true
  appDbType: "mysql"
  queue_shard_strategy:
    default_suffix: default
    clusters:
      queues:
        - advanz_onboarding_queue
        - fintech_los_connekt
        - fintech_cf_scheduler
        - lms_onboarding_queue
        - hawkeye_dedupe_ingestion

  mysql:
    hibernate.dialect: org.hibernate.dialect.MySQL57Dialect
    hibernate.c3p0.idle_test_period: "60000"
    hibernate.c3p0.max_size: "100"
    hibernate.c3p0.max_statements: "50"
    hibernate.c3p0.min_size: "20"
    hibernate.c3p0.timeout: "300"
    hibernate.connection.driver_class: com.mysql.jdbc.Driver
    hibernate.connection.password: pinaka
    hibernate.connection.url: ******************************************************************************
    hibernate.connection.username: pinaka
    hibernate.connection.autocommit: true
    hibernate.show_sql: "true"
    hibernate.connection.zeroDateTimeBehavior: convertToNull

gibraltarClientConfig:
  # TODO: SM: THIS IS PROD ENDPOINT FOR TESTING, UPDATE BEFORE MERGE
  host: https://************
  keySpaceName: onboarding
  clientName: efa-onboarding-client
  # TODO: SM: THIS IS PROD ENDPOINT FOR TESTING, UPDATE BEFORE MERGE
  targetClientId: gibraltar-prod
  maxPublicKeyCount: 100

criClientConfig:
  host: http://***********
  maxRetries: 3

coinManagerClientConfig:
  url: http://***********
  clientId: "CBC"
  merchantId: "MERCH220307165327503I1YWG"
  requestId: "ABCD"

esClientConfig:
  hostName: "sm-es-prod-elasticsearch.sm-es-prod.fkcloud.in"
  port: 80
  connectionTimeout : 3000

khaataClientConfig:
  url: http://************:8980
  clientName: "pinaka"

skylerClientConfig:
  url: http://***********:80/fintech-skyler/
  clientId: "pinaka"
  exchangeName: "cbc_bill_initiation_queue"

hawkeyeAsyncConfig:
  client: FK_CONSUMER_CREDIT
  exchangeName: hawkeye_dedupe_ingestion
  url: http://************:8080/cf-events/v1/identity-aggregation

viestiConfig:
  enableConsumer: false
  viestiConsumerProps:
    CALM:
      pulsarClientConfig:
#        Using prod consumer client since preprod isn't activated
        authnSecret: 6mzlqJnCzxp1X4Z+jrHTEUAsJxFBNfmO8YQFgB+B6eNGd01m
        authnClientId: sm_pl_ams_consumer
        issuerUrl: https://service.authn-prod.fkcloud.in
        viestiEndPoint: http://************:80
      pulsarConsumerConfiguration:
        topic: persistent://prod-yak-archive/prod_supermoney_los/prod_sm_los_calm
        subscriptionName: supermoney-engg/sm_ams_consumer_preprod
        numberOfConsumers: 1

fintechLogConfig:
  serviceTag: pinaka
  bucketNames: ["pinaka-preprod"]

smUserServiceClientConfig:
  url: "http://usersvc-kaas-v1.sm-user-service-prod.fkcloud.in"
  clientId: "supermoney"


merchantJourneyUrlConfig:
  FLIPKART:
    statusUrl: "https://www.flipkart.com/sumo-3p/pl/pages/status"
    callbackUrl: "https://dl.flipkart.com/sumo-3p/pl/pages/status"
    resumeUrl: "https://dl.flipkart.com/sumo-3p/pl/pages/journey"
  SHOPSY:
    statusUrl: "https://www.shopsy.in/sm-3p/pl/pages/status"
    callbackUrl: "https://dl.shopsy.in/sm-3p/pl/pages/status"
    resumeUrl: "https://dl.shopsy.in/sm-3p/pl/pages/journey"
  MYNTRA:
    statusUrl: "https://super.money/sm-3p/pl/pages/status"
    callbackUrl: "https://www.myntra.com/sumo-3p/pl/pages/status"
    resumeUrl: "https://www.myntra.com/sumo-3p/pl/pages/journey"

crisysClientConfig:
  host: http://crisys-service.sm-crisys-playground.fkcloud.in

offerServiceConfig:
  url: http://sm-offer-service.sm-offer-service-playground.fkcloud.in

pinakaCalvinClientConfig:
  url: http://***********
  client : pinaka

pageServiceConfig:
  isWinterfellMocked: false
  dataAPIPath: "com.flipkart.sm.pages.dp.apis"
  widgetAdapterPath: "com.flipkart.sm.pages.dp.widget.adapters"
  pageServiceStaticResourcesBucket:
    bucketName: "sm-pinaka-preprod"
    enableLocalDynamicBucket: false
  featureFlagConfigBucket:
    bucketName: "sm-feature-flag-preprod"
    enableLocalDynamicBucket: false
  pageConfigResolverName: calm
  pageConfigPollingInterval: 5
  widgetTemplateMap:
    # cardSummaryList widgets
    ACCOUNTS_CARD_IDENTIFIER__ACTIVE: "template/creditscore/ActiveAccountsRichCard.json"
    ACCOUNTS_CARD_IDENTIFIER__INACTIVE: "template/creditscore/InactiveAccountsRichCard.json"
    ON_TIME_PAYMENT_CREDIT_METRIC_CARD_SUMMARY_LIST_IDENTIFIER: "template/creditscore/OnTimePaymentsCreditMetricCard.json"
    CREDIT_UTILIZATION_CREDIT_METRIC_CARD_SUMMARY_LIST_IDENTIFIER: "template/creditscore/CreditUtilisationCreditMetricCard.json"
    # card widgets
    CREDIT_SCORE_CARD_IDENTIFIER__ON_TIME_PAYMENTS: "template/creditscore/OnTimePaymentsCreditScoreCard.json"
    CREDIT_SCORE_CARD_IDENTIFIER__CREDIT_UTILISATION: "template/creditscore/CreditUtilisationCreditScoreCard.json"
    CREDIT_SCORE_CARD_IDENTIFIER__CREDIT_AGE: "template/creditscore/CreditAgeCreditScoreCard.json"
    CREDIT_SCORE_CARD_IDENTIFIER__CREDIT_MIX: "template/creditscore/CreditMixCreditScoreCard.json"
    CREDIT_SCORE_CARD_IDENTIFIER__CREDIT_ENQUIRES: "template/creditscore/CreditEnquireCreditScoreCard.json"
    # key value widgets
    CREDIT_METRIC_KEY_VALUE: "template/creditscore/CreditMetricImpactKeyValue.json"
    CREDIT_SCORE_TABLE: "template/creditscore/CreditScoreTable.json"
    CHECK_CREDIT_SCORE_BUTTON: template/creditscore/CheckCreditScoreSubmitButton.json
    EXPERIAN_FOOTER: template/creditscore/ExperianFooter.json
    CHECK_CREDIT_SCORE_BANNER: template/creditscore/CheckCreditScoreBanner.json
    CHECK_CREDIT_SCORE_PL_BANNER: template/creditscore/CheckCreditScorePlBanner.json
    CREDIT_SCORE_METRICS_TITLE: template/creditscore/CreditScoreMetricsHeader.json
    CREDIT_SCORE_METRICS_CARDS: template/creditscore/CreditScoreMetrics.json
    IMPROVE_CREDIT_SCORE: template/creditscore/HowToImproveCSButton.json
    CS_REFRESH_BUTTON: template/creditscore/RefreshCreditScore.json
    CREDIT_SCORE_SCALE: template/creditscore/CreditScoreScale.json
    NO_CS_CARD: template/creditscore/NoCSCard.json
    NO_CS_BUTTON: template/creditscore/NoCSButton.json
    NO_CS_TIPS_LIST: template/creditscore/NoCSTips.json
    NAV_BAR_BACK_BUTTON: template/creditscore/BackNavBar.json
    CS_FROM_PARTNER_CARD: template/creditscore/CreditScoreFromPartner.json
    SUPERCASH_NAV_BAR: template/creditscore/SupercashNavBar.json
    SUPERCASH_NAV_BAR_WITH_BACK: template/creditscore/SupercashNavBarWithBack.json
    EXPERIAN_TERMS_AND_CONDITIONS: template/creditscore/TnC.json
    EXPERIAN_TnC_HEADER: template/creditscore/TncHeader.json
    POWERED_BY_UPI_FOOTER: template/creditscore/PoweredByUpiFooter.json
    NAV_COMING_SOON_CASH: template/creditscore/ComingSoonNavBar.json
    CASH_COMING_SOON_CARD: template/creditscore/ComingSoonCard.json

  widgetConfigMap:
    PAGE_TITLE_TEXT: "widgetConfig/PageTitle.json"


bigtableConfig:
  projects:
    - projectId: "sm-personal-loans"
      instances:
        - instanceId: "sumo-bigtable-preprod-1"
          tables:
            credit-report:
              tableId: "creditReport"

