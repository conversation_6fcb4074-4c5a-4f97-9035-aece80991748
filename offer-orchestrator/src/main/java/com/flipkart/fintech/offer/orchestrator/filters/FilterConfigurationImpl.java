package com.flipkart.fintech.offer.orchestrator.filters;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.offer.orchestrator.model.filter.LenderFilter;
import com.flipkart.fintech.offer.orchestrator.model.filter.LenderFilterDetail;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.google.inject.Inject;
import lombok.CustomLog;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@CustomLog
public class FilterConfigurationImpl implements FilterConfiguration{

    private List<LenderFilter> configuration;
    @Inject
    public FilterConfigurationImpl() throws IOException {
    try{
        String configFile = "FILTER_CONFIGURATION.json";
        ObjectMapper objectMapper = new ObjectMapper();
        InputStream inputStream = this.getClass().getClassLoader().getResourceAsStream(configFile);
        configuration = objectMapper.readValue(inputStream, new TypeReference<List<LenderFilter>>() {});
    } catch (IOException e) {
        log.error(String.format("Error reading configuration from file %s", e));
    }
    }


    @Override
    public List<String> getEnabledFilters(Lender lender) {
        LenderFilter lenderFilters = configuration.stream()
                .filter(f -> f.getLender().equals(lender))
                .findFirst().orElse(null); // not throwing exception to ease onboarding lender with no filters
        if(lenderFilters == null) return new ArrayList<>();
        return lenderFilters.getFilters().stream()
                .filter(f -> f.getEnabled().equals(true))
                .map(LenderFilterDetail::getName)
                .collect(Collectors.toList());
    }

    @Override
    public LenderFilterDetail getConfiguration(Lender lender, Class<? extends LenderBasedFilter> lenderBasedFilter) {
        LenderFilter lenderFilters = configuration.stream()
                .filter(f -> f.getLender().equals(lender))
                .findFirst().orElse(null); // not throwing exception to ease onboarding a lender with no filters
        if(lenderFilters == null) return null;
        return lenderFilters.getFilters().stream()
                .filter(filterDetail -> filterDetail.getName().equals(getFilterName(lenderBasedFilter))).findFirst().orElse(null);
    }

    private static String getFilterName(Class<? extends LenderBasedFilter> filterClass) {
        FilterName annotation = filterClass.getAnnotation(FilterName.class);
        return annotation != null ? annotation.value() : null;
    }

}
