package com.flipkart.fintech.offer.orchestrator;

import com.flipkart.fintech.pinaka.common.decrypter.Decrypter;
import com.google.inject.Inject;

import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;

public class Utility {

    private final Decrypter decrypter;

    @Inject
    public Utility(Decrypter decrypter) {
        this.decrypter = decrypter;
    }

    public String decryptString(String encryptedString){
        return decrypter.decryptString(encryptedString);
    }

    public int calculateAge(String dob){
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        LocalDate parsedDob = LocalDate.parse(dob, formatter);
        LocalDate currentDate = LocalDate.now();
        Period period = Period.between(parsedDob, currentDate);
        return period.getYears();
    }

}
