package com.flipkart.fintech.offer.orchestrator.filters;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.flipkart.fintech.offer.orchestrator.dao.LenderPincodeServiceabilityDao;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.offer.orchestrator.model.LenderPincodeServiceabilityEntity;
import com.flipkart.fintech.offer.orchestrator.model.UserProfileCohortEntity;
import com.flipkart.fintech.offer.orchestrator.model.filter.LenderFilterDetail;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.google.inject.Inject;
import lombok.CustomLog;
import lombok.Data;

import java.util.List;
import java.util.Objects;


@CustomLog
public class PinCodeServiceabilityFilterImpl implements PinCodeServiceabilityFilter {


    private static final ObjectMapper mapper = new ObjectMapper();
    private static final String SHIPPING_PINCODE = "shippingPincode";
    private static final String USER_ENTERED_PINCODE = "userEnteredPincode";
    private final FilterConfiguration filterConfiguration;
    private final LenderPincodeServiceabilityDao lenderPincodeServiceabilityDao;

    @Inject
    public PinCodeServiceabilityFilterImpl(FilterConfiguration filterConfiguration, LenderPincodeServiceabilityDao lenderPincodeServiceabilityDao) {
        this.filterConfiguration = filterConfiguration;
        this.lenderPincodeServiceabilityDao = lenderPincodeServiceabilityDao;
    }

    private static PinCodeFilterDetails parse(String json) {
        try {
            return mapper.readValue(json, PinCodeFilterDetails.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Boolean filter(LenderOfferEntity lenderOfferEntity, UserProfileCohortEntity userCohort,
                          ProfileDetailedResponse userProfile, MerchantUser merchantUser) {
        LenderFilterDetail filterDetail = filterConfiguration.getConfiguration(
                lenderOfferEntity.getLender(),
                PinCodeServiceabilityFilter.class
        );
        if (filterDetail == null || !filterDetail.getEnabled()) return true;
        PinCodeFilterDetails pinCodeFilterDetails = parse(filterDetail.getDescriptor());
        return evaluateServiceability(pinCodeFilterDetails, userProfile, lenderOfferEntity);
    }

    private Integer getPincode(ProfileDetailedResponse profileDetailedResponse, String pincodeType) {
        if (Objects.equals(pincodeType, SHIPPING_PINCODE)) return profileDetailedResponse.getShippingPincode();
        if (Objects.equals(pincodeType, USER_ENTERED_PINCODE)) return profileDetailedResponse.getUserEnteredPincode();
        return null;
    }

    private boolean evaluateServiceability(PinCodeFilterDetails pinCodeFilterDetails, ProfileDetailedResponse userProfile, LenderOfferEntity lenderOfferEntity) {
        for (String pincodeType : pinCodeFilterDetails.getPincodeTypes()) {
            Integer pin = getPincode(userProfile, pincodeType);
            if (pin == null) continue;
            LenderPincodeServiceabilityEntity lenderPincodeServiceabilityEntity = lenderPincodeServiceabilityDao.getActive(lenderOfferEntity.getLender(), String.valueOf(pin));
            if (lenderPincodeServiceabilityEntity != null) return true;
        }
        return false;
    }

    @Data
    @JsonIgnoreProperties(ignoreUnknown = true)
    static
    class PinCodeFilterDetails {
        private List<String> pincodeTypes;
    }
}
