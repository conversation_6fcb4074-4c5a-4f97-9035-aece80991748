package com.flipkart.fintech.offer.orchestrator.filters;

import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.offer.orchestrator.model.UserProfileCohortEntity;
import com.flipkart.fintech.offer.orchestrator.model.filter.LenderFilterDetail;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.profile.model.EmploymentType;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.google.inject.Inject;

public class EmploymentFilterImpl implements EmploymentFilter {

    private final FilterConfiguration filterConfiguration;

    @Inject
    public EmploymentFilterImpl(FilterConfiguration filterConfiguration) {
        this.filterConfiguration = filterConfiguration;
    }

    @Override
    public Boolean filter(LenderOfferEntity lenderOfferEntity, UserProfileCohortEntity userCohort,
                          ProfileDetailedResponse userProfile, MerchantUser merchantUser) {
        LenderFilterDetail filterDetail = filterConfiguration.getConfiguration(
                lenderOfferEntity.getLender(),
                EmploymentFilter.class
        );
        if (filterDetail == null || !filterDetail.getEnabled()) return true;
        return userProfile.getEmploymentType() == EmploymentType.Salaried;
    }
}
