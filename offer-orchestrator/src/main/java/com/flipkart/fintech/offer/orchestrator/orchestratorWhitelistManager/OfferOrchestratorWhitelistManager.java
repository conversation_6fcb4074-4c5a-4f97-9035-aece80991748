package com.flipkart.fintech.offer.orchestrator.orchestratorWhitelistManager;

import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.google.inject.ImplementedBy;

import java.util.Optional;

@ImplementedBy(OfferOrchestratorWhitelistManagerImpl.class)
public interface OfferOrchestratorWhitelistManager {

    public Optional<LenderOfferEntity> getWhitelistedOffer(String userAccountId);

}
