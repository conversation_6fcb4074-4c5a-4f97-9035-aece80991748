package com.flipkart.fintech.offer.orchestrator.filters;

import com.flipkart.ams.ApplicationService;
import com.flipkart.fintech.citadel.api.models.ApplicationStateResponse;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferType;
import com.flipkart.fintech.offer.orchestrator.model.UserProfileCohortEntity;
import com.flipkart.fintech.offer.orchestrator.model.response.FilterServiceFilterResponse;
import com.flipkart.fintech.offer.orchestrator.model.responsemetadata.FilterServiceFilterMetadata;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.pinaka.service.exception.PinakaException;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.google.inject.Inject;
import lombok.CustomLog;
import lombok.Getter;
import lombok.Setter;

import java.util.*;
import java.util.concurrent.*;

@CustomLog
public class FilterServiceImpl implements FilterService {

    private final FilterConfiguration filterConfiguration;

    private final PinCodeServiceabilityFilter pinCodeServiceabilityFilter;
    private final AgeFilter ageFilter;
    private final DuplicateApplicationFilter duplicateApplicationFilter;
    private final EmploymentFilter employmentFilter;
    private final ApplicationService applicationService;
    private final String PinCodeServiceabilityFilterName = "PinCodeServiceabilityFilter";
    private final String DuplicateApplicationFilterName = "DuplicateApplicationFilter";
    private final String EmploymentFilterName = "EmploymentFilter";
    private final String AgeFilterName = "AgeFilter";
    private final String ApplicationCoolOffFilterName = "ApplicationCoolOffFilter";
    private final int THREAD_COUNT = 100;
    ExecutorService executor = Executors.newFixedThreadPool(THREAD_COUNT);

    @Inject
    public FilterServiceImpl(FilterConfiguration filterConfiguration, PinCodeServiceabilityFilter pinCodeServiceabilityFilter, AgeFilter ageFilter, DuplicateApplicationFilter duplicateApplicationFilter, EmploymentFilter employmentFilter,
                             ApplicationService applicationService) {
        this.filterConfiguration = filterConfiguration;
        this.pinCodeServiceabilityFilter = pinCodeServiceabilityFilter;
        this.ageFilter = ageFilter;
        this.duplicateApplicationFilter = duplicateApplicationFilter;
        this.employmentFilter = employmentFilter;
        this.applicationService = applicationService;
    }

    @Override
    public List<String> getFilters(List<LenderOfferEntity> offers) {
        Set<String> distinctFilters = new HashSet<>();
        for (LenderOfferEntity offer : offers) {
            List<String> activeFilters = filterConfiguration.getEnabledFilters(offer.getLender());
            distinctFilters.addAll(activeFilters);
        }
        return new ArrayList<>(distinctFilters);
    }

    @Override
    public FilterServiceFilterResponse filter(List<LenderOfferEntity> offers, List<String> eligibleFilters,
                                              UserProfileCohortEntity userProfileCohort,
                                              ProfileDetailedResponse profileDetailedResponse, MerchantUser merchantUser) {
        List<LenderOfferEntity> filteredOffers = new ArrayList<>();
        FilterServiceFilterResponse response = new FilterServiceFilterResponse();
        FilterServiceFilterMetadata metadata = new FilterServiceFilterMetadata();
        HashMap<String, String> rejectedOfferReason = new HashMap<>();
        for (LenderOfferEntity offer : offers) {
            boolean offerPassesAllFilters = true;
            for (String filter : eligibleFilters) {
                if (filter.equals(DuplicateApplicationFilterName)) continue;
                boolean offerPassesFilter = getFilter(filter, merchantUser).filter(offer, userProfileCohort,
                        profileDetailedResponse, merchantUser);

                if (!offerPassesFilter) {
                    rejectedOfferReason.put(offer.getId(), filter);
                    offerPassesAllFilters = false;
                    break;
                }
            }
            if (offerPassesAllFilters) {
                filteredOffers.add(offer);
            }
        }
        if (eligibleFilters.contains(DuplicateApplicationFilterName)) {
            try {
                FilterNonDuplicateOffersResponse filterNonDuplicateOffersResponse = filterNonDuplicateOffers(filteredOffers, userProfileCohort, profileDetailedResponse, merchantUser);
                filteredOffers = filterNonDuplicateOffersResponse.filteredOffers;
                for (String offerId : filterNonDuplicateOffersResponse.rejectedOfferIds) {
                    rejectedOfferReason.put(offerId, DuplicateApplicationFilterName);
                }
            } catch (Exception e) {
                log.error(String.format("FAILED_DEDUPE_FILTER due to %s", e.getMessage()));
            }
        }
        // remove OM offers if any pre-approved
        boolean hasAnyPreApprovedOffer = filteredOffers.stream().anyMatch(offer -> offer.getOfferType() == LenderOfferType.PRE_APPROVED);
        // create new non-lender filter for the following
        if (hasAnyPreApprovedOffer)
            filteredOffers.removeIf(offer -> offer.getOfferType() == LenderOfferType.OPEN_MARKET);
        metadata.setRejectedOfferReason(rejectedOfferReason);
        response.setOffers(filteredOffers);
        response.setFilterServiceFilterMetadata(metadata);
        return response;
    }

    private FilterNonDuplicateOffersResponse filterNonDuplicateOffers(List<LenderOfferEntity> offers,
                                                                      UserProfileCohortEntity userProfileCohort,
                                                                      ProfileDetailedResponse profileDetailedResponse,
                                                                      MerchantUser merchantUser) {
        FilterNonDuplicateOffersResponse filterNonDuplicateOffersResponse = new FilterNonDuplicateOffersResponse();
        List<String> rejectedOfferIds = new ArrayList<>();
        List<LenderOfferEntity> filteredOffers = new ArrayList<>();
        filterNonDuplicateOffersResponse.setFilteredOffers(filteredOffers);
        filterNonDuplicateOffersResponse.setRejectedOfferIds(rejectedOfferIds);
        List<Callable<Boolean>> tasks = new ArrayList<>();
        for (LenderOfferEntity offer : offers) {
            LenderOfferEntity finalOffer = offer;
            Callable<Boolean> task = () -> duplicateApplicationFilter.filter(finalOffer, userProfileCohort,
                    profileDetailedResponse, merchantUser);
            tasks.add(task);
        }
        try {
            List<Future<Boolean>> futures = executor.invokeAll(tasks, 3, TimeUnit.SECONDS);
            for (int i = 0; i < futures.size(); i++) {
                Future<Boolean> future = futures.get(i);
                LenderOfferEntity currentOffer = offers.get(i);
                try {
                    boolean result = future.get();
                    if (result) {
                        filteredOffers.add(currentOffer);
                    } else {
                        rejectedOfferIds.add(currentOffer.getId());
                    }
                } catch (InterruptedException | ExecutionException | CancellationException | NullPointerException e) {
                    log.error(String.format("DuplicateApplicationFilter:: Failed to check dedupe for %s due to %s", currentOffer.getLender(), e));
                    filteredOffers.add(currentOffer);  // not blocking
                }
            }
        } catch (Exception e) {
            log.error(String.format("DuplicateApplicationFilter:: Failed to check dedupe for any lender %s", e));
            filterNonDuplicateOffersResponse.setFilteredOffers(offers);  // not blocking
            filterNonDuplicateOffersResponse.setRejectedOfferIds(new ArrayList<>());
            return filterNonDuplicateOffersResponse;
        }
        return filterNonDuplicateOffersResponse;
    }


    private LenderBasedFilter getFilter(String filterName, MerchantUser merchantUser) { // refactor: move to common component for filter svc and filter configuration
        if (Objects.equals(filterName, PinCodeServiceabilityFilterName)) return pinCodeServiceabilityFilter;
        if (Objects.equals(filterName, DuplicateApplicationFilterName)) return duplicateApplicationFilter;
        if (Objects.equals(filterName, EmploymentFilterName)) return employmentFilter;
        if (Objects.equals(filterName, AgeFilterName)) return ageFilter;
        if (Objects.equals(filterName, ApplicationCoolOffFilterName)) {
            ApplicationStateResponse discardedApplications = getDiscardedApplications(merchantUser);
            return new ApplicationCoolOffFilter(filterConfiguration, discardedApplications);
        }
        return null;
    }


    private ApplicationStateResponse getDiscardedApplications(MerchantUser merchantUser) {
        try {
            return applicationService.getDiscardedApplications(merchantUser);
        } catch (PinakaException e) {
            throw new RuntimeException(e);
        }
    }

    @Setter
    @Getter
    private static class FilterNonDuplicateOffersResponse {
        List<LenderOfferEntity> filteredOffers;
        List<String> rejectedOfferIds = new ArrayList<>();
    }

}
