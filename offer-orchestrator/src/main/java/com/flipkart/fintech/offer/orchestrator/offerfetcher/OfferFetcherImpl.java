package com.flipkart.fintech.offer.orchestrator.offerfetcher;

import com.flipkart.fintech.offer.orchestrator.dao.temp.BorrowerEntityDaoNew;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferState;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferType;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.enums.ProductType;
import com.flipkart.fintech.pinaka.api.model.LeadDetails;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.google.inject.Inject;
import lombok.CustomLog;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@CustomLog
public class OfferFetcherImpl implements OfferFetcher {

    private final BorrowerEntityDaoNew borrowerEntityDao;
    private final List<Lender> eligibleOpenMarketLenders = Arrays.asList(Lender.AXIS, Lender.FIBE, Lender.MONEYVIEWOPENMKT, Lender.DMI);

    @Inject
    public OfferFetcherImpl(BorrowerEntityDaoNew borrowerEntityDao) {
        this.borrowerEntityDao = borrowerEntityDao;
    }

    @Override
    public List<LenderOfferEntity> getOffersForLead(LeadDetails lead, MerchantUser merchantUser) {
        List<LenderOfferEntity> offers = new ArrayList<>();
        // get preApprovedOffers
        List<LenderOfferEntity> preApprovedOffers = borrowerEntityDao.getActiveBorrowers(lead.getUserProfile().getMerchantUserId(),
                        merchantUser.getMerchantKey(), ProductType.PERSONAL_LOAN).stream()
                .map(LenderOfferEntity::new)
                .collect(Collectors.toList());
        // get openMarketOffers
        List<LenderOfferEntity> openMarketOffers = getEligibleOpenMarketOffers(preApprovedOffers, lead);
        offers.addAll(preApprovedOffers);
        offers.addAll(openMarketOffers);
        return offers;
    }

    private List<LenderOfferEntity> getEligibleOpenMarketOffers(List<LenderOfferEntity> preApprovedOffers, LeadDetails lead) {
//        later decide open market eligibility by cohort etc
        List<LenderOfferEntity> openMarketOffers = new ArrayList<>();
        for (Lender lender : eligibleOpenMarketLenders) {
            if (preApprovedOffers.stream().anyMatch(offer -> offer.getLender() == lender)) continue;
            LenderOfferEntity offerEntity = new LenderOfferEntity(lead.getUserProfile().getMerchantUserId(),
                    lead.getUserProfile().getProfileId().toString(), lender, LenderOfferState.ACTIVE, LenderOfferType.OPEN_MARKET);
            openMarketOffers.add(offerEntity);
        }
        return openMarketOffers;
    }
}