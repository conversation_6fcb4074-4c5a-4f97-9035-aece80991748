package com.flipkart.fintech.offer.orchestrator.dao;

import com.flipkart.fintech.offer.orchestrator.model.LenderPincodeServiceabilityEntity;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import io.dropwizard.hibernate.AbstractDAO;
import lombok.CustomLog;
import org.hibernate.Criteria;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.criterion.Restrictions;

import java.util.List;

@CustomLog
public class LenderPincodeServiceabilityDaoImpl extends AbstractDAO<LenderPincodeServiceabilityEntity> implements LenderPincodeServiceabilityDao {
    public LenderPincodeServiceabilityDaoImpl(SessionFactory sessionFactory) {
        super(sessionFactory);
    }
    @Override
    public List<LenderPincodeServiceabilityEntity> getAllActive(String pincode) {
        return null;
    }

    @Override
    public LenderPincodeServiceabilityEntity getActive(Lender lender, String pincode) {
        Criteria criteria = criteria();
        criteria.add(Restrictions.eq("lender", lender));
        criteria.add(Restrictions.eq("pincode", pincode));
        criteria.add(Restrictions.eq("active", true));
        return list(criteria).stream().findFirst().orElse(null);
    }
}
