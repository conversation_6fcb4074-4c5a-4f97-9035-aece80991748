package com.flipkart.fintech.offer.orchestrator.filters;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.flipkart.ams.ApplicationTypeUtils;
import com.flipkart.fintech.citadel.api.models.ApplicationPivotsResponse;
import com.flipkart.fintech.citadel.api.models.ApplicationStateResponse;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.offer.orchestrator.model.UserProfileCohortEntity;
import com.flipkart.fintech.offer.orchestrator.model.filter.LenderFilterDetail;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.pinaka.api.model.MerchantUser;
import com.flipkart.fintech.profile.response.ProfileDetailedResponse;
import com.supermoney.ams.bridge.models.LifecycleConfig;
import com.supermoney.ams.bridge.utils.ObjectMapperUtil;
import com.supermoney.ams.bridge.utils.TimeUtil;
import javax.validation.constraints.NotNull;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@FilterName("ApplicationCoolOffFilter")
public class ApplicationCoolOffFilter implements LenderBasedFilter {

    private final FilterConfiguration filterConfiguration;

    private final ApplicationStateResponse applicationStateResponse;

    public ApplicationCoolOffFilter(FilterConfiguration filterConfiguration,
                                    ApplicationStateResponse applicationStateResponse) {
        this.filterConfiguration = filterConfiguration;
        this.applicationStateResponse = applicationStateResponse;
    }

    private static LifecycleConfig parse(String json) {
        try {
            return ObjectMapperUtil.get().readValue(json, LifecycleConfig.class);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    private static boolean hasCooledOff(ApplicationPivotsResponse response, LifecycleConfig lifecycleConfig) {
        Date createdAt = response.getCreatedAt();
        Date coolOffDate = TimeUtil.addTime(createdAt, lifecycleConfig);
        Date currentDate = new Date();
        return coolOffDate.before(currentDate);
    }

    @Override
    public Boolean filter(LenderOfferEntity lenderOfferEntity, UserProfileCohortEntity userCohort,
                          ProfileDetailedResponse userProfile, MerchantUser merchantUser) {
        Lender lender = lenderOfferEntity.getLender();
        LenderFilterDetail filterDetail = filterConfiguration.getConfiguration(lender,
                ApplicationCoolOffFilter.class
        );
        if (filterDetail == null || !filterDetail.getEnabled()) return true;
        LifecycleConfig lifecycleConfig = parse(filterDetail.getDescriptor());
        return hasCooledOff(lender, lifecycleConfig);
    }

    @NotNull
    private Boolean hasCooledOff(Lender lender, LifecycleConfig lifecycleConfig) {
        List<ApplicationPivotsResponse> applicationsList = getApplicationsList();
        return applicationsList.stream()
                .filter(e -> ApplicationTypeUtils.isSameLender(e.getApplicationType(), lender))
                .map(e -> hasCooledOff(e, lifecycleConfig))
                .reduce(Boolean::logicalAnd)
                .orElse(true);
    }

    private List<ApplicationPivotsResponse> getApplicationsList() {
        List<ApplicationPivotsResponse> applicationsList = applicationStateResponse.getApplicationsList();
        if (applicationsList == null)
            applicationsList = new ArrayList<>();
        return applicationsList;
    }

}
