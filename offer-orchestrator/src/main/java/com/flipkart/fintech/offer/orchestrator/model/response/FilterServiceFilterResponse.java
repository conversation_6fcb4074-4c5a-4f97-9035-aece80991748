package com.flipkart.fintech.offer.orchestrator.model.response;


import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.offer.orchestrator.model.responsemetadata.FilterServiceFilterMetadata;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Setter
@Getter
public class FilterServiceFilterResponse {
    List<LenderOfferEntity> offers;
    FilterServiceFilterMetadata filterServiceFilterMetadata;
}


