package com.flipkart.fintech.offer.orchestrator;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.flipkart.fintech.offer.orchestrator.model.*;
import com.flipkart.fintech.pinaka.api.enums.Lender;
import com.flipkart.fintech.offer.orchestrator.model.UserProfileCohortEntity;
import lombok.Data;

import java.util.List;
import java.util.Optional;


@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class OfferEvaluator {
    private String id;
    private String userProfileCohortId;
    private Double weight;
    private String lenders;
    private OfferEvaluatorState state;
    private Double version;
    private String created_by;

    public LenderOfferEntity evaluate(List<LenderOfferEntity> offers, UserProfileCohortEntity profileCohort) {
        String[] lenderNames = lenders.split(",");
        for (String lenderName : lenderNames) {
            String lender = lenderName.trim();
            Optional<LenderOfferEntity> matchingOffer = offers.stream()
                    .filter(offer -> offer.getLender().equals(Lender.valueOf(lender)))
                    .findAny();
            if (matchingOffer.isPresent()) {
                return matchingOffer.get();
            }
        }
        return null;
    }

    public OfferEvaluator(OfferEvaluatorEntity evaluatorEntity) {
        this.userProfileCohortId = evaluatorEntity.getUserProfileCohortId();
        this.weight = evaluatorEntity.getWeight();
        this.lenders = evaluatorEntity.getLenders();
        this.state = evaluatorEntity.getState();
        this.version = evaluatorEntity.getVersion();
        this.created_by = evaluatorEntity.getCreated_by();
    }

}