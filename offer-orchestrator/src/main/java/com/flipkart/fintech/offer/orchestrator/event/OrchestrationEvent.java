package com.flipkart.fintech.offer.orchestrator.event;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Setter
@Getter
public class OrchestrationEvent {

    @JsonProperty("lead_id")
    String leadId;
    @JsonProperty("user_id")
    String userId;
    @JsonProperty("profile_id")
    String profileId;
    @JsonProperty("merchant")
    String merchant;
    @JsonProperty("metadata")
    List<HashMap<String, String>> metadata;
    @JsonProperty("user_cohort_id")
    String userCohortId;
    @JsonProperty("all_offers")
    List<Offer> allOffers;
    @JsonProperty("filter_rejected_offers")
    List<Offer> filterRejectedOffers;
    @JsonProperty("filtered_offers")
    List<Offer> filteredOffers;
    @JsonProperty("evaluator_id")
    String evaluatorId;
    @JsonProperty("final_offer")
    Offer finalOffer;
    @JsonProperty("final_offer_reason_code")
    String finalOfferReasonCode;

    public void setAllOffers(List<LenderOfferEntity> offers) {
        if(offers == null){
            this.allOffers = null;
            return;
        }
        this.allOffers = offers.stream()
                .map(Offer::new)
                .collect(Collectors.toList());
    }

    public void setFilteredOffers(List<LenderOfferEntity> offers) {
        if(offers == null){
            this.filteredOffers = null;
            return;
        }
        this.filteredOffers = offers.stream()
                .map(Offer::new)
                .collect(Collectors.toList());
    }

    public void setFilterRejectedOffers(HashMap<String, String> filterRejectedOffers, List<LenderOfferEntity> allOffers) {
        if(allOffers == null || filterRejectedOffers == null){
            this.filterRejectedOffers = null;
            return;
        }
        List<Offer> offers = new ArrayList<>();
        for (HashMap.Entry<String, String> entry : filterRejectedOffers.entrySet()) {
            String offerId = entry.getKey();
            String rejectionReason = entry.getValue();
            LenderOfferEntity lenderOffer = allOffers.stream()
                    .filter(it -> it.getId().equals(offerId))
                    .findAny()
                    .orElse(null);
            Offer offer = new Offer(offerId, rejectionReason, lenderOffer.getLender()); // lenderOffer should never be null
            offers.add(offer);
        }
        this.filterRejectedOffers = offers;
    }

    public void setFinalOffer(LenderOfferEntity offer, String reason) {
        this.finalOffer = offer != null ? new Offer(offer) : null;
        this.finalOfferReasonCode = reason;
    }

    public void addMetadata(String key, Object value) {
        if (this.metadata == null) {
            this.metadata = new ArrayList<>();
        }
        HashMap<String, String> map = new HashMap<>();
        map.put("key", key);
        map.put("value", value != null ? value.toString() : null);
        this.metadata.add(map);
    }


}
