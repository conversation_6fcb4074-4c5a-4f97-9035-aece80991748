package offerorchestrator;

import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.offer.orchestrator.offerDistributor.OfferDistributorImpl;
import com.flipkart.fintech.offer.orchestrator.model.UserProfileCohortEntity;
import org.junit.Before;
import org.junit.Test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static offerorchestrator.LenderOfferEntityDaoTest.getOffersForUserProfile_MultipleOffers1;

public class EvaluatorTest {

    private OfferDistributorImpl offerDistributorImpl;

    @Before
    public void setUp() {
//        offerDistributorImpl = new OfferDistributorImpl(new FileBasedOfferEvaluatorDao());

    }

    @Test
    public void testEvaluator() {
        Map<String, Integer> countMap = new HashMap<>();
        List<LenderOfferEntity> offers = getOffersForUserProfile_MultipleOffers1("profile1")
                ;
        UserProfileCohortEntity cohort = new UserProfileCohortEntity(
                1L,
                "PL_V0_COHORT_2",
                true,
                "v0"
        );
//        for (int i = 0; i < 1000000; i++) {
//            OfferEvaluatorEntity entity = offerDistributorImpl.getActiveEvaluator(offers, cohort);
//            String lender = entity.getLenders();
//            countMap.put(lender, countMap.getOrDefault(lender, 0) + 1);
//        }
//        for (Map.Entry<String, Integer> entry : countMap.entrySet()) {
//            System.out.println("Lender: " + entry.getKey() + ", Count: " + entry.getValue());
//        }
    }

}
