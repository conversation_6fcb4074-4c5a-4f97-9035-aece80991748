package offerorchestrator;

import com.flipkart.fintech.offer.orchestrator.model.LenderOfferEntity;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferState;
import com.flipkart.fintech.offer.orchestrator.model.LenderOfferType;
import com.flipkart.fintech.pinaka.api.enums.Lender;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class LenderOfferEntityDaoTest {

    private static final List<LenderOfferEntity> offers = new ArrayList<>();

    static {
        // Multiple offers for userProfileId "user1" and "profile1"
        offers.add(new LenderOfferEntity("user1", "profile1", Lender.AXIS, LenderOfferState.ACTIVE, LenderOfferType.PRE_APPROVED));
        offers.add(new LenderOfferEntity("user1", "profile1", Lender.FIBE, LenderOfferState.INACTIVE, LenderOfferType.PRE_APPROVED));
        offers.add(new LenderOfferEntity("user1", "profile1", Lender.IDFC, LenderOfferState.INACTIVE, LenderOfferType.PRE_APPROVED));
        offers.add(new LenderOfferEntity("user1", "profile1", Lender.MONEYVIEW, LenderOfferState.ACTIVE, LenderOfferType.PRE_APPROVED));

        // Multiple offers for userProfileId "user1" and "profile2"
        offers.add(new LenderOfferEntity("user1", "profile2", Lender.AXIS, LenderOfferState.ACTIVE, LenderOfferType.PRE_APPROVED));
        offers.add(new LenderOfferEntity("user1", "profile2", Lender.IDFC, LenderOfferState.INACTIVE, LenderOfferType.PRE_APPROVED));
        offers.add(new LenderOfferEntity("user1", "profile2", Lender.MONEYVIEW, LenderOfferState.ACTIVE, LenderOfferType.PRE_APPROVED));

        // Multiple offers for userProfileId "user2" and "profile3"
        offers.add(new LenderOfferEntity("user2", "profile3", Lender.AXIS, LenderOfferState.ACTIVE, LenderOfferType.PRE_APPROVED));
        offers.add(new LenderOfferEntity("user2", "profile3", Lender.IDFC, LenderOfferState.INACTIVE, LenderOfferType.PRE_APPROVED));

        // Single offer for userProfileId "user3" and "profile4"
        offers.add(new LenderOfferEntity("user3", "profile4", Lender.FIBE, LenderOfferState.ACTIVE, LenderOfferType.PRE_APPROVED));
    }

    public static List<LenderOfferEntity> getOffersForUserProfile_MultipleOffers1(String userProfileId) {
        return offers.stream()
                .filter(offer -> offer.getUserProfileId().equals(userProfileId))
                .collect(Collectors.toList());
    }

    public static List<LenderOfferEntity> getOffersForUserProfile_MultipleOffers2(String userProfileId) {
        return offers.stream()
                .filter(offer -> offer.getUserProfileId().equals(userProfileId))
                .filter(offer -> offer.getStatus() == LenderOfferState.ACTIVE)
                .collect(Collectors.toList());
    }

    public static List<LenderOfferEntity> getOffersForUserProfile_NoOffers(String userProfileId) {
        return Collections.emptyList();
    }

    public static List<LenderOfferEntity> getOffersForUserProfile_SingleOffer(String userProfileId) {
        return offers.stream()
                .filter(offer -> offer.getUserProfileId().equals(userProfileId))
                .limit(1) // Limit to one offer
                .collect(Collectors.toList());
    }
}
